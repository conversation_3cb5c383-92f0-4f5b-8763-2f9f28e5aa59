# 3D Scanner Mobile App - Project Architecture

## 1. Introduction

This document outlines the architectural design and key technical considerations for the 3D Scanner Mobile App, targeting the Samsung S25 Ultra. The application aims to provide advanced 3D scanning capabilities utilizing the device's ToF sensor and camera, real-time processing, and flexible model export options.

## 2. Application Configuration

### 2.1. Target Device

*   **Device:** Samsung S25 Ultra

### 2.2. Android SDK Configuration

*   **Minimum SDK Version:** Android 14
*   **Target SDK Version:** Android 14

### 2.3. Required Permissions

The application will require the following Android permissions to function correctly:

*   `android.permission.CAMERA`: For accessing the device's camera for 4K video capture.
*   `android.permission.WRITE_EXTERNAL_STORAGE` (or equivalent for Android 14+): For local storage of scanned 3D models and related data. Note: For Android 10 (API level 29) and higher, scoped storage is enforced, so `WRITE_EXTERNAL_STORAGE` might not be directly applicable for app-specific directories. We will use `MediaStore` or app-specific directories for storage.
*   `android.permission.READ_EXTERNAL_STORAGE` (or equivalent for Android 14+): For reading previously saved 3D models.
*   `android.permission.SENSORS`: For accessing device sensors, particularly the ToF sensor for depth sensing.
*   `android.permission.ACCESS_FINE_LOCATION` / `android.permission.ACCESS_COARSE_LOCATION`: While not explicitly stated for scanning, location might be used for geotagging scans or for ARCore functionalities that sometimes leverage location for better tracking. If not strictly necessary, this permission will be omitted to reduce permission overhead.

## 3. Core Features Architecture

### 3.1. Scanning Module

The scanning module is the core of the application, responsible for capturing depth and visual data, and processing it into 3D models.

#### 3.1.1. Depth Sensing

*   **Type:** Time-of-Flight (ToF) Sensor
*   **Resolution:** VGA
*   **Frame Rate:** 30 FPS

#### 3.1.2. Camera

*   **Resolution:** 4K
*   **Frame Rate:** 60 FPS
*   **Auto Focus:** Enabled
*   **Stabilization:** Enabled

#### 3.1.3. Processing Pipeline

The processing pipeline will handle the conversion of raw sensor data into a usable 3D model. This will involve several stages:

*   **Point Cloud Generation:** Raw depth data from the ToF sensor will be converted into a 3D point cloud.
*   **Mesh Generation:** The point cloud will be triangulated to form a 3D mesh.
*   **Texture Mapping:** Visual data from the 4K camera will be mapped onto the generated mesh to provide realistic textures.
*   **Real-Time Preview:** A real-time preview of the scanning process and the evolving 3D model will be displayed to the user.

### 3.2. Model Export Module

This module will handle the export of generated 3D models into various standard formats.

*   **Supported Formats:** OBJ, STL, PLY, GLTF
*   **Compression:** Enabled for efficient storage and transfer.
*   **Quality:** Adjustable by the user, allowing for trade-offs between file size and model fidelity.

## 4. User Interface (UI) Architecture

The UI will be designed to be intuitive and user-friendly, providing distinct views for scanning, model management, and gallery browsing.

### 4.1. Screens

*   **ScanningView:**
    *   `cameraPreview`: Displays the live camera feed.
    *   `depthView`: Overlays or displays the depth information from the ToF sensor.
    *   `scanProgress`: Indicates the progress of the 3D scan.
    *   `controlButtons`: Buttons for starting, pausing, stopping, and configuring scans.

*   **ModelView:**
    *   `3dViewer`: Renders the generated 3D model, allowing for rotation, zooming, and panning.
    *   `editTools`: Provides tools for basic model editing (e.g., cropping, smoothing).
    *   `exportOptions`: Allows users to select export format, quality, and initiate export.

*   **GalleryView:**
    *   `scanList`: Displays a list of previously scanned 3D models.
    *   `filterOptions`: Allows users to filter and sort scans.
    *   `shareButtons`: Provides options to share models via various platforms.

## 5. Data Management Architecture

### 5.1. Storage

*   **Local Storage:**
    *   **Format:** Binary for efficiency.
    *   **Compression:** Enabled.
    *   **Maximum Size:** 1GB (app-specific data, can be expanded if needed).
*   **Cloud Storage:**
    *   **Enabled:** Yes, for backup and multi-device access.
    *   **Auto-Sync:** Enabled for seamless data synchronization.
    *   **Encryption:** Enabled for data security.

### 5.2. Database

*   **Type:** SQLite (embedded database for local data management).
*   **Tables:**
    *   `scans`: Stores metadata about each 3D scan (e.g., name, date, size, format).
    *   `settings`: Stores application settings.
    *   `userPreferences`: Stores user-specific preferences.

## 6. Performance Optimization Architecture

To ensure a smooth and responsive user experience, especially during real-time 3D processing, several optimization techniques will be employed.

*   **Mesh Simplification:** Reduces the polygon count of 3D models without significant loss of visual quality.
*   **Texture Compression:** Optimizes texture data for faster loading and reduced memory footprint.
*   **GPU Acceleration:** Leverages the device's GPU for computationally intensive tasks like mesh generation and rendering.

### 6.1. Memory Management

*   **Maximum RAM Usage:** 2GB (target, will be monitored and optimized).
*   **Cache Size:** 500MB (for frequently accessed data).

## 7. Security Architecture

*   **Data Encryption:** All sensitive data, especially cloud-synced data, will be encrypted.
*   **Authentication:**
    *   **Required:** Yes.
    *   **Methods:** Biometric (fingerprint/face unlock) and PIN.

## 8. Dependencies

The following key libraries and frameworks will be integrated:

*   **OpenCV:** For image processing tasks, camera calibration, and potentially some initial point cloud processing.
*   **OpenGL ES 3.0:** For efficient 3D rendering and real-time preview.
*   **ARCore:** For advanced motion tracking, environmental understanding, and potentially improved depth sensing capabilities (even with a ToF sensor, ARCore can provide additional benefits).
*   **TensorFlow Lite:** For on-device machine learning tasks, potentially for object recognition during scanning, or advanced mesh refinement.

## 9. Overall Architecture Pattern

The application will likely follow a **Model-View-ViewModel (MVVM)** architectural pattern. This pattern promotes separation of concerns, testability, and maintainability.

*   **Model:** Represents the data and business logic (e.g., 3D model data, database interactions, scanning logic).
*   **View:** The UI elements (Activities, Fragments, custom views) that display the data.
*   **ViewModel:** Acts as a bridge between the Model and the View, exposing data streams and handling UI-related logic, abstracting the data source from the view.

## 10. Future Considerations

*   **Cross-platform compatibility:** While initially targeting Android, future versions could explore cross-platform frameworks like Flutter or React Native for iOS support.
*   **Advanced Editing Tools:** More sophisticated 3D model editing capabilities.
*   **Cloud Processing:** Offloading heavy 3D processing to cloud servers for faster results and reduced on-device resource consumption.
*   **AI-powered features:** Enhanced object recognition, scene understanding, and automated model cleanup using advanced AI models.





## 11. Technology Stack

### 11.1. Programming Language

*   **Kotlin:** Modern, concise, and safe programming language officially recommended by Google for Android development. It offers excellent interoperability with existing Java libraries and frameworks.

### 11.2. Android Jetpack Components

*   **Lifecycle:** Manages activity and fragment lifecycles, preventing common errors like memory leaks.
*   **LiveData:** Observable data holder that is lifecycle-aware, ensuring UI updates only when components are in an active state.
*   **ViewModel:** Stores and manages UI-related data in a lifecycle-conscious way, surviving configuration changes.
*   **Room Persistence Library:** An abstraction layer over SQLite, providing a more robust way to access the database.
*   **Navigation Component:** Simplifies the implementation of navigation within the app, including fragment transactions and deep linking.
*   **CameraX:** A Jetpack library that simplifies camera app development, providing a consistent and easy-to-use API for camera functionalities, including advanced features like HDR and Portrait mode, which can be beneficial for capturing high-quality textures.

### 11.3. 3D Graphics and Processing Libraries

*   **OpenGL ES 3.0:** As specified, this will be used for rendering 3D models and real-time previews. We will utilize Android's `GLSurfaceView` for integration.
*   **ARCore SDK:** For augmented reality capabilities, motion tracking, and environmental understanding. ARCore can provide robust pose estimation and spatial mapping, which can complement the ToF sensor data for more accurate 3D reconstruction.
*   **OpenCV for Android:** A powerful computer vision library that will be used for image processing tasks such as camera calibration, noise reduction in depth maps, and potentially feature detection for aligning camera frames with depth data.
*   **TensorFlow Lite:** For on-device machine learning. This can be leveraged for advanced features like semantic segmentation (identifying objects in the scene to improve scanning accuracy), object recognition, or even for optimizing mesh generation through learned models.

### 11.4. Model Export Libraries

*   **Assimp (Open Asset Import Library):** While primarily a C++ library, it can be integrated into Android projects via JNI (Java Native Interface) to handle the loading and saving of various 3D model formats (OBJ, STL, PLY, GLTF). This provides a robust and widely supported solution for model import/export.
*   **Custom Implementations:** For simpler formats or specific requirements, custom parsers/writers might be developed to ensure optimal performance and control over the export process.

### 11.5. Cloud Integration

*   **Firebase (or similar BaaS):** For cloud storage (Firebase Storage), user authentication (Firebase Authentication), and potentially real-time database (Firestore) for metadata synchronization. Firebase offers robust, scalable, and secure solutions for mobile backend services.

### 11.6. Dependency Management

*   **Gradle:** The build automation system used by Android Studio. Dependencies will be managed through `build.gradle` files.





## 12. SDK Versions and Permissions Planning

As per the project requirements, the application will target Android 14. This implies specific considerations for SDK versions and how permissions are handled.

### 12.1. SDK Versions

*   **`minSdkVersion` (Android 14 / API Level 34):** Setting the minimum SDK to Android 14 ensures that the app will only run on devices that support this version or higher. This simplifies development by allowing us to use modern Android APIs and features without needing extensive backward compatibility checks for older Android versions. It also aligns with the target device, Samsung S25 Ultra, which is expected to run Android 14 or newer.
*   **`targetSdkVersion` (Android 14 / API Level 34):** Targeting Android 14 means the app will behave according to the latest platform changes and privacy features introduced in Android 14. This is crucial for security, performance, and user experience. It ensures the app is optimized for the latest Android environment.
*   **`compileSdkVersion` (Android 14 / API Level 34):** The app will be compiled against Android 14, allowing access to all the latest APIs and features available in that version.

### 12.2. Permissions Handling

Android 14 continues to build upon the permission model introduced in previous versions, emphasizing user privacy and control. We will implement runtime permission requests for all sensitive permissions.

*   **`CAMERA` (android.permission.CAMERA):** This is a runtime permission. The app will request this permission when the user initiates a scanning session or attempts to access camera functionalities. It is essential for capturing 4K video data.

*   **`STORAGE` (android.permission.READ_MEDIA_IMAGES, android.permission.READ_MEDIA_VIDEO, android.permission.READ_MEDIA_AUDIO for Android 13+; `WRITE_EXTERNAL_STORAGE` for older versions):** For Android 14, `WRITE_EXTERNAL_STORAGE` is largely deprecated for app-specific files. Instead, we will use `MediaStore` APIs for saving 3D models to shared storage (e.g., the `Downloads` or `Documents` directory) or use app-specific internal/external storage directories which do not require explicit storage permissions. If models need to be accessible by other apps, `MediaStore` will be the preferred method, and the app will request `READ_MEDIA_IMAGES` or `READ_MEDIA_VIDEO` if it needs to read media files created by other apps. For our purpose of saving 3D models, we will primarily use app-specific storage or `MediaStore` for user-initiated exports, which often don't require explicit permissions beyond the intent to save.

*   **`SENSORS` (android.permission.BODY_SENSORS):** This permission is for accessing sensitive sensor data, which might include some advanced ToF sensor capabilities if they fall under this category. However, basic ToF sensor access for depth data often does not require explicit `BODY_SENSORS` permission, as it's typically handled by the camera framework or specific device APIs. We will verify the exact permission requirements for the Samsung S25 Ultra's ToF sensor. If the ToF sensor data is exposed through standard camera APIs (e.g., Camera2 API with depth output), then `CAMERA` permission might suffice. If it's a separate sensor, `BODY_SENSORS` might be needed.

*   **`LOCATION` (android.permission.ACCESS_FINE_LOCATION, android.permission.ACCESS_COARSE_LOCATION):** As discussed, location permission is not strictly required for the core scanning functionality. If ARCore's full capabilities (e.g., persistent anchors that rely on location) are utilized, or if geotagging of scans becomes a desired feature, then these runtime permissions will be requested. Otherwise, they will be omitted to minimize permission requests and enhance user privacy.

### 12.3. Best Practices for Permissions

*   **Just-in-time requests:** Permissions will be requested only when they are actually needed by the user action, not at app startup.
*   **Clear explanations:** Users will be provided with clear explanations (e.g., using a dialog) why a particular permission is needed before requesting it.
*   **Graceful degradation:** The app will be designed to function gracefully even if a user denies a non-critical permission, providing alternative functionalities or informing the user about limitations.





## 13. Initial Design Decisions Summary

Based on the requirements and architectural planning, the following initial design decisions have been made:

*   **Development Environment:** Android Studio with Kotlin as the primary programming language.
*   **Architectural Pattern:** MVVM (Model-View-ViewModel) for robust, testable, and maintainable code.
*   **Target Android Version:** Android 14 (API Level 34) for `minSdkVersion`, `targetSdkVersion`, and `compileSdkVersion` to leverage the latest features and ensure compatibility with the Samsung S25 Ultra.
*   **Core Libraries:**
    *   **CameraX:** For simplified and consistent camera access, including 4K video capture at 60 FPS with auto-focus and stabilization.
    *   **ARCore:** For advanced motion tracking, environmental understanding, and potentially enhancing depth data from the ToF sensor.
    *   **OpenCV:** For image processing tasks such as camera calibration, noise reduction, and feature detection.
    *   **TensorFlow Lite:** For on-device machine learning capabilities, enabling potential future enhancements like object recognition or intelligent mesh optimization.
    *   **OpenGL ES 3.0:** For high-performance 3D rendering and real-time preview of scanned models.
    *   **Assimp (via JNI) or Custom Parsers:** For handling various 3D model export formats (OBJ, STL, PLY, GLTF) with compression and adjustable quality.
*   **Data Storage:**
    *   **Local:** App-specific storage and `MediaStore` for saving binary 3D model files with compression, targeting a maximum size of 1GB for local storage.
    *   **Cloud:** Firebase (or similar BaaS) for encrypted, auto-syncing cloud storage for backup and multi-device access.
    *   **Database:** SQLite with Room Persistence Library for managing metadata about scans, application settings, and user preferences.
*   **User Interface:** Distinct screens for Scanning, Model Viewing, and Gallery, designed for intuitive user interaction.
*   **Performance:** Focus on GPU acceleration, mesh simplification, and texture compression to ensure smooth real-time performance. Strict memory management with a target max RAM usage of 2GB and a 500MB cache.
*   **Security:** Data encryption for all sensitive data and mandatory user authentication via biometric methods (fingerprint/face unlock) and PIN.
*   **Permissions:** Runtime permission requests for `CAMERA`, and careful consideration for `STORAGE` (using `MediaStore` or app-specific storage) and `SENSORS` (verifying ToF sensor access requirements). `LOCATION` permission will be omitted unless explicitly required for future features.

This architecture provides a solid foundation for developing a high-performance, feature-rich, and user-friendly 3D Scanner Mobile App.

