<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- App specific colors -->
    <color name="primary_blue">#FF1976D2</color>
    <color name="primary_blue_dark">#FF0D47A1</color>
    <color name="primary_blue_light">#FF42A5F5</color>
    <color name="accent_orange">#FFFF9800</color>
    <color name="accent_orange_dark">#FFF57C00</color>
    <color name="accent_orange_light">#FFFFB74D</color>
    
    <!-- Background colors -->
    <color name="background_light">#FFF5F5F5</color>
    <color name="background_dark">#FF121212</color>
    <color name="surface_light">#FFFFFFFF</color>
    <color name="surface_dark">#FF1E1E1E</color>
    
    <!-- Text colors -->
    <color name="text_primary_light">#DE000000</color>
    <color name="text_secondary_light">#99000000</color>
    <color name="text_primary_dark">#FFFFFFFF</color>
    <color name="text_secondary_dark">#B3FFFFFF</color>
    
    <!-- Status colors -->
    <color name="success_green">#FF4CAF50</color>
    <color name="error_red">#FFF44336</color>
    <color name="warning_yellow">#FFFF9800</color>
    <color name="info_blue">#FF2196F3</color>
    
    <!-- Scanning specific colors -->
    <color name="scan_progress">#FF4CAF50</color>
    <color name="depth_overlay">#804CAF50</color>
    <color name="camera_overlay">#80000000</color>
    
    <!-- Button colors -->
    <color name="button_primary">#FF1976D2</color>
    <color name="button_secondary">#FF757575</color>
    <color name="button_danger">#FFF44336</color>
</resources>

