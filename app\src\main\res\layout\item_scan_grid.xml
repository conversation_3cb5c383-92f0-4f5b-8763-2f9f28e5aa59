<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/stroke_light"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Thumbnail -->
        <ImageView
            android:id="@+id/iv_thumbnail"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:background="@color/surface_light"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_3d_model_placeholder" />

        <!-- Quality Indicator -->
        <ImageView
            android:id="@+id/iv_quality_indicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_background_white"
            android:padding="4dp"
            app:layout_constraintEnd_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="@+id/iv_thumbnail"
            tools:src="@drawable/ic_quality_high" />

        <!-- Texture Indicator -->
        <ImageView
            android:id="@+id/iv_texture_indicator"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_background_white"
            android:padding="3dp"
            android:src="@drawable/ic_texture"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="@+id/iv_thumbnail"
            tools:visibility="visible" />

        <!-- Scan Name -->
        <TextView
            android:id="@+id/tv_scan_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary_light"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_thumbnail"
            tools:text="Living Room Scan" />

        <!-- Date and Size -->
        <TextView
            android:id="@+id/tv_scan_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary_light"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_file_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_scan_name"
            tools:text="Mar 15, 2024" />

        <TextView
            android:id="@+id/tv_file_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary_light"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_scan_name"
            tools:text="2.5 MB" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
