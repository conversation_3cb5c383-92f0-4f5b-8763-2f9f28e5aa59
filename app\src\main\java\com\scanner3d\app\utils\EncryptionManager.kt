package com.scanner3d.app.utils

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import androidx.security.crypto.EncryptedFile
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import java.io.File
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec

class EncryptionManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "EncryptionManager"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS = "Scanner3D_MasterKey"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
        
        @Volatile
        private var INSTANCE: EncryptionManager? = null
        
        fun getInstance(context: Context): EncryptionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: EncryptionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val masterKey: MasterKey by lazy {
        MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
    }
    
    private val keyStore: KeyStore by lazy {
        KeyStore.getInstance(ANDROID_KEYSTORE).apply {
            load(null)
        }
    }
    
    init {
        generateOrRetrieveMasterKey()
    }
    
    private fun generateOrRetrieveMasterKey() {
        try {
            if (!keyStore.containsAlias(KEY_ALIAS)) {
                val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
                val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                    KEY_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                )
                    .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                    .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                    .setKeySize(256)
                    .build()
                
                keyGenerator.init(keyGenParameterSpec)
                keyGenerator.generateKey()
                
                Log.d(TAG, "Master key generated successfully")
            } else {
                Log.d(TAG, "Master key already exists")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate or retrieve master key", e)
            throw e
        }
    }
    
    fun encryptData(data: ByteArray): EncryptedData {
        return try {
            val secretKey = keyStore.getKey(KEY_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val iv = cipher.iv
            val encryptedBytes = cipher.doFinal(data)
            
            EncryptedData(
                encryptedBytes = encryptedBytes,
                iv = iv,
                algorithm = TRANSFORMATION
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to encrypt data", e)
            throw e
        }
    }
    
    fun decryptData(encryptedData: EncryptedData): ByteArray {
        return try {
            val secretKey = keyStore.getKey(KEY_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val spec = GCMParameterSpec(GCM_TAG_LENGTH * 8, encryptedData.iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec)
            
            cipher.doFinal(encryptedData.encryptedBytes)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to decrypt data", e)
            throw e
        }
    }
    
    fun encryptString(plainText: String): String {
        return try {
            val encryptedData = encryptData(plainText.toByteArray(Charsets.UTF_8))
            val combined = encryptedData.iv + encryptedData.encryptedBytes
            Base64.encodeToString(combined, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to encrypt string", e)
            throw e
        }
    }
    
    fun decryptString(encryptedString: String): String {
        return try {
            val combined = Base64.decode(encryptedString, Base64.DEFAULT)
            val iv = combined.sliceArray(0..GCM_IV_LENGTH - 1)
            val encryptedBytes = combined.sliceArray(GCM_IV_LENGTH until combined.size)
            
            val encryptedData = EncryptedData(encryptedBytes, iv, TRANSFORMATION)
            val decryptedBytes = decryptData(encryptedData)
            
            String(decryptedBytes, Charsets.UTF_8)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to decrypt string", e)
            throw e
        }
    }
    
    fun createEncryptedFile(file: File): EncryptedFile {
        return EncryptedFile.Builder(
            context,
            file,
            masterKey,
            EncryptedFile.FileEncryptionScheme.AES256_GCM_HKDF_4KB
        ).build()
    }
    
    fun createEncryptedSharedPreferences(fileName: String): android.content.SharedPreferences {
        return EncryptedSharedPreferences.create(
            context,
            fileName,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    fun encryptScanFile(inputFile: File, outputFile: File): Boolean {
        return try {
            val encryptedFile = createEncryptedFile(outputFile)
            
            inputFile.inputStream().use { input ->
                encryptedFile.openFileOutput().use { output ->
                    input.copyTo(output)
                }
            }
            
            Log.d(TAG, "Scan file encrypted successfully: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to encrypt scan file", e)
            false
        }
    }
    
    fun decryptScanFile(encryptedFile: File, outputFile: File): Boolean {
        return try {
            val encrypted = createEncryptedFile(encryptedFile)
            
            encrypted.openFileInput().use { input ->
                outputFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            
            Log.d(TAG, "Scan file decrypted successfully: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to decrypt scan file", e)
            false
        }
    }
    
    fun generateSecureHash(data: ByteArray): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(data)
            Base64.encodeToString(hashBytes, Base64.NO_WRAP)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate secure hash", e)
            throw e
        }
    }
    
    fun generateSecureHash(text: String): String {
        return generateSecureHash(text.toByteArray(Charsets.UTF_8))
    }
    
    fun verifyHash(data: ByteArray, expectedHash: String): Boolean {
        return try {
            val actualHash = generateSecureHash(data)
            actualHash == expectedHash
        } catch (e: Exception) {
            Log.e(TAG, "Failed to verify hash", e)
            false
        }
    }
    
    fun generateRandomKey(keySize: Int = 256): SecretKey {
        val keyGenerator = KeyGenerator.getInstance("AES")
        keyGenerator.init(keySize)
        return keyGenerator.generateKey()
    }
    
    fun encryptWithCustomKey(data: ByteArray, key: SecretKey): EncryptedData {
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, key)
            
            val iv = cipher.iv
            val encryptedBytes = cipher.doFinal(data)
            
            EncryptedData(
                encryptedBytes = encryptedBytes,
                iv = iv,
                algorithm = TRANSFORMATION
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to encrypt with custom key", e)
            throw e
        }
    }
    
    fun decryptWithCustomKey(encryptedData: EncryptedData, key: SecretKey): ByteArray {
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val spec = GCMParameterSpec(GCM_TAG_LENGTH * 8, encryptedData.iv)
            cipher.init(Cipher.DECRYPT_MODE, key, spec)
            
            cipher.doFinal(encryptedData.encryptedBytes)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to decrypt with custom key", e)
            throw e
        }
    }
    
    fun keyToString(key: SecretKey): String {
        return Base64.encodeToString(key.encoded, Base64.NO_WRAP)
    }
    
    fun stringToKey(keyString: String): SecretKey {
        val keyBytes = Base64.decode(keyString, Base64.NO_WRAP)
        return SecretKeySpec(keyBytes, "AES")
    }
    
    fun isKeyStoreAvailable(): Boolean {
        return try {
            keyStore.containsAlias(KEY_ALIAS)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check keystore availability", e)
            false
        }
    }
    
    fun clearKeys() {
        try {
            if (keyStore.containsAlias(KEY_ALIAS)) {
                keyStore.deleteEntry(KEY_ALIAS)
                Log.d(TAG, "Master key deleted")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear keys", e)
        }
    }
    
    data class EncryptedData(
        val encryptedBytes: ByteArray,
        val iv: ByteArray,
        val algorithm: String
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            
            other as EncryptedData
            
            if (!encryptedBytes.contentEquals(other.encryptedBytes)) return false
            if (!iv.contentEquals(other.iv)) return false
            if (algorithm != other.algorithm) return false
            
            return true
        }
        
        override fun hashCode(): Int {
            var result = encryptedBytes.contentHashCode()
            result = 31 * result + iv.contentHashCode()
            result = 31 * result + algorithm.hashCode()
            return result
        }
    }
}
