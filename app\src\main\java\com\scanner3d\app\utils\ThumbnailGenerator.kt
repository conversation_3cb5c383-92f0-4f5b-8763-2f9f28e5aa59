package com.scanner3d.app.utils

import android.graphics.*
import android.util.Log
import com.scanner3d.app.data.model.Mesh3D
import kotlin.math.*

class ThumbnailGenerator {
    
    companion object {
        private const val TAG = "ThumbnailGenerator"
        private const val THUMBNAIL_SIZE = 256
        private const val BACKGROUND_COLOR = Color.WHITE
        private const val MESH_COLOR = Color.GRAY
        private const val WIREFRAME_COLOR = Color.BLACK
    }
    
    fun generateThumbnail(mesh: Mesh3D): Bitmap {
        val bitmap = Bitmap.createBitmap(THUMBNAIL_SIZE, THUMBNAIL_SIZE, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // Clear background
        canvas.drawColor(BACKGROUND_COLOR)
        
        try {
            // Project 3D mesh to 2D and render
            renderMeshThumbnail(canvas, mesh)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate thumbnail", e)
            // Draw fallback placeholder
            drawPlaceholder(canvas)
        }
        
        return bitmap
    }
    
    private fun renderMeshThumbnail(canvas: Canvas, mesh: Mesh3D) {
        // Calculate optimal viewing angle and scale
        val boundingBox = mesh.boundingBox
        val center = boundingBox.center
        val maxDimension = maxOf(boundingBox.width, boundingBox.height, boundingBox.depth)
        
        // Set up projection parameters
        val scale = (THUMBNAIL_SIZE * 0.8f) / maxDimension
        val offsetX = THUMBNAIL_SIZE / 2f
        val offsetY = THUMBNAIL_SIZE / 2f
        
        // Rotation for better viewing angle
        val rotationX = -30f * PI / 180f // Tilt down slightly
        val rotationY = 45f * PI / 180f  // Rotate for 3D effect
        
        // Project vertices to 2D
        val projectedVertices = mutableListOf<PointF>()
        
        for (i in 0 until mesh.vertexCount) {
            val x = mesh.vertices[i * 3] - center.first
            val y = mesh.vertices[i * 3 + 1] - center.second
            val z = mesh.vertices[i * 3 + 2] - center.third
            
            // Apply rotations
            val rotatedPoint = applyRotation(x, y, z, rotationX, rotationY)
            
            // Project to 2D (orthographic projection)
            val screenX = rotatedPoint.first * scale + offsetX
            val screenY = -rotatedPoint.second * scale + offsetY // Flip Y axis
            
            projectedVertices.add(PointF(screenX, screenY))
        }
        
        // Render triangles
        renderTriangles(canvas, mesh, projectedVertices)
        
        // Optionally render wireframe
        if (mesh.triangleCount < 1000) { // Only for low-poly meshes
            renderWireframe(canvas, mesh, projectedVertices)
        }
    }
    
    private fun applyRotation(x: Float, y: Float, z: Float, rotX: Double, rotY: Double): Triple<Float, Float, Float> {
        // Rotate around X axis
        val cosX = cos(rotX).toFloat()
        val sinX = sin(rotX).toFloat()
        val y1 = y * cosX - z * sinX
        val z1 = y * sinX + z * cosX
        
        // Rotate around Y axis
        val cosY = cos(rotY).toFloat()
        val sinY = sin(rotY).toFloat()
        val x2 = x * cosY + z1 * sinY
        val z2 = -x * sinY + z1 * cosY
        
        return Triple(x2, y1, z2)
    }
    
    private fun renderTriangles(canvas: Canvas, mesh: Mesh3D, projectedVertices: List<PointF>) {
        val paint = Paint().apply {
            color = MESH_COLOR
            style = Paint.Style.FILL
            isAntiAlias = true
        }
        
        // Calculate triangle depths for z-sorting
        val triangleDepths = mutableListOf<Pair<Int, Float>>()
        
        for (i in mesh.indices.indices step 3) {
            val v1Index = mesh.indices[i]
            val v2Index = mesh.indices[i + 1]
            val v3Index = mesh.indices[i + 2]
            
            // Calculate average Z depth
            val z1 = mesh.vertices[v1Index * 3 + 2]
            val z2 = mesh.vertices[v2Index * 3 + 2]
            val z3 = mesh.vertices[v3Index * 3 + 2]
            val avgDepth = (z1 + z2 + z3) / 3f
            
            triangleDepths.add(Pair(i, avgDepth))
        }
        
        // Sort triangles by depth (back to front)
        triangleDepths.sortBy { it.second }
        
        // Render triangles
        for ((triangleIndex, _) in triangleDepths) {
            val v1Index = mesh.indices[triangleIndex]
            val v2Index = mesh.indices[triangleIndex + 1]
            val v3Index = mesh.indices[triangleIndex + 2]
            
            val p1 = projectedVertices[v1Index]
            val p2 = projectedVertices[v2Index]
            val p3 = projectedVertices[v3Index]
            
            // Check if triangle is visible (not degenerate)
            if (isTriangleVisible(p1, p2, p3)) {
                val path = Path().apply {
                    moveTo(p1.x, p1.y)
                    lineTo(p2.x, p2.y)
                    lineTo(p3.x, p3.y)
                    close()
                }
                
                // Calculate lighting based on normal
                val lightingFactor = calculateLighting(mesh, triangleIndex)
                val shadedColor = applyLighting(MESH_COLOR, lightingFactor)
                paint.color = shadedColor
                
                canvas.drawPath(path, paint)
            }
        }
    }
    
    private fun renderWireframe(canvas: Canvas, mesh: Mesh3D, projectedVertices: List<PointF>) {
        val paint = Paint().apply {
            color = WIREFRAME_COLOR
            style = Paint.Style.STROKE
            strokeWidth = 1f
            isAntiAlias = true
        }
        
        for (i in mesh.indices.indices step 3) {
            val v1Index = mesh.indices[i]
            val v2Index = mesh.indices[i + 1]
            val v3Index = mesh.indices[i + 2]
            
            val p1 = projectedVertices[v1Index]
            val p2 = projectedVertices[v2Index]
            val p3 = projectedVertices[v3Index]
            
            // Draw triangle edges
            canvas.drawLine(p1.x, p1.y, p2.x, p2.y, paint)
            canvas.drawLine(p2.x, p2.y, p3.x, p3.y, paint)
            canvas.drawLine(p3.x, p3.y, p1.x, p1.y, paint)
        }
    }
    
    private fun isTriangleVisible(p1: PointF, p2: PointF, p3: PointF): Boolean {
        // Check if triangle is within canvas bounds and not degenerate
        val minX = minOf(p1.x, p2.x, p3.x)
        val maxX = maxOf(p1.x, p2.x, p3.x)
        val minY = minOf(p1.y, p2.y, p3.y)
        val maxY = maxOf(p1.y, p2.y, p3.y)
        
        // Triangle is visible if it overlaps with canvas
        return maxX >= 0 && minX <= THUMBNAIL_SIZE && maxY >= 0 && minY <= THUMBNAIL_SIZE
    }
    
    private fun calculateLighting(mesh: Mesh3D, triangleIndex: Int): Float {
        // Simple directional lighting
        val lightDirection = floatArrayOf(0.5f, 0.5f, 1f) // Light coming from top-right-front
        
        // Get triangle normal
        val normal = if (mesh.normals != null) {
            val v1Index = mesh.indices[triangleIndex]
            val v2Index = mesh.indices[triangleIndex + 1]
            val v3Index = mesh.indices[triangleIndex + 2]
            
            // Average vertex normals
            val n1 = floatArrayOf(mesh.normals[v1Index * 3], mesh.normals[v1Index * 3 + 1], mesh.normals[v1Index * 3 + 2])
            val n2 = floatArrayOf(mesh.normals[v2Index * 3], mesh.normals[v2Index * 3 + 1], mesh.normals[v2Index * 3 + 2])
            val n3 = floatArrayOf(mesh.normals[v3Index * 3], mesh.normals[v3Index * 3 + 1], mesh.normals[v3Index * 3 + 2])
            
            floatArrayOf(
                (n1[0] + n2[0] + n3[0]) / 3f,
                (n1[1] + n2[1] + n3[1]) / 3f,
                (n1[2] + n2[2] + n3[2]) / 3f
            )
        } else {
            // Calculate face normal
            calculateFaceNormal(mesh, triangleIndex)
        }
        
        // Normalize light direction
        val lightLength = sqrt(lightDirection[0] * lightDirection[0] + lightDirection[1] * lightDirection[1] + lightDirection[2] * lightDirection[2])
        lightDirection[0] /= lightLength
        lightDirection[1] /= lightLength
        lightDirection[2] /= lightLength
        
        // Calculate dot product (cosine of angle between normal and light)
        val dotProduct = normal[0] * lightDirection[0] + normal[1] * lightDirection[1] + normal[2] * lightDirection[2]
        
        // Clamp to [0.3, 1.0] for ambient + diffuse lighting
        return (dotProduct * 0.7f + 0.3f).coerceIn(0.3f, 1f)
    }
    
    private fun calculateFaceNormal(mesh: Mesh3D, triangleIndex: Int): FloatArray {
        val v1Index = mesh.indices[triangleIndex] * 3
        val v2Index = mesh.indices[triangleIndex + 1] * 3
        val v3Index = mesh.indices[triangleIndex + 2] * 3
        
        val v1 = floatArrayOf(mesh.vertices[v1Index], mesh.vertices[v1Index + 1], mesh.vertices[v1Index + 2])
        val v2 = floatArrayOf(mesh.vertices[v2Index], mesh.vertices[v2Index + 1], mesh.vertices[v2Index + 2])
        val v3 = floatArrayOf(mesh.vertices[v3Index], mesh.vertices[v3Index + 1], mesh.vertices[v3Index + 2])
        
        val edge1 = floatArrayOf(v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2])
        val edge2 = floatArrayOf(v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2])
        
        val normal = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        // Normalize
        val length = sqrt(normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2])
        if (length > 0) {
            normal[0] /= length
            normal[1] /= length
            normal[2] /= length
        }
        
        return normal
    }
    
    private fun applyLighting(baseColor: Int, lightingFactor: Float): Int {
        val r = ((Color.red(baseColor) * lightingFactor).toInt()).coerceIn(0, 255)
        val g = ((Color.green(baseColor) * lightingFactor).toInt()).coerceIn(0, 255)
        val b = ((Color.blue(baseColor) * lightingFactor).toInt()).coerceIn(0, 255)
        
        return Color.rgb(r, g, b)
    }
    
    private fun drawPlaceholder(canvas: Canvas) {
        val paint = Paint().apply {
            color = Color.LTGRAY
            style = Paint.Style.FILL
            isAntiAlias = true
        }
        
        // Draw a simple 3D cube as placeholder
        val size = THUMBNAIL_SIZE * 0.4f
        val centerX = THUMBNAIL_SIZE / 2f
        val centerY = THUMBNAIL_SIZE / 2f
        
        // Draw cube faces
        val path = Path().apply {
            // Front face
            moveTo(centerX - size/2, centerY - size/2)
            lineTo(centerX + size/2, centerY - size/2)
            lineTo(centerX + size/2, centerY + size/2)
            lineTo(centerX - size/2, centerY + size/2)
            close()
        }
        
        canvas.drawPath(path, paint)
        
        // Draw edges for 3D effect
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 2f
        paint.color = Color.GRAY
        
        canvas.drawPath(path, paint)
        
        // Draw perspective lines
        val offset = size * 0.2f
        canvas.drawLine(centerX - size/2, centerY - size/2, centerX - size/2 + offset, centerY - size/2 - offset, paint)
        canvas.drawLine(centerX + size/2, centerY - size/2, centerX + size/2 + offset, centerY - size/2 - offset, paint)
        canvas.drawLine(centerX + size/2, centerY + size/2, centerX + size/2 + offset, centerY + size/2 - offset, paint)
    }
}
