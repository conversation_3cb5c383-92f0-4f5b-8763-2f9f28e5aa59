// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityGalleryBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton fabNewScan;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewScans;

  @NonNull
  public final SearchView searchView;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final LinearLayout tvEmptyState;

  @NonNull
  public final TextView tvScanCount;

  @NonNull
  public final TextView tvTotalSize;

  private ActivityGalleryBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton fabNewScan, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewScans, @NonNull SearchView searchView,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull Toolbar toolbar,
      @NonNull LinearLayout tvEmptyState, @NonNull TextView tvScanCount,
      @NonNull TextView tvTotalSize) {
    this.rootView = rootView;
    this.fabNewScan = fabNewScan;
    this.progressBar = progressBar;
    this.recyclerViewScans = recyclerViewScans;
    this.searchView = searchView;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.toolbar = toolbar;
    this.tvEmptyState = tvEmptyState;
    this.tvScanCount = tvScanCount;
    this.tvTotalSize = tvTotalSize;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityGalleryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityGalleryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_gallery, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityGalleryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fab_new_scan;
      FloatingActionButton fabNewScan = ViewBindings.findChildViewById(rootView, id);
      if (fabNewScan == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_scans;
      RecyclerView recyclerViewScans = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewScans == null) {
        break missingId;
      }

      id = R.id.search_view;
      SearchView searchView = ViewBindings.findChildViewById(rootView, id);
      if (searchView == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_empty_state;
      LinearLayout tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_scan_count;
      TextView tvScanCount = ViewBindings.findChildViewById(rootView, id);
      if (tvScanCount == null) {
        break missingId;
      }

      id = R.id.tv_total_size;
      TextView tvTotalSize = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSize == null) {
        break missingId;
      }

      return new ActivityGalleryBinding((CoordinatorLayout) rootView, fabNewScan, progressBar,
          recyclerViewScans, searchView, swipeRefreshLayout, toolbar, tvEmptyState, tvScanCount,
          tvTotalSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
