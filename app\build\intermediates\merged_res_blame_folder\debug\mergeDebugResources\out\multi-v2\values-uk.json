{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa51a49a0fdb8d78736b292657adf1a3\\transformed\\core-1.41.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,293,427,564", "endColumns": "49,52,133,136,85", "endOffsets": "239,292,426,563,649"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "424,478,535,673,814", "endColumns": "53,56,137,140,89", "endOffsets": "473,530,668,809,899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08372b0b4714dee0e9823f116e744e60\\transformed\\navigation-ui-2.7.5\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11247,11355", "endColumns": "107,119", "endOffsets": "11350,11470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e90c754ff7f976aa957bdef8385fc4b1\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "904,1013,1115,1223,1309,1414,1532,1613,1692,1783,1876,1971,2065,2165,2258,2353,2448,2539,2630,2729,2835,2941,3039,3146,3253,3358,3528,11557", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "1008,1110,1218,1304,1409,1527,1608,1687,1778,1871,1966,2060,2160,2253,2348,2443,2534,2625,2724,2830,2936,3034,3141,3248,3353,3523,3623,11634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b986efbab3f35a8495809ca7b1ee567d\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "45,46,47,48,49,50,51,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4071,4171,4273,4374,4475,4580,4685,11639", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "4166,4268,4369,4470,4575,4680,4793,11735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0060a6d1e9ec36933e7259d41494599\\transformed\\material-1.11.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2954,3042,3120,3177,3228,3294,3366,3442,3532,3615,3688,3765,3846,3920,4029,4119,4198,4289,4385,4459,4540,4635,4689,4771,4837,4924,5010,5072,5136,5199,5272,5379,5489,5587,5693,5754,5809", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2949,3037,3115,3172,3223,3289,3361,3437,3527,3610,3683,3760,3841,3915,4024,4114,4193,4284,4380,4454,4535,4630,4684,4766,4832,4919,5005,5067,5131,5194,5267,5374,5484,5582,5688,5749,5804,5886"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3628,3706,3784,3872,3980,4798,4894,5010,5293,5360,6708,6774,6837,6925,6987,7054,7112,7183,7242,7296,7410,7470,7533,7587,7660,7779,7865,7948,8087,8172,8259,8392,8480,8558,8615,8666,8732,8804,8880,8970,9053,9126,9203,9284,9358,9467,9557,9636,9727,9823,9897,9978,10073,10127,10209,10275,10362,10448,10510,10574,10637,10710,10817,10927,11025,11131,11192,11475", "endLines": "7,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "419,3701,3779,3867,3975,4066,4889,5005,5088,5355,5446,6769,6832,6920,6982,7049,7107,7178,7237,7291,7405,7465,7528,7582,7655,7774,7860,7943,8082,8167,8254,8387,8475,8553,8610,8661,8727,8799,8875,8965,9048,9121,9198,9279,9353,9462,9552,9631,9722,9818,9892,9973,10068,10122,10204,10270,10357,10443,10505,10569,10632,10705,10812,10922,11020,11126,11187,11242,11552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad912a0b6595c0fd27017314ccf5845d\\transformed\\biometric-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "55,56,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5093,5200,5451,5578,5709,5850,5961,6088,6222,6321,6451,6583", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "5195,5288,5573,5704,5845,5956,6083,6217,6316,6446,6578,6703"}}]}]}