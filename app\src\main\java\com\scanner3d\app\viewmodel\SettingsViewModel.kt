package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.data.model.AppSettings
import com.scanner3d.app.repository.SettingsRepository
import com.scanner3d.app.utils.FileManager
import kotlinx.coroutines.launch

class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val settingsRepository = SettingsRepository(application)
    private val fileManager = FileManager(application)
    
    private val _settings = MutableLiveData<AppSettings>()
    val settings: LiveData<AppSettings> = _settings
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _message = MutableLiveData<String?>()
    val message: LiveData<String?> = _message
    
    private val _storageInfo = MutableLiveData<StorageInfo>()
    val storageInfo: LiveData<StorageInfo> = _storageInfo
    
    data class StorageInfo(
        val cacheSize: Long,
        val dataSize: Long,
        val totalSize: Long
    )
    
    fun loadSettings() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val currentSettings = settingsRepository.getSettings()
                _settings.value = currentSettings
            } catch (e: Exception) {
                _message.value = "Failed to load settings: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun loadStorageInfo() {
        viewModelScope.launch {
            try {
                val cacheSize = fileManager.getCacheSize()
                val dataSize = fileManager.getDataSize()
                _storageInfo.value = StorageInfo(
                    cacheSize = cacheSize,
                    dataSize = dataSize,
                    totalSize = cacheSize + dataSize
                )
            } catch (e: Exception) {
                _message.value = "Failed to load storage info: ${e.message}"
            }
        }
    }
    
    fun setHighQualityMode(enabled: Boolean) {
        updateSetting { it.copy(highQualityMode = enabled) }
    }
    
    fun setAutoFocus(enabled: Boolean) {
        updateSetting { it.copy(autoFocus = enabled) }
    }
    
    fun setFlashlightEnabled(enabled: Boolean) {
        updateSetting { it.copy(flashlightEnabled = enabled) }
    }
    
    fun setResolution(resolution: String) {
        updateSetting { it.copy(resolution = resolution) }
    }
    
    fun setScanQuality(quality: String) {
        updateSetting { it.copy(defaultScanQuality = quality) }
    }
    
    fun setBiometricEnabled(enabled: Boolean) {
        updateSetting { it.copy(biometricEnabled = enabled) }
    }
    
    fun setAutoLockEnabled(enabled: Boolean) {
        updateSetting { it.copy(autoLockEnabled = enabled) }
    }
    
    fun setDeveloperMode(enabled: Boolean) {
        updateSetting { it.copy(developerMode = enabled) }
    }
    
    fun setDebugLogging(enabled: Boolean) {
        updateSetting { it.copy(debugLogging = enabled) }
    }
    
    private fun updateSetting(update: (AppSettings) -> AppSettings) {
        viewModelScope.launch {
            try {
                val currentSettings = _settings.value ?: settingsRepository.getSettings()
                val newSettings = update(currentSettings)
                settingsRepository.saveSettings(newSettings)
                _settings.value = newSettings
            } catch (e: Exception) {
                _message.value = "Failed to update setting: ${e.message}"
            }
        }
    }
    
    fun clearCache() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                fileManager.clearCache()
                loadStorageInfo()
                _message.value = "Cache cleared successfully"
            } catch (e: Exception) {
                _message.value = "Failed to clear cache: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun exportAppData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val exportFile = fileManager.exportAppData()
                _message.value = "App data exported to: ${exportFile.absolutePath}"
            } catch (e: Exception) {
                _message.value = "Export failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun importAppData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // For now, simulate import - would need file picker integration
                val dummyFile = java.io.File("dummy.zip")
                val success = fileManager.importAppData(dummyFile)
                if (success) {
                    loadSettings()
                    loadStorageInfo()
                    _message.value = "App data imported successfully"
                } else {
                    _message.value = "Failed to import app data"
                }
            } catch (e: Exception) {
                _message.value = "Import failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun resetSettings() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val defaultSettings = AppSettings()
                settingsRepository.saveSettings(defaultSettings)
                _settings.value = defaultSettings
                _message.value = "Settings reset to defaults"
            } catch (e: Exception) {
                _message.value = "Failed to reset settings: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearMessage() {
        _message.value = null
    }
} 