package com.scanner3d.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "scans")
@Parcelize
data class ScanEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String? = null,
    val createdAt: Long,
    val modifiedAt: Long,
    val scanDuration: Long,
    val filePath: String,
    val thumbnailPath: String? = null,
    val fileSize: Long,
    val format: String, // OBJ, STL, PLY, GLTF
    val quality: String, // LOW, MEDIUM, HIGH, ULTRA
    val vertexCount: Int,
    val triangleCount: Int,
    val hasTexture: Boolean,
    val hasColors: Boolean,
    val isUploaded: Boolean = false,
    val cloudUrl: String? = null,
    val tags: String? = null, // JSON array of tags
    val boundingBoxMinX: Float,
    val boundingBoxMinY: Float,
    val boundingBoxMinZ: Float,
    val boundingBoxMaxX: Float,
    val boundingBoxMaxY: Float,
    val boundingBoxMaxZ: Float
) : Parcelable {
    
    val boundingBox: Mesh3D.BoundingBox
        get() = Mesh3D.BoundingBox(
            boundingBoxMinX, boundingBoxMinY, boundingBoxMinZ,
            boundingBoxMaxX, boundingBoxMaxY, boundingBoxMaxZ
        )
    
    val meshQuality: Mesh3D.MeshQuality
        get() = when (quality) {
            "LOW" -> Mesh3D.MeshQuality.LOW
            "MEDIUM" -> Mesh3D.MeshQuality.MEDIUM
            "HIGH" -> Mesh3D.MeshQuality.HIGH
            "ULTRA" -> Mesh3D.MeshQuality.ULTRA
            else -> Mesh3D.MeshQuality.MEDIUM
        }
    
    val fileSizeFormatted: String
        get() = when {
            fileSize < 1024 -> "$fileSize B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024} KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)} MB"
            else -> "${fileSize / (1024 * 1024 * 1024)} GB"
        }
    
    val scanDurationFormatted: String
        get() {
            val seconds = scanDuration / 1000
            val minutes = seconds / 60
            val hours = minutes / 60
            return when {
                hours > 0 -> "${hours}h ${minutes % 60}m ${seconds % 60}s"
                minutes > 0 -> "${minutes}m ${seconds % 60}s"
                else -> "${seconds}s"
            }
        }
}
