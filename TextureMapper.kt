package com.scanner3d.app.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.Image
import android.util.Log
import com.google.ar.core.Pose
import com.scanner3d.app.core.ScanningEngine
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import java.nio.ByteBuffer
import kotlin.math.*

class TextureMapper {
    
    companion object {
        private const val TAG = "TextureMapper"
        private const val TEXTURE_SIZE = 1024
        private const val UV_SCALE = 1.0f
    }
    
    suspend fun applyTextures(
        mesh: Mesh3D,
        capturedFrames: List<ScanningEngine.CapturedFrame>
    ): Mesh3D = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Starting texture mapping with ${capturedFrames.size} frames")
        
        try {
            // Generate UV coordinates for the mesh
            val uvCoordinates = generateUVCoordinates(mesh)
            
            // Create texture atlas from captured frames
            val textureAtlas = createTextureAtlas(mesh, capturedFrames, uvCoordinates)
            
            // Update mesh metadata
            val updatedMetadata = mesh.metadata.copy(
                hasTexture = true,
                estimatedFileSize = mesh.metadata.estimatedFileSize + estimateTextureSize(textureAtlas)
            )
            
            mesh.copy(
                textureCoordinates = uvCoordinates,
                metadata = updatedMetadata
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying textures", e)
            throw e
        }
    }
    
    private fun generateUVCoordinates(mesh: Mesh3D): FloatArray {
        val vertexCount = mesh.vertexCount
        val uvCoordinates = FloatArray(vertexCount * 2) // 2 coordinates per vertex (u, v)
        
        // Simple planar projection for UV mapping
        // In a more sophisticated implementation, you would use:
        // - Conformal mapping
        // - Angle-based flattening
        // - Seam detection and unwrapping
        
        val boundingBox = mesh.boundingBox
        val width = boundingBox.width
        val height = boundingBox.height
        
        for (i in 0 until vertexCount) {
            val vertexIndex = i * 3
            val x = mesh.vertices[vertexIndex]
            val y = mesh.vertices[vertexIndex + 1]
            val z = mesh.vertices[vertexIndex + 2]
            
            // Project to UV space using bounding box normalization
            val u = ((x - boundingBox.minX) / width).coerceIn(0f, 1f)
            val v = ((y - boundingBox.minY) / height).coerceIn(0f, 1f)
            
            uvCoordinates[i * 2] = u * UV_SCALE
            uvCoordinates[i * 2 + 1] = v * UV_SCALE
        }
        
        Log.d(TAG, "Generated UV coordinates for $vertexCount vertices")
        return uvCoordinates
    }
    
    private fun createTextureAtlas(
        mesh: Mesh3D,
        capturedFrames: List<ScanningEngine.CapturedFrame>,
        uvCoordinates: FloatArray
    ): Bitmap {
        
        // Create texture atlas bitmap
        val atlas = Bitmap.createBitmap(TEXTURE_SIZE, TEXTURE_SIZE, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(atlas)
        
        // Initialize with a neutral color
        canvas.drawColor(android.graphics.Color.GRAY)
        
        // Process each captured frame
        capturedFrames.forEach { frame ->
            try {
                val bitmap = convertImageToBitmap(frame.colorImage)
                if (bitmap != null) {
                    projectFrameToAtlas(bitmap, frame.pose, mesh, uvCoordinates, canvas)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to process frame for texture mapping", e)
            }
        }
        
        Log.d(TAG, "Created texture atlas of size ${TEXTURE_SIZE}x${TEXTURE_SIZE}")
        return atlas
    }
    
    private fun convertImageToBitmap(image: Image): Bitmap? {
        return try {
            val buffer: ByteBuffer = image.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)
            BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to convert Image to Bitmap", e)
            null
        }
    }
    
    private fun projectFrameToAtlas(
        frameBitmap: Bitmap,
        pose: Pose?,
        mesh: Mesh3D,
        uvCoordinates: FloatArray,
        atlasCanvas: android.graphics.Canvas
    ) {
        if (pose == null) return
        
        // Convert frame bitmap to OpenCV Mat for processing
        val frameMat = Mat()
        Utils.bitmapToMat(frameBitmap, frameMat)
        
        // Camera intrinsic parameters (these should be obtained from CameraX)
        val fx = 1000.0 // Focal length X
        val fy = 1000.0 // Focal length Y
        val cx = frameBitmap.width / 2.0 // Principal point X
        val cy = frameBitmap.height / 2.0 // Principal point Y
        
        val cameraMatrix = Mat.eye(3, 3, CvType.CV_64F)
        cameraMatrix.put(0, 0, fx)
        cameraMatrix.put(1, 1, fy)
        cameraMatrix.put(0, 2, cx)
        cameraMatrix.put(1, 2, cy)
        
        // Convert pose to rotation and translation vectors
        val rotation = pose.rotationQuaternion
        val translation = pose.translation
        
        val rotationMatrix = Mat.eye(3, 3, CvType.CV_64F)
        quaternionToRotationMatrix(rotation, rotationMatrix)
        
        val translationVector = Mat(3, 1, CvType.CV_64F)
        translationVector.put(0, 0, translation[0].toDouble())
        translationVector.put(1, 0, translation[1].toDouble())
        translationVector.put(2, 0, translation[2].toDouble())
        
        // Project 3D mesh vertices to 2D image coordinates
        val vertexCount = mesh.vertexCount
        val objectPoints = Mat(vertexCount, 1, CvType.CV_32FC3)
        
        for (i in 0 until vertexCount) {
            val x = mesh.vertices[i * 3].toDouble()
            val y = mesh.vertices[i * 3 + 1].toDouble()
            val z = mesh.vertices[i * 3 + 2].toDouble()
            objectPoints.put(i, 0, x, y, z)
        }
        
        val imagePoints = Mat()
        val rvec = Mat()
        Calib3d.Rodrigues(rotationMatrix, rvec)
        
        Calib3d.projectPoints(
            objectPoints,
            rvec,
            translationVector,
            cameraMatrix,
            Mat(), // No distortion coefficients
            imagePoints
        )
        
        // Sample colors from the frame and apply to texture atlas
        val paint = android.graphics.Paint().apply {
            isAntiAlias = true
            alpha = 128 // Semi-transparent for blending
        }
        
        for (i in 0 until vertexCount) {
            val imagePoint = imagePoints.get(i, 0)
            val imageX = imagePoint[0].toInt()
            val imageY = imagePoint[1].toInt()
            
            // Check if the projected point is within the image bounds
            if (imageX >= 0 && imageX < frameBitmap.width && 
                imageY >= 0 && imageY < frameBitmap.height) {
                
                // Get color from the frame
                val color = frameBitmap.getPixel(imageX, imageY)
                
                // Map to texture atlas coordinates using UV
                val u = uvCoordinates[i * 2]
                val v = uvCoordinates[i * 2 + 1]
                
                val atlasX = (u * TEXTURE_SIZE).toInt().coerceIn(0, TEXTURE_SIZE - 1)
                val atlasY = (v * TEXTURE_SIZE).toInt().coerceIn(0, TEXTURE_SIZE - 1)
                
                // Draw a small circle at the atlas position
                paint.color = color
                atlasCanvas.drawCircle(atlasX.toFloat(), atlasY.toFloat(), 2f, paint)
            }
        }
    }
    
    private fun quaternionToRotationMatrix(quaternion: FloatArray, rotationMatrix: Mat) {
        val w = quaternion[3].toDouble()
        val x = quaternion[0].toDouble()
        val y = quaternion[1].toDouble()
        val z = quaternion[2].toDouble()
        
        // Convert quaternion to rotation matrix
        val r00 = 1 - 2 * (y * y + z * z)
        val r01 = 2 * (x * y - w * z)
        val r02 = 2 * (x * z + w * y)
        
        val r10 = 2 * (x * y + w * z)
        val r11 = 1 - 2 * (x * x + z * z)
        val r12 = 2 * (y * z - w * x)
        
        val r20 = 2 * (x * z - w * y)
        val r21 = 2 * (y * z + w * x)
        val r22 = 1 - 2 * (x * x + y * y)
        
        rotationMatrix.put(0, 0, r00, r01, r02)
        rotationMatrix.put(1, 0, r10, r11, r12)
        rotationMatrix.put(2, 0, r20, r21, r22)
    }
    
    private fun estimateTextureSize(textureAtlas: Bitmap): Long {
        // Estimate compressed texture size (assuming JPEG compression)
        val uncompressedSize = textureAtlas.width * textureAtlas.height * 4L // ARGB
        return uncompressedSize / 10 // Rough compression ratio
    }
    
    fun generateTextureCoordinatesForTriangle(
        v1: FloatArray,
        v2: FloatArray,
        v3: FloatArray,
        boundingBox: Mesh3D.BoundingBox
    ): FloatArray {
        // Generate UV coordinates for a single triangle
        val uvCoords = FloatArray(6) // 2 coordinates per vertex
        
        for (i in 0..2) {
            val vertex = when (i) {
                0 -> v1
                1 -> v2
                else -> v3
            }
            
            val u = ((vertex[0] - boundingBox.minX) / boundingBox.width).coerceIn(0f, 1f)
            val v = ((vertex[1] - boundingBox.minY) / boundingBox.height).coerceIn(0f, 1f)
            
            uvCoords[i * 2] = u
            uvCoords[i * 2 + 1] = v
        }
        
        return uvCoords
    }
    
    fun blendTextures(texture1: Bitmap, texture2: Bitmap, alpha: Float): Bitmap {
        val result = Bitmap.createBitmap(texture1.width, texture1.height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(result)
        
        val paint = android.graphics.Paint().apply {
            isAntiAlias = true
        }
        
        // Draw first texture
        canvas.drawBitmap(texture1, 0f, 0f, paint)
        
        // Draw second texture with alpha blending
        paint.alpha = (alpha * 255).toInt()
        canvas.drawBitmap(texture2, 0f, 0f, paint)
        
        return result
    }
}

