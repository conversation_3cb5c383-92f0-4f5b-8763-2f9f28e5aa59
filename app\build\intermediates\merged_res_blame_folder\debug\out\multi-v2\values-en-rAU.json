{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "64,65", "startColumns": "4,4", "startOffsets": "6398,6504", "endColumns": "105,116", "endOffsets": "6499,6616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "34,35,36,37,38,39,40,74", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3208,3304,3406,3505,3604,3708,3811,7239", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3299,3401,3500,3599,3703,3806,3922,7335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "43,46,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4101,4399,4980,5099,5228,5366,5493,5611,5742,5842,5968,6107", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4202,4484,5094,5223,5361,5488,5606,5737,5837,5963,6102,6222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-en-rAU\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,241,292,414,536", "endColumns": "46,50,121,121,83", "endOffsets": "240,291,413,535,619"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,211,337,463", "endColumns": "50,54,125,125,87", "endOffsets": "151,206,332,458,546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1331", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1326,1447"}, "to": {"startLines": "41,42,45,47,48,62,63,66,67,68,69,70,71,73,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3927,4019,4305,4489,4588,6227,6309,6621,6710,6794,6859,6923,7001,7166,7340,7406", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,65,120", "endOffsets": "4014,4096,4394,4583,4670,6304,6393,6705,6789,6854,6918,6996,7078,7234,7401,7522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "44,49,50,51", "startColumns": "4,4,4,4", "startOffsets": "4207,4675,4772,4881", "endColumns": "97,96,108,98", "endOffsets": "4300,4767,4876,4975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "551,655,755,863,947,1047,1162,1240,1315,1406,1499,1594,1688,1788,1881,1976,2070,2161,2252,2334,2437,2540,2639,2744,2848,2952,3108,7083", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "650,750,858,942,1042,1157,1235,1310,1401,1494,1589,1683,1783,1876,1971,2065,2156,2247,2329,2432,2535,2634,2739,2843,2947,3103,3203,7161"}}]}]}