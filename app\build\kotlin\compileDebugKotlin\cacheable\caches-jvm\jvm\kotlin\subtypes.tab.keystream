android.os.Parcelable4androidx.recyclerview.widget.RecyclerView.ViewHolder(androidx.appcompat.app.AppCompatActivitykotlin.Enum#androidx.lifecycle.AndroidViewModel androidx.viewbinding.ViewBindingandroidx.room.RoomDatabase#androidx.room.RoomDatabase.Callbackandroid.view.Viewandroid.opengl.GLSurfaceView%android.opengl.GLSurfaceView.Renderer(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           