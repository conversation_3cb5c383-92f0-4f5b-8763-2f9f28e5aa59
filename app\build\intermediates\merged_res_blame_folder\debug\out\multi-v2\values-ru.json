{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "45,46,47,48,49,50,51,166", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4025,4123,4225,4326,4427,4532,4635,15600", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4118,4220,4321,4422,4527,4630,4747,15696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "76,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "7634,8294,8399,8511", "endColumns": "107,104,111,104", "endOffsets": "7737,8394,8506,8611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,118", "endOffsets": "158,277"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "14657,14765", "endColumns": "107,118", "endOffsets": "14760,14879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3596,3674,3752,3836,3934,4752,4849,4986,8129,8195,9945,10194,10257,10375,10436,10501,10558,10628,10689,10743,10859,10916,10978,11032,11106,11234,11322,11408,11545,11629,11714,11848,11939,12015,12069,12120,12186,12258,12336,12432,12514,12594,12670,12747,12824,12931,13020,13093,13183,13278,13352,13433,13526,13581,13662,13728,13814,13899,13961,14025,14088,14160,14258,14357,14452,14544,14602,15195", "endLines": "7,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "426,3669,3747,3831,3929,4020,4844,4981,5073,8190,8289,10017,10252,10370,10431,10496,10553,10623,10684,10738,10854,10911,10973,11027,11101,11229,11317,11403,11540,11624,11709,11843,11934,12010,12064,12115,12181,12253,12331,12427,12509,12589,12665,12742,12819,12926,13015,13088,13178,13273,13347,13428,13521,13576,13657,13723,13809,13894,13956,14020,14083,14155,14253,14352,14447,14539,14597,14652,15270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,285,410,535", "endColumns": "46,47,124,124,82", "endOffsets": "236,284,409,534,617"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "431,482,534,663,792", "endColumns": "50,51,128,128,86", "endOffsets": "477,529,658,787,874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5254,5361,5527,5653,5763,5905,6034,6149,6410,6591,6698,6861,6987,7154,7312,7381,7441", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "5356,5522,5648,5758,5900,6029,6144,6248,6586,6693,6856,6982,7149,7307,7376,7436,7522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1369", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1364,1487"}, "to": {"startLines": "55,56,77,79,80,97,98,157,158,159,160,162,163,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5078,5171,7742,7935,8037,10022,10104,14884,14972,15054,15125,15275,15359,15528,15701,15771", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,69,122", "endOffsets": "5166,5249,7835,8032,8124,10099,10189,14967,15049,15120,15190,15354,15441,15595,15766,15889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,257,377,508,650,789,921,1064,1163,1304,1461", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "157,252,372,503,645,784,916,1059,1158,1299,1456,1581"}, "to": {"startLines": "75,78,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7527,7840,8616,8736,8867,9009,9148,9280,9423,9522,9663,9820", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "7629,7930,8731,8862,9004,9143,9275,9418,9517,9658,9815,9940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "879,994,1096,1195,1281,1386,1507,1586,1662,1754,1848,1943,2036,2131,2225,2321,2416,2508,2600,2689,2795,2902,3000,3109,3216,3330,3496,15446", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "989,1091,1190,1276,1381,1502,1581,1657,1749,1843,1938,2031,2126,2220,2316,2411,2503,2595,2684,2790,2897,2995,3104,3211,3325,3491,3591,15523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6253", "endColumns": "156", "endOffsets": "6405"}}]}]}