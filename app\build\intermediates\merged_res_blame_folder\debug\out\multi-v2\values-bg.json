{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-62:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2963,3048,3125,3178,3229,3295,3370,3446,3532,3611,3688,3764,3841,3915,4027,4118,4193,4284,4376,4450,4537,4628,4683,4765,4831,4914,5000,5062,5126,5189,5259,5376,5488,5599,5709,5766,5821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2958,3043,3120,3173,3224,3290,3365,3441,3527,3606,3683,3759,3836,3910,4022,4113,4188,4279,4371,4445,4532,4623,4678,4760,4826,4909,4995,5057,5121,5184,5254,5371,5483,5594,5704,5761,5816,5902"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3523,3602,3681,3764,3886,4737,4832,4965,5262,5328,6734,6814,6876,6965,7028,7093,7152,7225,7288,7342,7470,7527,7589,7643,7716,7859,7943,8031,8167,8255,8343,8479,8564,8641,8694,8745,8811,8886,8962,9048,9127,9204,9280,9357,9431,9543,9634,9709,9800,9892,9966,10053,10144,10199,10281,10347,10430,10516,10578,10642,10705,10775,10892,11004,11115,11225,11282,11574", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "310,3597,3676,3759,3881,3991,4827,4960,5049,5323,5420,6809,6871,6960,7023,7088,7147,7220,7283,7337,7465,7522,7584,7638,7711,7854,7938,8026,8162,8250,8338,8474,8559,8636,8689,8740,8806,8881,8957,9043,9122,9199,9275,9352,9426,9538,9629,9704,9795,9887,9961,10048,10139,10194,10276,10342,10425,10511,10573,10637,10700,10770,10887,10999,11110,11220,11277,11332,11655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3996,4093,4203,4305,4406,4513,4618,11744", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "4088,4198,4300,4401,4508,4613,4732,11840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,123", "endOffsets": "163,287"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11337,11450", "endColumns": "112,123", "endOffsets": "11445,11569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,263,385,515,648,784,906,1069,1170,1304,1441", "endColumns": "113,93,121,129,132,135,121,162,100,133,136,130", "endOffsets": "164,258,380,510,643,779,901,1064,1165,1299,1436,1567"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5054,5168,5425,5547,5677,5810,5946,6068,6231,6332,6466,6603", "endColumns": "113,93,121,129,132,135,121,162,100,133,136,130", "endOffsets": "5163,5257,5542,5672,5805,5941,6063,6226,6327,6461,6598,6729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,285,412,540", "endColumns": "45,48,126,127,88", "endOffsets": "235,284,411,539,628"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "315,365,418,549,681", "endColumns": "49,52,130,131,92", "endOffsets": "360,413,544,676,769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,894,1000,1105,1191,1301,1422,1502,1579,1670,1763,1858,1952,2052,2145,2240,2348,2439,2530,2613,2727,2835,2935,3049,3156,3264,3424,11660", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "889,995,1100,1186,1296,1417,1497,1574,1665,1758,1853,1947,2047,2140,2235,2343,2434,2525,2608,2722,2830,2930,3044,3151,3259,3419,3518,11739"}}]}]}