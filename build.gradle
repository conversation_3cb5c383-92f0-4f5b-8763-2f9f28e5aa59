// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.1.4' apply false
    id 'com.android.library' version '8.1.4' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.20' apply false
    id 'com.google.devtools.ksp' version '1.9.20-1.0.14' apply false
    // Firebase configuration (temporarily disabled for build testing)
    // id 'com.google.gms.google-services' version '4.4.0' apply false
    // id 'com.google.firebase.crashlytics' version '2.9.9' apply false
}

// Repositories are now managed in settings.gradle
// No need to declare them here due to repositoriesMode.FAIL_ON_PROJECT_REPOS

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
