com.scanner3d.app.MainActivity%com.scanner3d.app.core.ScanningEngine/com.scanner3d.app.core.ScanningEngine.Companion3com.scanner3d.app.core.ScanningEngine.CapturedFrame'com.scanner3d.app.data.database.ScanDao1com.scanner3d.app.data.database.Scanner3DDatabase;com.scanner3d.app.data.database.Scanner3DDatabase.CompanionLcom.scanner3d.app.data.database.Scanner3DDatabase.Companion.DatabaseCallback+com.scanner3d.app.data.database.SettingsDao(com.scanner3d.app.data.model.AppSettings#com.scanner3d.app.data.model.Mesh3D/com.scanner3d.app.data.model.Mesh3D.BoundingBox0com.scanner3d.app.data.model.Mesh3D.MeshMetadata/com.scanner3d.app.data.model.Mesh3D.MeshQuality+com.scanner3d.app.data.model.PointCloudData'com.scanner3d.app.data.model.ScanEntity)com.scanner3d.app.data.model.ScanProgress+com.scanner3d.app.repository.AuthRepository5com.scanner3d.app.repository.AuthRepository.Companion3com.scanner3d.app.repository.CloudStorageRepository=com.scanner3d.app.repository.CloudStorageRepository.CompanionEcom.scanner3d.app.repository.CloudStorageRepository.CloudStorageUsage+com.scanner3d.app.repository.ScanRepository5com.scanner3d.app.repository.ScanRepository.Companion5com.scanner3d.app.repository.ScanRepository.ScanStats0com.scanner3d.app.ui.auth.AuthenticationActivity%com.scanner3d.app.ui.custom.DepthView*com.scanner3d.app.ui.custom.PointCloudView=com.scanner3d.app.ui.custom.PointCloudView.PointCloudRenderer,com.scanner3d.app.ui.gallery.GalleryActivity+com.scanner3d.app.ui.gallery.GalleryAdapter4com.scanner3d.app.ui.gallery.GalleryAdapter.ViewMode5com.scanner3d.app.ui.gallery.GalleryAdapter.Companion:com.scanner3d.app.ui.gallery.GalleryAdapter.GridViewHolder:com.scanner3d.app.ui.gallery.GalleryAdapter.ListViewHolder<com.scanner3d.app.ui.gallery.GalleryAdapter.ScanDiffCallback.com.scanner3d.app.ui.scanning.ScanningActivity8com.scanner3d.app.ui.scanning.ScanningActivity.Companion)com.scanner3d.app.utils.EncryptionManager3com.scanner3d.app.utils.EncryptionManager.Companion7com.scanner3d.app.utils.EncryptionManager.EncryptedData#com.scanner3d.app.utils.FileManager-com.scanner3d.app.utils.FileManager.Companion/com.scanner3d.app.utils.FileManager.StorageInfo%com.scanner3d.app.utils.MemoryManager/com.scanner3d.app.utils.MemoryManager.Companion0com.scanner3d.app.utils.MemoryManager.MemoryInfo0com.scanner3d.app.utils.MemoryManager.CacheStats/com.scanner3d.app.utils.MemoryManager.TypeStats7com.scanner3d.app.utils.MemoryManager.MemoryUsageReport2com.scanner3d.app.utils.MemoryManager.MemoryObject6com.scanner3d.app.utils.MemoryManager.MemoryObjectType$com.scanner3d.app.utils.MeshExporter.com.scanner3d.app.utils.MeshExporter.Companioncom.scanner3d.app.utils.Point%com.scanner3d.app.utils.MeshGenerator/com.scanner3d.app.utils.MeshGenerator.Companion3com.scanner3d.app.utils.MeshGenerator.Triangulation&com.scanner3d.app.utils.MeshSimplifier0com.scanner3d.app.utils.MeshSimplifier.Companion+com.scanner3d.app.utils.MeshSimplifier.Edge6com.scanner3d.app.utils.MeshSimplifier.VertexAdjacency4com.scanner3d.app.utils.MeshSimplifier.QuadricMatrix>com.scanner3d.app.utils.MeshSimplifier.QuadricMatrix.Companion)com.scanner3d.app.utils.TextureCompressor3com.scanner3d.app.utils.TextureCompressor.Companion;com.scanner3d.app.utils.TextureCompressor.CompressionFormat<com.scanner3d.app.utils.TextureCompressor.CompressionQuality;com.scanner3d.app.utils.TextureCompressor.CompressedTexture6com.scanner3d.app.utils.TextureCompressor.TextureAtlas7com.scanner3d.app.utils.TextureCompressor.PackedTexture8com.scanner3d.app.utils.TextureCompressor.IndexedTexture3com.scanner3d.app.utils.TextureCompressor.Rectangle%com.scanner3d.app.utils.TextureMapper/com.scanner3d.app.utils.TextureMapper.Companion*com.scanner3d.app.utils.ThumbnailGenerator4com.scanner3d.app.utils.ThumbnailGenerator.Companion)com.scanner3d.app.viewmodel.AuthViewModel4com.scanner3d.app.viewmodel.AuthViewModel.AuthResult,com.scanner3d.app.viewmodel.GalleryViewModel6com.scanner3d.app.viewmodel.GalleryViewModel.ScanStats3com.scanner3d.app.viewmodel.GalleryViewModel.SortBy5com.scanner3d.app.viewmodel.GalleryViewModel.FilterBy)com.scanner3d.app.viewmodel.MainViewModel-com.scanner3d.app.viewmodel.ScanningViewModel;com.scanner3d.app.databinding.ActivityAuthenticationBindingcom.scanner3d.app.BuildConfig1com.scanner3d.app.databinding.ItemScanListBinding1com.scanner3d.app.databinding.ActivityMainBinding1com.scanner3d.app.databinding.ItemScanGridBinding4com.scanner3d.app.databinding.ActivityGalleryBinding5com.scanner3d.app.databinding.ActivityScanningBinding.com.scanner3d.app.ui.model.ModelViewerActivity0com.scanner3d.app.viewmodel.ModelViewerViewModel8com.scanner3d.app.databinding.ActivityModelViewerBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                