{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-62:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "44,45,46,47,48,49,50,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3885,3979,4081,4178,4275,4376,4476,11166", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3974,4076,4173,4270,4371,4471,4577,11262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,252,370,499,622,753,867,994,1088,1231,1373", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "156,247,365,494,617,748,862,989,1083,1226,1368,1481"}, "to": {"startLines": "54,55,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4868,4974,5218,5336,5465,5588,5719,5833,5960,6054,6197,6339", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "4969,5060,5331,5460,5583,5714,5828,5955,6049,6192,6334,6447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1082,1170,1240,1303,1395,1458,1518,1577,1640,1701,1755,1857,1914,1973,2027,2095,2206,2287,2369,2501,2572,2645,2769,2857,2933,2986,3040,3106,3179,3255,3341,3419,3489,3564,3646,3714,3815,3900,3970,4060,4151,4225,4298,4387,4438,4519,4586,4668,4753,4815,4879,4942,5010,5104,5199,5289,5386,5443,5501", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "306,383,458,535,635,726,819,932,1012,1077,1165,1235,1298,1390,1453,1513,1572,1635,1696,1750,1852,1909,1968,2022,2090,2201,2282,2364,2496,2567,2640,2764,2852,2928,2981,3035,3101,3174,3250,3336,3414,3484,3559,3641,3709,3810,3895,3965,4055,4146,4220,4293,4382,4433,4514,4581,4663,4748,4810,4874,4937,5005,5099,5194,5284,5381,5438,5496,5571"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3465,3542,3617,3694,3794,4582,4675,4788,5065,5130,6452,6522,6585,6677,6740,6800,6859,6922,6983,7037,7139,7196,7255,7309,7377,7488,7569,7651,7783,7854,7927,8051,8139,8215,8268,8322,8388,8461,8537,8623,8701,8771,8846,8928,8996,9097,9182,9252,9342,9433,9507,9580,9669,9720,9801,9868,9950,10035,10097,10161,10224,10292,10386,10481,10571,10668,10725,11009", "endLines": "6,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "356,3537,3612,3689,3789,3880,4670,4783,4863,5125,5213,6517,6580,6672,6735,6795,6854,6917,6978,7032,7134,7191,7250,7304,7372,7483,7564,7646,7778,7849,7922,8046,8134,8210,8263,8317,8383,8456,8532,8618,8696,8766,8841,8923,8991,9092,9177,9247,9337,9428,9502,9575,9664,9715,9796,9863,9945,10030,10092,10156,10219,10287,10381,10476,10566,10663,10720,10778,11079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "809,914,1014,1122,1206,1308,1424,1503,1581,1672,1766,1860,1954,2054,2147,2242,2335,2426,2518,2599,2704,2807,2905,3010,3112,3214,3368,11084", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "909,1009,1117,1201,1303,1419,1498,1576,1667,1761,1855,1949,2049,2142,2237,2330,2421,2513,2594,2699,2802,2900,3005,3107,3209,3363,3460,11161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,119", "endOffsets": "156,276"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "10783,10889", "endColumns": "105,119", "endOffsets": "10884,11004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,283,403,524", "endColumns": "45,46,119,120,93", "endOffsets": "235,282,402,523,617"}, "to": {"startLines": "7,8,9,10,11", "startColumns": "4,4,4,4,4", "startOffsets": "361,411,462,586,711", "endColumns": "49,50,123,124,97", "endOffsets": "406,457,581,706,804"}}]}]}