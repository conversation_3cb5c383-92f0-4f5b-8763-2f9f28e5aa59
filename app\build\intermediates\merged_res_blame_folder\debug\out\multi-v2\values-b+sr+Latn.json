{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1369", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1364,1482"}, "to": {"startLines": "54,55,76,78,79,96,97,156,157,158,159,161,162,164,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5002,5099,7583,7773,7874,9775,9852,14633,14725,14810,14882,15033,15113,15285,15459,15529", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,69,117", "endOffsets": "5094,5181,7675,7869,7955,9847,9938,14720,14805,14877,14948,15108,15193,15353,15524,15642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "154,155", "startColumns": "4,4", "startOffsets": "14403,14510", "endColumns": "106,122", "endOffsets": "14505,14628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "64", "startColumns": "4", "startOffsets": "6183", "endColumns": "129", "endOffsets": "6308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1038,1138,1277,1410", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1033,1133,1272,1405,1531"}, "to": {"startLines": "74,77,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7361,7680,8430,8551,8685,8816,8943,9074,9209,9309,9448,9581", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "7467,7768,8546,8680,8811,8938,9069,9204,9304,9443,9576,9702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5186,5293,5451,5578,5688,5842,5969,6081,6313,6462,6569,6729,6856,7005,7148,7216,7281", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "5288,5446,5573,5683,5837,5964,6076,6178,6457,6564,6724,6851,7000,7143,7211,7276,7356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1133,1226,1294,1357,1460,1520,1586,1642,1713,1773,1827,1939,1996,2057,2111,2187,2312,2399,2482,2621,2703,2786,2917,3005,3083,3137,3193,3259,3333,3411,3500,3582,3658,3734,3809,3881,3988,4078,4151,4243,4339,4411,4487,4583,4636,4718,4785,4872,4959,5021,5085,5148,5217,5322,5432,5528,5636,5694,5754", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "319,396,473,553,661,755,849,981,1062,1128,1221,1289,1352,1455,1515,1581,1637,1708,1768,1822,1934,1991,2052,2106,2182,2307,2394,2477,2616,2698,2781,2912,3000,3078,3132,3188,3254,3328,3406,3495,3577,3653,3729,3804,3876,3983,4073,4146,4238,4334,4406,4482,4578,4631,4713,4780,4867,4954,5016,5080,5143,5212,5317,5427,5523,5631,5689,5749,5829"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,80,81,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3533,3610,3687,3767,3875,4695,4789,4921,7960,8026,9707,9943,10006,10109,10169,10235,10291,10362,10422,10476,10588,10645,10706,10760,10836,10961,11048,11131,11270,11352,11435,11566,11654,11732,11786,11842,11908,11982,12060,12149,12231,12307,12383,12458,12530,12637,12727,12800,12892,12988,13060,13136,13232,13285,13367,13434,13521,13608,13670,13734,13797,13866,13971,14081,14177,14285,14343,14953", "endLines": "6,39,40,41,42,43,51,52,53,80,81,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,160", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "369,3605,3682,3762,3870,3964,4784,4916,4997,8021,8114,9770,10001,10104,10164,10230,10286,10357,10417,10471,10583,10640,10701,10755,10831,10956,11043,11126,11265,11347,11430,11561,11649,11727,11781,11837,11903,11977,12055,12144,12226,12302,12378,12453,12525,12632,12722,12795,12887,12983,13055,13131,13227,13280,13362,13429,13516,13603,13665,13729,13792,13861,13966,14076,14172,14280,14338,14398,15028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "197,244,294,406,520", "endColumns": "46,49,111,113,84", "endOffsets": "243,293,405,519,604"}, "to": {"startLines": "7,8,9,10,11", "startColumns": "4,4,4,4,4", "startOffsets": "374,425,479,595,713", "endColumns": "50,53,115,117,88", "endOffsets": "420,474,590,708,797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,909,1010,1116,1202,1306,1428,1513,1595,1686,1779,1874,1968,2068,2161,2256,2361,2452,2543,2629,2734,2840,2943,3050,3159,3266,3436,15198", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "904,1005,1111,1197,1301,1423,1508,1590,1681,1774,1869,1963,2063,2156,2251,2356,2447,2538,2624,2729,2835,2938,3045,3154,3261,3431,3528,15280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "75,82,83,84", "startColumns": "4,4,4,4", "startOffsets": "7472,8119,8219,8332", "endColumns": "110,99,112,97", "endOffsets": "7578,8214,8327,8425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "44,45,46,47,48,49,50,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3969,4067,4169,4266,4370,4474,4579,15358", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4062,4164,4261,4365,4469,4574,4690,15454"}}]}]}