1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scanner3d.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission
14-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
17    <uses-permission
17-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
25    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
26    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
27
28    <!-- Camera features -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-23:35
30        android:name="android.hardware.camera"
30-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:9-47
31        android:required="true" />
31-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:23:9-32
32    <uses-feature
32-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:24:5-26:35
33        android:name="android.hardware.camera.autofocus"
33-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:9-57
34        android:required="true" />
34-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-32
35    <uses-feature
35-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:5-29:36
36        android:name="android.hardware.camera.flash"
36-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:9-53
37        android:required="false" />
37-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-33
38
39    <!-- ARCore features -->
40    <uses-feature
40-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:5-34:35
41        android:name="android.hardware.camera.ar"
41-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-50
42        android:required="true" />
42-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:34:9-32
43
44    <!-- OpenGL ES 3.0 -->
45    <uses-feature
45-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:5-39:35
46        android:glEsVersion="0x00030000"
46-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-41
47        android:required="true" />
47-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:39:9-32
48
49    <!-- Sensors -->
50    <uses-feature
50-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:5-44:35
51        android:name="android.hardware.sensor.accelerometer"
51-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-61
52        android:required="true" />
52-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:44:9-32
53    <uses-feature
53-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:45:5-47:35
54        android:name="android.hardware.sensor.gyroscope"
54-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:9-57
55        android:required="true" />
55-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-32
56
57    <queries>
57-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
58        <intent>
58-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
59            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
60        </intent>
61
62        <package android:name="com.google.ar.core" />
62-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
62-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
63        <package android:name="com.android.vending" />
63-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
63-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
64
65        <intent>
65-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
66            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
67        </intent>
68    </queries>
69
70    <permission
70-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
71        android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
71-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
72        android:protectionLevel="signature" />
72-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
73
74    <uses-permission android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
75
76    <application
76-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
77        android:allowBackup="true"
77-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-35
78        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
78-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
79        android:dataExtractionRules="@xml/data_extraction_rules"
79-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-65
80        android:debuggable="true"
81        android:extractNativeLibs="false"
82        android:fullBackupContent="@xml/backup_rules"
82-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:52:9-54
83        android:hardwareAccelerated="true"
83-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-43
84        android:icon="@mipmap/ic_launcher"
84-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:9-43
85        android:label="@string/app_name"
85-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-41
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-54
87        android:supportsRtl="true"
87-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-35
88        android:theme="@style/Theme.Scanner3D" >
88-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-47
89
90        <!-- ARCore metadata -->
91        <meta-data
91-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-64:40
92            android:name="com.google.ar.core"
92-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:13-46
93            android:value="required" />
93-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:64:13-37
94
95        <!-- Main Activity -->
96        <activity
96-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:9-76:20
97            android:name="com.scanner3d.app.MainActivity"
97-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-41
98            android:exported="true"
98-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:69:13-36
99            android:screenOrientation="portrait"
99-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:70:13-49
100            android:theme="@style/Theme.Scanner3D.NoActionBar" >
100-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:13-63
101            <intent-filter>
101-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-75:29
102                <action android:name="android.intent.action.MAIN" />
102-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:17-69
102-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:25-66
103
104                <category android:name="android.intent.category.LAUNCHER" />
104-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:17-77
104-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:27-74
105            </intent-filter>
106        </activity>
107
108        <!-- Scanning Activity -->
109        <activity
109-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:79:9-83:66
110            android:name="com.scanner3d.app.ui.scanning.ScanningActivity"
110-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:80:13-57
111            android:exported="false"
111-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:81:13-37
112            android:screenOrientation="portrait"
112-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:82:13-49
113            android:theme="@style/Theme.Scanner3D.NoActionBar" />
113-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:13-63
114
115        <!-- Model Viewer Activity -->
116        <activity
116-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:9-90:66
117            android:name="com.scanner3d.app.ui.model.ModelViewerActivity"
117-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-57
118            android:exported="false"
118-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:88:13-37
119            android:screenOrientation="portrait"
119-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:89:13-49
120            android:theme="@style/Theme.Scanner3D.NoActionBar" />
120-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:13-63
121
122        <!-- Gallery Activity -->
123        <activity
123-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:9-97:66
124            android:name="com.scanner3d.app.ui.gallery.GalleryActivity"
124-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-55
125            android:exported="false"
125-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:95:13-37
126            android:screenOrientation="portrait"
126-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:96:13-49
127            android:theme="@style/Theme.Scanner3D.NoActionBar" />
127-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:13-63
128
129        <!-- Authentication Activity -->
130        <activity
130-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:9-104:66
131            android:name="com.scanner3d.app.ui.auth.AuthenticationActivity"
131-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-59
132            android:exported="false"
132-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:102:13-37
133            android:screenOrientation="portrait"
133-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:103:13-49
134            android:theme="@style/Theme.Scanner3D.NoActionBar" />
134-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:13-63
135
136        <!-- File Provider for sharing files -->
137        <provider
138            android:name="androidx.core.content.FileProvider"
138-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-62
139            android:authorities="com.scanner3d.app.fileprovider"
139-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:109:13-64
140            android:exported="false"
140-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:110:13-37
141            android:grantUriPermissions="true" >
141-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:13-47
142            <meta-data
142-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-114:54
143                android:name="android.support.FILE_PROVIDER_PATHS"
143-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:17-67
144                android:resource="@xml/file_paths" />
144-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:17-51
145        </provider>
146
147        <activity
147-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
148            android:name="com.karumi.dexter.DexterActivity"
148-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
149            android:theme="@style/Dexter.Internal.Theme.Transparent" />
149-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
150
151        <uses-library
151-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
152            android:name="androidx.camera.extensions.impl"
152-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
153            android:required="false" />
153-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
154
155        <service
155-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
156            android:name="androidx.camera.core.impl.MetadataHolderService"
156-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
157            android:enabled="false"
157-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
158            android:exported="false" >
158-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
159            <meta-data
159-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
160                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
160-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
161                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
161-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
162        </service>
163
164        <provider
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
165            android:name="androidx.startup.InitializationProvider"
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
166            android:authorities="com.scanner3d.app.androidx-startup"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
167            android:exported="false" >
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
168            <meta-data
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.emoji2.text.EmojiCompatInitializer"
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
170                android:value="androidx.startup" />
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
172-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
173                android:value="androidx.startup" />
173-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
175-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
176                android:value="androidx.startup" />
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
177        </provider>
178
179        <uses-library
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
180            android:name="androidx.window.extensions"
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
181            android:required="false" />
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
182        <uses-library
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
183            android:name="androidx.window.sidecar"
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
184            android:required="false" />
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
185
186        <service
186-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
187            android:name="androidx.room.MultiInstanceInvalidationService"
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
188            android:directBootAware="true"
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
189            android:exported="false" /> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
190        <meta-data
190-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
191            android:name="com.google.ar.core.min_apk_version"
191-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
192            android:value="232620000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
192-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
193        <activity
193-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
194            android:name="com.google.ar.core.InstallActivity"
194-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
195            android:configChanges="keyboardHidden|orientation|screenSize"
195-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
196            android:excludeFromRecents="true"
196-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
197            android:exported="false"
197-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
198            android:launchMode="singleTop"
198-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
199            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
199-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
200
201        <receiver
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
202            android:name="androidx.profileinstaller.ProfileInstallReceiver"
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
203            android:directBootAware="false"
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
204            android:enabled="true"
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
205            android:exported="true"
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
206            android:permission="android.permission.DUMP" >
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
208                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
211                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
214                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
217                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
218            </intent-filter>
219        </receiver>
220    </application>
221
222</manifest>
