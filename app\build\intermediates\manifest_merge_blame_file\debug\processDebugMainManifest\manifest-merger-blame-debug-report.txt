1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scanner3d.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission
14-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
17    <uses-permission
17-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
25    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
26    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
27
28    <!-- Camera features -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-23:35
30        android:name="android.hardware.camera"
30-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:9-47
31        android:required="true" />
31-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:23:9-32
32    <uses-feature
32-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:24:5-26:35
33        android:name="android.hardware.camera.autofocus"
33-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:9-57
34        android:required="true" />
34-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-32
35    <uses-feature
35-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:5-29:36
36        android:name="android.hardware.camera.flash"
36-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:9-53
37        android:required="false" />
37-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-33
38
39    <!-- ARCore features -->
40    <uses-feature
40-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:5-34:35
41        android:name="android.hardware.camera.ar"
41-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-50
42        android:required="true" />
42-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:34:9-32
43
44    <!-- OpenGL ES 3.0 -->
45    <uses-feature
45-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:5-39:35
46        android:glEsVersion="0x00030000"
46-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-41
47        android:required="true" />
47-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:39:9-32
48
49    <!-- Sensors -->
50    <uses-feature
50-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:5-44:35
51        android:name="android.hardware.sensor.accelerometer"
51-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-61
52        android:required="true" />
52-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:44:9-32
53    <uses-feature
53-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:45:5-47:35
54        android:name="android.hardware.sensor.gyroscope"
54-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:9-57
55        android:required="true" />
55-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-32
56
57    <queries>
57-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
58        <intent>
58-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
59            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
60        </intent>
61
62        <package android:name="com.google.ar.core" />
62-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
62-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
63        <package android:name="com.android.vending" />
63-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
63-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
64
65        <intent>
65-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
66            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
67        </intent>
68    </queries>
69
70    <permission
70-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
71        android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
71-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
72        android:protectionLevel="signature" />
72-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
73
74    <uses-permission android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
75
76    <application
76-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
77        android:allowBackup="true"
77-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-35
78        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
78-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
79        android:dataExtractionRules="@xml/data_extraction_rules"
79-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-65
80        android:debuggable="true"
81        android:extractNativeLibs="false"
82        android:fullBackupContent="@xml/backup_rules"
82-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:52:9-54
83        android:hardwareAccelerated="true"
83-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-43
84        android:icon="@mipmap/ic_launcher"
84-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:9-43
85        android:label="@string/app_name"
85-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-41
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-54
87        android:supportsRtl="true"
87-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-35
88        android:testOnly="true"
89        android:theme="@style/Theme.Scanner3D" >
89-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-47
90
91        <!-- ARCore metadata -->
92        <meta-data
92-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-64:40
93            android:name="com.google.ar.core"
93-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:13-46
94            android:value="required" />
94-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:64:13-37
95
96        <!-- Main Activity -->
97        <activity
97-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:9-76:20
98            android:name="com.scanner3d.app.MainActivity"
98-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-41
99            android:exported="true"
99-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:69:13-36
100            android:screenOrientation="portrait"
100-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:70:13-49
101            android:theme="@style/Theme.Scanner3D.NoActionBar" >
101-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:13-63
102            <intent-filter>
102-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-75:29
103                <action android:name="android.intent.action.MAIN" />
103-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:17-69
103-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:25-66
104
105                <category android:name="android.intent.category.LAUNCHER" />
105-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:17-77
105-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:27-74
106            </intent-filter>
107        </activity>
108
109        <!-- Scanning Activity -->
110        <activity
110-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:79:9-83:66
111            android:name="com.scanner3d.app.ui.scanning.ScanningActivity"
111-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:80:13-57
112            android:exported="false"
112-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:81:13-37
113            android:screenOrientation="portrait"
113-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:82:13-49
114            android:theme="@style/Theme.Scanner3D.NoActionBar" />
114-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:13-63
115
116        <!-- Model Viewer Activity -->
117        <activity
117-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:9-90:66
118            android:name="com.scanner3d.app.ui.model.ModelViewerActivity"
118-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-57
119            android:exported="false"
119-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:88:13-37
120            android:screenOrientation="portrait"
120-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:89:13-49
121            android:theme="@style/Theme.Scanner3D.NoActionBar" />
121-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:13-63
122
123        <!-- Gallery Activity -->
124        <activity
124-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:9-97:66
125            android:name="com.scanner3d.app.ui.gallery.GalleryActivity"
125-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-55
126            android:exported="false"
126-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:95:13-37
127            android:screenOrientation="portrait"
127-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:96:13-49
128            android:theme="@style/Theme.Scanner3D.NoActionBar" />
128-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:13-63
129
130        <!-- Authentication Activity -->
131        <activity
131-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:9-104:66
132            android:name="com.scanner3d.app.ui.auth.AuthenticationActivity"
132-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-59
133            android:exported="false"
133-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:102:13-37
134            android:screenOrientation="portrait"
134-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:103:13-49
135            android:theme="@style/Theme.Scanner3D.NoActionBar" />
135-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:13-63
136
137        <!-- File Provider for sharing files -->
138        <provider
139            android:name="androidx.core.content.FileProvider"
139-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-62
140            android:authorities="com.scanner3d.app.fileprovider"
140-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:109:13-64
141            android:exported="false"
141-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:110:13-37
142            android:grantUriPermissions="true" >
142-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:13-47
143            <meta-data
143-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-114:54
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:17-67
145                android:resource="@xml/file_paths" />
145-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:17-51
146        </provider>
147
148        <activity
148-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
149            android:name="com.karumi.dexter.DexterActivity"
149-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
150            android:theme="@style/Dexter.Internal.Theme.Transparent" />
150-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
151
152        <uses-library
152-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
153            android:name="androidx.camera.extensions.impl"
153-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
154            android:required="false" />
154-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
155
156        <service
156-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
157            android:name="androidx.camera.core.impl.MetadataHolderService"
157-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
158            android:enabled="false"
158-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
159            android:exported="false" >
159-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
160            <meta-data
160-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
161                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
161-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
162                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
162-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
163        </service>
164
165        <provider
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
166            android:name="androidx.startup.InitializationProvider"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
167            android:authorities="com.scanner3d.app.androidx-startup"
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
168            android:exported="false" >
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
169            <meta-data
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.emoji2.text.EmojiCompatInitializer"
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
171                android:value="androidx.startup" />
171-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
173-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
174                android:value="androidx.startup" />
174-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
175            <meta-data
175-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
177                android:value="androidx.startup" />
177-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
178        </provider>
179
180        <uses-library
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
181            android:name="androidx.window.extensions"
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
182            android:required="false" />
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
183        <uses-library
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
184            android:name="androidx.window.sidecar"
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
185            android:required="false" />
185-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
186
187        <service
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
188            android:name="androidx.room.MultiInstanceInvalidationService"
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
189            android:directBootAware="true"
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
190            android:exported="false" /> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
191        <meta-data
191-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
192            android:name="com.google.ar.core.min_apk_version"
192-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
193            android:value="232620000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
193-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
194        <activity
194-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
195            android:name="com.google.ar.core.InstallActivity"
195-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
196            android:configChanges="keyboardHidden|orientation|screenSize"
196-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
197            android:excludeFromRecents="true"
197-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
198            android:exported="false"
198-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
199            android:launchMode="singleTop"
199-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
200            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
200-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
201
202        <receiver
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
203            android:name="androidx.profileinstaller.ProfileInstallReceiver"
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
204            android:directBootAware="false"
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
205            android:enabled="true"
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
206            android:exported="true"
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
207            android:permission="android.permission.DUMP" >
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
209                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
212                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
215                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
218                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
219            </intent-filter>
220        </receiver>
221    </application>
222
223</manifest>
