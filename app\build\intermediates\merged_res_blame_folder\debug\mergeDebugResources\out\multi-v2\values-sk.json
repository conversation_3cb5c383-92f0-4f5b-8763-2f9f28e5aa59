{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,378,527,663,792,927,1060,1158,1293,1426", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "156,246,373,522,658,787,922,1055,1153,1288,1421,1535"}, "to": {"startLines": "55,56,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5015,5121,5375,5502,5651,5787,5916,6051,6184,6282,6417,6550", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "5116,5206,5497,5646,5782,5911,6046,6179,6277,6412,6545,6659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,290,404,520", "endColumns": "46,52,113,115,83", "endOffsets": "236,289,403,519,603"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "428,479,536,654,774", "endColumns": "50,56,117,119,87", "endOffsets": "474,531,649,769,857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "45,46,47,48,49,50,51,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3977,4073,4175,4276,4374,4484,4592,11564", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4068,4170,4271,4369,4479,4587,4709,11660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,126", "endOffsets": "156,283"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11169,11275", "endColumns": "105,126", "endOffsets": "11270,11397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2935,3023,3103,3157,3208,3274,3346,3423,3506,3588,3660,3737,3810,3881,3986,4074,4146,4238,4334,4408,4482,4578,4630,4712,4779,4866,4953,5015,5079,5142,5210,5316,5423,5521,5638,5696,5751", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2930,3018,3098,3152,3203,3269,3341,3418,3501,3583,3655,3732,3805,3876,3981,4069,4141,4233,4329,4403,4477,4573,4625,4707,4774,4861,4948,5010,5074,5137,5205,5311,5418,5516,5633,5691,5746,5825"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3574,3649,3724,3802,3894,4714,4806,4934,5211,5276,6664,6740,6805,6895,6959,7025,7079,7148,7208,7262,7379,7439,7501,7555,7627,7757,7844,7936,8075,8144,8222,8353,8441,8521,8575,8626,8692,8764,8841,8924,9006,9078,9155,9228,9299,9404,9492,9564,9656,9752,9826,9900,9996,10048,10130,10197,10284,10371,10433,10497,10560,10628,10734,10841,10939,11056,11114,11402", "endLines": "7,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "423,3644,3719,3797,3889,3972,4801,4929,5010,5271,5370,6735,6800,6890,6954,7020,7074,7143,7203,7257,7374,7434,7496,7550,7622,7752,7839,7931,8070,8139,8217,8348,8436,8516,8570,8621,8687,8759,8836,8919,9001,9073,9150,9223,9294,9399,9487,9559,9651,9747,9821,9895,9991,10043,10125,10192,10279,10366,10428,10492,10555,10623,10729,10836,10934,11051,11109,11164,11476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "862,969,1070,1181,1267,1375,1493,1572,1649,1740,1833,1931,2025,2125,2218,2313,2411,2502,2593,2677,2782,2890,2989,3095,3207,3310,3476,11481", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "964,1065,1176,1262,1370,1488,1567,1644,1735,1828,1926,2020,2120,2213,2308,2406,2497,2588,2672,2777,2885,2984,3090,3202,3305,3471,3569,11559"}}]}]}