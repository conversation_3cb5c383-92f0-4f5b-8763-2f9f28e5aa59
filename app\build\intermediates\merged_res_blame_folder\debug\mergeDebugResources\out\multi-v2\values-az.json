{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,257,378,508,636,757,877,1024,1118,1248,1378", "endColumns": "112,88,120,129,127,120,119,146,93,129,129,119", "endOffsets": "163,252,373,503,631,752,872,1019,1113,1243,1373,1493"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4898,5011,5258,5379,5509,5637,5758,5878,6025,6119,6249,6379", "endColumns": "112,88,120,129,127,120,119,146,93,129,129,119", "endOffsets": "5006,5095,5374,5504,5632,5753,5873,6020,6114,6244,6374,6494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1038,1132,1200,1259,1354,1417,1481,1540,1607,1670,1724,1839,1897,1959,2013,2084,2216,2300,2380,2514,2590,2666,2795,2879,2958,3015,3066,3132,3202,3280,3363,3443,3513,3589,3667,3738,3836,3922,4005,4098,4191,4264,4336,4430,4484,4568,4635,4719,4807,4871,4936,5000,5070,5172,5276,5372,5473,5534,5589", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,128,83,78,56,50,65,69,77,82,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79", "endOffsets": "260,348,433,513,598,677,773,889,969,1033,1127,1195,1254,1349,1412,1476,1535,1602,1665,1719,1834,1892,1954,2008,2079,2211,2295,2375,2509,2585,2661,2790,2874,2953,3010,3061,3127,3197,3275,3358,3438,3508,3584,3662,3733,3831,3917,4000,4093,4186,4259,4331,4425,4479,4563,4630,4714,4802,4866,4931,4995,5065,5167,5271,5367,5468,5529,5584,5664"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3462,3550,3635,3715,3800,4606,4702,4818,5100,5164,6499,6567,6626,6721,6784,6848,6907,6974,7037,7091,7206,7264,7326,7380,7451,7583,7667,7747,7881,7957,8033,8162,8246,8325,8382,8433,8499,8569,8647,8730,8810,8880,8956,9034,9105,9203,9289,9372,9465,9558,9631,9703,9797,9851,9935,10002,10086,10174,10238,10303,10367,10437,10539,10643,10739,10840,10901,11182", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,128,83,78,56,50,65,69,77,82,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79", "endOffsets": "310,3545,3630,3710,3795,3874,4697,4813,4893,5159,5253,6562,6621,6716,6779,6843,6902,6969,7032,7086,7201,7259,7321,7375,7446,7578,7662,7742,7876,7952,8028,8157,8241,8320,8377,8428,8494,8564,8642,8725,8805,8875,8951,9029,9100,9198,9284,9367,9460,9553,9626,9698,9792,9846,9930,9997,10081,10169,10233,10298,10362,10432,10534,10638,10734,10835,10896,10951,11257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,293,405,523", "endColumns": "49,52,111,117,85", "endOffsets": "239,292,404,522,608"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "315,369,426,542,664", "endColumns": "53,56,115,121,89", "endOffsets": "364,421,537,659,749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "754,864,965,1075,1163,1270,1384,1466,1544,1635,1728,1822,1921,2021,2114,2209,2303,2394,2486,2571,2676,2782,2882,2991,3096,3198,3356,11262", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "859,960,1070,1158,1265,1379,1461,1539,1630,1723,1817,1916,2016,2109,2204,2298,2389,2481,2566,2671,2777,2877,2986,3091,3193,3351,3457,11341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3879,3980,4082,4185,4289,4390,4495,11346", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3975,4077,4180,4284,4385,4490,4601,11442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\857ae526a5ee6c76b63616ddc978cbae\\transformed\\navigation-ui-2.7.5\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,118", "endOffsets": "157,276"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10956,11063", "endColumns": "106,118", "endOffsets": "11058,11177"}}]}]}