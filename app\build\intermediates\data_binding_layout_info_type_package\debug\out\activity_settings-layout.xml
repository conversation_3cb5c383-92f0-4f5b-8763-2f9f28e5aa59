<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="353" endOffset="12"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="21" endOffset="75"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="24" startOffset="8" endLine="30" endOffset="39"/></Target><Target id="@+id/switchHighQuality" view="Switch"><Expressions/><location startLine="48" startOffset="12" endLine="54" endOffset="41"/></Target><Target id="@+id/switchAutoFocus" view="Switch"><Expressions/><location startLine="56" startOffset="12" endLine="62" endOffset="41"/></Target><Target id="@+id/switchFlashlight" view="Switch"><Expressions/><location startLine="64" startOffset="12" endLine="70" endOffset="41"/></Target><Target id="@+id/seekBarResolution" view="SeekBar"><Expressions/><location startLine="104" startOffset="16" endLine="110" endOffset="42"/></Target><Target id="@+id/tvResolutionValue" view="TextView"><Expressions/><location startLine="112" startOffset="16" endLine="120" endOffset="45"/></Target><Target id="@+id/seekBarQuality" view="SeekBar"><Expressions/><location startLine="138" startOffset="16" endLine="144" endOffset="42"/></Target><Target id="@+id/tvQualityValue" view="TextView"><Expressions/><location startLine="146" startOffset="16" endLine="154" endOffset="45"/></Target><Target id="@+id/switchBiometric" view="Switch"><Expressions/><location startLine="176" startOffset="12" endLine="182" endOffset="41"/></Target><Target id="@+id/switchAutoLock" view="Switch"><Expressions/><location startLine="184" startOffset="12" endLine="190" endOffset="41"/></Target><Target id="@+id/tvCacheSize" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="228" endOffset="49"/></Target><Target id="@+id/tvDataSize" view="TextView"><Expressions/><location startLine="230" startOffset="20" endLine="236" endOffset="49"/></Target><Target id="@+id/tvTotalSize" view="TextView"><Expressions/><location startLine="238" startOffset="20" endLine="245" endOffset="50"/></Target><Target id="@+id/btnClearCache" view="Button"><Expressions/><location startLine="258" startOffset="16" endLine="266" endOffset="45"/></Target><Target id="@+id/btnExportData" view="Button"><Expressions/><location startLine="268" startOffset="16" endLine="277" endOffset="45"/></Target><Target id="@+id/btnImportData" view="Button"><Expressions/><location startLine="279" startOffset="16" endLine="287" endOffset="45"/></Target><Target id="@+id/switchDeveloperMode" view="Switch"><Expressions/><location startLine="309" startOffset="12" endLine="315" endOffset="41"/></Target><Target id="@+id/llDeveloperOptions" view="LinearLayout"><Expressions/><location startLine="318" startOffset="12" endLine="333" endOffset="26"/></Target><Target id="@+id/switchDebugLogging" view="Switch"><Expressions/><location startLine="325" startOffset="16" endLine="331" endOffset="45"/></Target><Target id="@+id/btnResetSettings" view="Button"><Expressions/><location startLine="336" startOffset="12" endLine="342" endOffset="50"/></Target></Targets></Layout>