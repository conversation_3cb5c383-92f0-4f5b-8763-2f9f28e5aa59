<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_model_viewer" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\activity_model_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_model_viewer_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="298" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="66"/></Target><Target id="@+id/pointCloudView" view="com.scanner3d.app.ui.custom.PointCloudView"><Expressions/><location startLine="31" startOffset="8" endLine="38" endOffset="55"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="41" startOffset="8" endLine="49" endOffset="68"/></Target><Target id="@+id/controlButtons" view="LinearLayout"><Expressions/><location startLine="52" startOffset="8" endLine="93" endOffset="22"/></Target><Target id="@+id/btnResetView" view="ImageButton"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="45"/></Target><Target id="@+id/btnToggleWireframe" view="ImageButton"><Expressions/><location startLine="73" startOffset="12" endLine="81" endOffset="45"/></Target><Target id="@+id/btnToggleTexture" view="ImageButton"><Expressions/><location startLine="83" startOffset="12" endLine="91" endOffset="45"/></Target><Target id="@+id/infoPanel" view="LinearLayout"><Expressions/><location startLine="96" startOffset="8" endLine="269" endOffset="22"/></Target><Target id="@+id/tvScanName" view="TextView"><Expressions/><location startLine="116" startOffset="16" endLine="124" endOffset="61"/></Target><Target id="@+id/qualityIndicator" view="View"><Expressions/><location startLine="126" startOffset="16" endLine="131" endOffset="76"/></Target><Target id="@+id/iconTexture" view="ImageView"><Expressions/><location startLine="133" startOffset="16" endLine="141" endOffset="47"/></Target><Target id="@+id/iconColors" view="ImageView"><Expressions/><location startLine="143" startOffset="16" endLine="151" endOffset="47"/></Target><Target id="@+id/tvScanDate" view="TextView"><Expressions/><location startLine="155" startOffset="12" endLine="162" endOffset="52"/></Target><Target id="@+id/tvVertexCount" view="TextView"><Expressions/><location startLine="178" startOffset="20" endLine="184" endOffset="67"/></Target><Target id="@+id/tvTriangleCount" view="TextView"><Expressions/><location startLine="202" startOffset="20" endLine="208" endOffset="67"/></Target><Target id="@+id/tvFileSize" view="TextView"><Expressions/><location startLine="226" startOffset="20" endLine="232" endOffset="67"/></Target><Target id="@+id/tvQuality" view="TextView"><Expressions/><location startLine="250" startOffset="20" endLine="256" endOffset="67"/></Target><Target id="@+id/fabInfo" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="274" startOffset="4" endLine="284" endOffset="33"/></Target><Target id="@+id/fabShare" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="286" startOffset="4" endLine="296" endOffset="33"/></Target></Targets></Layout>