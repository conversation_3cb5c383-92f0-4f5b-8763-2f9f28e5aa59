{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08372b0b4714dee0e9823f116e744e60\\transformed\\navigation-ui-2.7.5\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,117", "endOffsets": "160,278"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11261,11371", "endColumns": "109,117", "endOffsets": "11366,11484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0060a6d1e9ec36933e7259d41494599\\transformed\\material-1.11.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2904,2993,3073,3134,3189,3255,3324,3401,3488,3569,3643,3719,3809,3882,3984,4069,4148,4238,4330,4404,4489,4579,4631,4715,4780,4865,4950,5012,5076,5139,5208,5325,5433,5533,5637,5702,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2899,2988,3068,3129,3184,3250,3319,3396,3483,3564,3638,3714,3804,3877,3979,4064,4143,4233,4325,4399,4484,4574,4626,4710,4775,4860,4945,5007,5071,5134,5203,5320,5428,5528,5632,5697,5756,5838"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3482,3565,3650,3735,3850,4691,4792,4933,5216,5280,6727,6797,6858,6945,7008,7072,7131,7205,7267,7321,7438,7496,7557,7611,7685,7807,7891,7987,8119,8197,8275,8404,8493,8573,8634,8689,8755,8824,8901,8988,9069,9143,9219,9309,9382,9484,9569,9648,9738,9830,9904,9989,10079,10131,10215,10280,10365,10450,10512,10576,10639,10708,10825,10933,11033,11137,11202,11489", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "310,3560,3645,3730,3845,3955,4787,4928,5012,5275,5369,6792,6853,6940,7003,7067,7126,7200,7262,7316,7433,7491,7552,7606,7680,7802,7886,7982,8114,8192,8270,8399,8488,8568,8629,8684,8750,8819,8896,8983,9064,9138,9214,9304,9377,9479,9564,9643,9733,9825,9899,9984,10074,10126,10210,10275,10360,10445,10507,10571,10634,10703,10820,10928,11028,11132,11197,11256,11566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa51a49a0fdb8d78736b292657adf1a3\\transformed\\core-1.41.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,234,284,409,537", "endColumns": "43,49,124,127,87", "endOffsets": "233,283,408,536,624"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "315,363,417,546,678", "endColumns": "47,53,128,131,91", "endOffsets": "358,412,541,673,765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad912a0b6595c0fd27017314ccf5845d\\transformed\\biometric-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,254,374,521,650,800,925,1078,1177,1319,1478", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "159,249,369,516,645,795,920,1073,1172,1314,1473,1602"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5017,5126,5374,5494,5641,5770,5920,6045,6198,6297,6439,6598", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "5121,5211,5489,5636,5765,5915,6040,6193,6292,6434,6593,6722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e90c754ff7f976aa957bdef8385fc4b1\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "770,881,990,1102,1187,1292,1409,1488,1566,1657,1750,1845,1939,2039,2132,2227,2322,2413,2504,2585,2691,2796,2894,3001,3104,3219,3380,11571", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "876,985,1097,1182,1287,1404,1483,1561,1652,1745,1840,1934,2034,2127,2222,2317,2408,2499,2580,2686,2791,2889,2996,3099,3214,3375,3477,11648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b986efbab3f35a8495809ca7b1ee567d\\transformed\\core-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3960,4060,4162,4265,4372,4476,4580,11653", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "4055,4157,4260,4367,4471,4575,4686,11749"}}]}]}