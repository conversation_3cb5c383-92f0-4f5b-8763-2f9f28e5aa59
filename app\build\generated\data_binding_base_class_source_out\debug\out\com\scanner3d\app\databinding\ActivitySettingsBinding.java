// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnClearCache;

  @NonNull
  public final Button btnExportData;

  @NonNull
  public final Button btnImportData;

  @NonNull
  public final Button btnResetSettings;

  @NonNull
  public final LinearLayout llDeveloperOptions;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final SeekBar seekBarQuality;

  @NonNull
  public final SeekBar seekBarResolution;

  @NonNull
  public final Switch switchAutoFocus;

  @NonNull
  public final Switch switchAutoLock;

  @NonNull
  public final Switch switchBiometric;

  @NonNull
  public final Switch switchDebugLogging;

  @NonNull
  public final Switch switchDeveloperMode;

  @NonNull
  public final Switch switchFlashlight;

  @NonNull
  public final Switch switchHighQuality;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCacheSize;

  @NonNull
  public final TextView tvDataSize;

  @NonNull
  public final TextView tvQualityValue;

  @NonNull
  public final TextView tvResolutionValue;

  @NonNull
  public final TextView tvTotalSize;

  private ActivitySettingsBinding(@NonNull ScrollView rootView, @NonNull Button btnClearCache,
      @NonNull Button btnExportData, @NonNull Button btnImportData,
      @NonNull Button btnResetSettings, @NonNull LinearLayout llDeveloperOptions,
      @NonNull ProgressBar progressBar, @NonNull SeekBar seekBarQuality,
      @NonNull SeekBar seekBarResolution, @NonNull Switch switchAutoFocus,
      @NonNull Switch switchAutoLock, @NonNull Switch switchBiometric,
      @NonNull Switch switchDebugLogging, @NonNull Switch switchDeveloperMode,
      @NonNull Switch switchFlashlight, @NonNull Switch switchHighQuality, @NonNull Toolbar toolbar,
      @NonNull TextView tvCacheSize, @NonNull TextView tvDataSize, @NonNull TextView tvQualityValue,
      @NonNull TextView tvResolutionValue, @NonNull TextView tvTotalSize) {
    this.rootView = rootView;
    this.btnClearCache = btnClearCache;
    this.btnExportData = btnExportData;
    this.btnImportData = btnImportData;
    this.btnResetSettings = btnResetSettings;
    this.llDeveloperOptions = llDeveloperOptions;
    this.progressBar = progressBar;
    this.seekBarQuality = seekBarQuality;
    this.seekBarResolution = seekBarResolution;
    this.switchAutoFocus = switchAutoFocus;
    this.switchAutoLock = switchAutoLock;
    this.switchBiometric = switchBiometric;
    this.switchDebugLogging = switchDebugLogging;
    this.switchDeveloperMode = switchDeveloperMode;
    this.switchFlashlight = switchFlashlight;
    this.switchHighQuality = switchHighQuality;
    this.toolbar = toolbar;
    this.tvCacheSize = tvCacheSize;
    this.tvDataSize = tvDataSize;
    this.tvQualityValue = tvQualityValue;
    this.tvResolutionValue = tvResolutionValue;
    this.tvTotalSize = tvTotalSize;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClearCache;
      Button btnClearCache = ViewBindings.findChildViewById(rootView, id);
      if (btnClearCache == null) {
        break missingId;
      }

      id = R.id.btnExportData;
      Button btnExportData = ViewBindings.findChildViewById(rootView, id);
      if (btnExportData == null) {
        break missingId;
      }

      id = R.id.btnImportData;
      Button btnImportData = ViewBindings.findChildViewById(rootView, id);
      if (btnImportData == null) {
        break missingId;
      }

      id = R.id.btnResetSettings;
      Button btnResetSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnResetSettings == null) {
        break missingId;
      }

      id = R.id.llDeveloperOptions;
      LinearLayout llDeveloperOptions = ViewBindings.findChildViewById(rootView, id);
      if (llDeveloperOptions == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.seekBarQuality;
      SeekBar seekBarQuality = ViewBindings.findChildViewById(rootView, id);
      if (seekBarQuality == null) {
        break missingId;
      }

      id = R.id.seekBarResolution;
      SeekBar seekBarResolution = ViewBindings.findChildViewById(rootView, id);
      if (seekBarResolution == null) {
        break missingId;
      }

      id = R.id.switchAutoFocus;
      Switch switchAutoFocus = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoFocus == null) {
        break missingId;
      }

      id = R.id.switchAutoLock;
      Switch switchAutoLock = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoLock == null) {
        break missingId;
      }

      id = R.id.switchBiometric;
      Switch switchBiometric = ViewBindings.findChildViewById(rootView, id);
      if (switchBiometric == null) {
        break missingId;
      }

      id = R.id.switchDebugLogging;
      Switch switchDebugLogging = ViewBindings.findChildViewById(rootView, id);
      if (switchDebugLogging == null) {
        break missingId;
      }

      id = R.id.switchDeveloperMode;
      Switch switchDeveloperMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDeveloperMode == null) {
        break missingId;
      }

      id = R.id.switchFlashlight;
      Switch switchFlashlight = ViewBindings.findChildViewById(rootView, id);
      if (switchFlashlight == null) {
        break missingId;
      }

      id = R.id.switchHighQuality;
      Switch switchHighQuality = ViewBindings.findChildViewById(rootView, id);
      if (switchHighQuality == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCacheSize;
      TextView tvCacheSize = ViewBindings.findChildViewById(rootView, id);
      if (tvCacheSize == null) {
        break missingId;
      }

      id = R.id.tvDataSize;
      TextView tvDataSize = ViewBindings.findChildViewById(rootView, id);
      if (tvDataSize == null) {
        break missingId;
      }

      id = R.id.tvQualityValue;
      TextView tvQualityValue = ViewBindings.findChildViewById(rootView, id);
      if (tvQualityValue == null) {
        break missingId;
      }

      id = R.id.tvResolutionValue;
      TextView tvResolutionValue = ViewBindings.findChildViewById(rootView, id);
      if (tvResolutionValue == null) {
        break missingId;
      }

      id = R.id.tvTotalSize;
      TextView tvTotalSize = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSize == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((ScrollView) rootView, btnClearCache, btnExportData,
          btnImportData, btnResetSettings, llDeveloperOptions, progressBar, seekBarQuality,
          seekBarResolution, switchAutoFocus, switchAutoLock, switchBiometric, switchDebugLogging,
          switchDeveloperMode, switchFlashlight, switchHighQuality, toolbar, tvCacheSize,
          tvDataSize, tvQualityValue, tvResolutionValue, tvTotalSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
