{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,108", "endOffsets": "161,270"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11190,11301", "endColumns": "110,108", "endOffsets": "11296,11405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2873,2963,3040,3097,3148,3214,3292,3375,3463,3539,3614,3693,3766,3837,3946,4040,4118,4207,4297,4371,4452,4539,4592,4671,4738,4819,4903,4965,5029,5092,5163,5271,5383,5485,5596,5657,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2868,2958,3035,3092,3143,3209,3287,3370,3458,3534,3609,3688,3761,3832,3941,4035,4113,4202,4292,4366,4447,4534,4587,4666,4733,4814,4898,4960,5024,5087,5158,5266,5378,5480,5591,5652,5707,5788"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3506,3592,3674,3751,3849,4677,4774,4896,5182,5246,6637,6716,6779,6872,6934,7000,7058,7131,7195,7251,7373,7430,7492,7548,7624,7758,7843,7929,8067,8148,8227,8351,8441,8518,8575,8626,8692,8770,8853,8941,9017,9092,9171,9244,9315,9424,9518,9596,9685,9775,9849,9930,10017,10070,10149,10216,10297,10381,10443,10507,10570,10641,10749,10861,10963,11074,11135,11410", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "314,3587,3669,3746,3844,3938,4769,4891,4972,5241,5330,6711,6774,6867,6929,6995,7053,7126,7190,7246,7368,7425,7487,7543,7619,7753,7838,7924,8062,8143,8222,8346,8436,8513,8570,8621,8687,8765,8848,8936,9012,9087,9166,9239,9310,9419,9513,9591,9680,9770,9844,9925,10012,10065,10144,10211,10292,10376,10438,10502,10565,10636,10744,10856,10958,11069,11130,11185,11486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,291,416,549", "endColumns": "49,50,124,132,85", "endOffsets": "239,290,415,548,634"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "319,373,428,557,694", "endColumns": "53,54,128,136,89", "endOffsets": "368,423,552,689,779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3943,4045,4147,4247,4347,4454,4558,11574", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "4040,4142,4242,4342,4449,4553,4672,11670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,902,1007,1114,1199,1303,1423,1501,1577,1669,1763,1858,1952,2052,2146,2242,2337,2429,2521,2603,2714,2817,2916,3031,3145,3248,3403,11491", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "897,1002,1109,1194,1298,1418,1496,1572,1664,1758,1853,1947,2047,2141,2237,2332,2424,2516,2598,2709,2812,2911,3026,3140,3243,3398,3501,11569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,260,381,519,653,782,910,1052,1150,1290,1436", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "164,255,376,514,648,777,905,1047,1145,1285,1431,1557"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4977,5091,5335,5456,5594,5728,5857,5985,6127,6225,6365,6511", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "5086,5177,5451,5589,5723,5852,5980,6122,6220,6360,6506,6632"}}]}]}