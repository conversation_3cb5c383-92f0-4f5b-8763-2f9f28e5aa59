package com.scanner3d.app.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.scanner3d.app.data.model.AppSettings;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SettingsDao_Impl implements SettingsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AppSettings> __insertionAdapterOfAppSettings;

  private final EntityDeletionOrUpdateAdapter<AppSettings> __updateAdapterOfAppSettings;

  private final SharedSQLiteStatement __preparedStmtOfUpdateScanQuality;

  private final SharedSQLiteStatement __preparedStmtOfUpdateExportFormat;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCloudSync;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAutoUpload;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTheme;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLanguage;

  private final SharedSQLiteStatement __preparedStmtOfUpdateGpuAcceleration;

  private final SharedSQLiteStatement __preparedStmtOfUpdateMaxMemoryUsage;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCacheSize;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAnalytics;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDebugMode;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastModified;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSettings;

  public SettingsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAppSettings = new EntityInsertionAdapter<AppSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `settings` (`id`,`defaultScanQuality`,`autoSaveScans`,`maxScanDuration`,`frameRate`,`enableDepthFiltering`,`enableMeshSmoothing`,`defaultExportFormat`,`enableCompression`,`compressionLevel`,`enableCloudSync`,`autoUpload`,`wifiOnlyUpload`,`cloudStorageLimit`,`theme`,`language`,`showTutorial`,`enableHapticFeedback`,`enableSoundEffects`,`maxMemoryUsage`,`cacheSize`,`enableGpuAcceleration`,`renderQuality`,`enableAnalytics`,`enableCrashReporting`,`dataRetentionDays`,`debugMode`,`enableExperimentalFeatures`,`logLevel`,`lastModified`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppSettings entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDefaultScanQuality());
        final int _tmp = entity.getAutoSaveScans() ? 1 : 0;
        statement.bindLong(3, _tmp);
        statement.bindLong(4, entity.getMaxScanDuration());
        statement.bindLong(5, entity.getFrameRate());
        final int _tmp_1 = entity.getEnableDepthFiltering() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        final int _tmp_2 = entity.getEnableMeshSmoothing() ? 1 : 0;
        statement.bindLong(7, _tmp_2);
        statement.bindString(8, entity.getDefaultExportFormat());
        final int _tmp_3 = entity.getEnableCompression() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        statement.bindLong(10, entity.getCompressionLevel());
        final int _tmp_4 = entity.getEnableCloudSync() ? 1 : 0;
        statement.bindLong(11, _tmp_4);
        final int _tmp_5 = entity.getAutoUpload() ? 1 : 0;
        statement.bindLong(12, _tmp_5);
        final int _tmp_6 = entity.getWifiOnlyUpload() ? 1 : 0;
        statement.bindLong(13, _tmp_6);
        statement.bindLong(14, entity.getCloudStorageLimit());
        statement.bindString(15, entity.getTheme());
        statement.bindString(16, entity.getLanguage());
        final int _tmp_7 = entity.getShowTutorial() ? 1 : 0;
        statement.bindLong(17, _tmp_7);
        final int _tmp_8 = entity.getEnableHapticFeedback() ? 1 : 0;
        statement.bindLong(18, _tmp_8);
        final int _tmp_9 = entity.getEnableSoundEffects() ? 1 : 0;
        statement.bindLong(19, _tmp_9);
        statement.bindLong(20, entity.getMaxMemoryUsage());
        statement.bindLong(21, entity.getCacheSize());
        final int _tmp_10 = entity.getEnableGpuAcceleration() ? 1 : 0;
        statement.bindLong(22, _tmp_10);
        statement.bindString(23, entity.getRenderQuality());
        final int _tmp_11 = entity.getEnableAnalytics() ? 1 : 0;
        statement.bindLong(24, _tmp_11);
        final int _tmp_12 = entity.getEnableCrashReporting() ? 1 : 0;
        statement.bindLong(25, _tmp_12);
        statement.bindLong(26, entity.getDataRetentionDays());
        final int _tmp_13 = entity.getDebugMode() ? 1 : 0;
        statement.bindLong(27, _tmp_13);
        final int _tmp_14 = entity.getEnableExperimentalFeatures() ? 1 : 0;
        statement.bindLong(28, _tmp_14);
        statement.bindString(29, entity.getLogLevel());
        statement.bindLong(30, entity.getLastModified());
      }
    };
    this.__updateAdapterOfAppSettings = new EntityDeletionOrUpdateAdapter<AppSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `settings` SET `id` = ?,`defaultScanQuality` = ?,`autoSaveScans` = ?,`maxScanDuration` = ?,`frameRate` = ?,`enableDepthFiltering` = ?,`enableMeshSmoothing` = ?,`defaultExportFormat` = ?,`enableCompression` = ?,`compressionLevel` = ?,`enableCloudSync` = ?,`autoUpload` = ?,`wifiOnlyUpload` = ?,`cloudStorageLimit` = ?,`theme` = ?,`language` = ?,`showTutorial` = ?,`enableHapticFeedback` = ?,`enableSoundEffects` = ?,`maxMemoryUsage` = ?,`cacheSize` = ?,`enableGpuAcceleration` = ?,`renderQuality` = ?,`enableAnalytics` = ?,`enableCrashReporting` = ?,`dataRetentionDays` = ?,`debugMode` = ?,`enableExperimentalFeatures` = ?,`logLevel` = ?,`lastModified` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppSettings entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDefaultScanQuality());
        final int _tmp = entity.getAutoSaveScans() ? 1 : 0;
        statement.bindLong(3, _tmp);
        statement.bindLong(4, entity.getMaxScanDuration());
        statement.bindLong(5, entity.getFrameRate());
        final int _tmp_1 = entity.getEnableDepthFiltering() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        final int _tmp_2 = entity.getEnableMeshSmoothing() ? 1 : 0;
        statement.bindLong(7, _tmp_2);
        statement.bindString(8, entity.getDefaultExportFormat());
        final int _tmp_3 = entity.getEnableCompression() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        statement.bindLong(10, entity.getCompressionLevel());
        final int _tmp_4 = entity.getEnableCloudSync() ? 1 : 0;
        statement.bindLong(11, _tmp_4);
        final int _tmp_5 = entity.getAutoUpload() ? 1 : 0;
        statement.bindLong(12, _tmp_5);
        final int _tmp_6 = entity.getWifiOnlyUpload() ? 1 : 0;
        statement.bindLong(13, _tmp_6);
        statement.bindLong(14, entity.getCloudStorageLimit());
        statement.bindString(15, entity.getTheme());
        statement.bindString(16, entity.getLanguage());
        final int _tmp_7 = entity.getShowTutorial() ? 1 : 0;
        statement.bindLong(17, _tmp_7);
        final int _tmp_8 = entity.getEnableHapticFeedback() ? 1 : 0;
        statement.bindLong(18, _tmp_8);
        final int _tmp_9 = entity.getEnableSoundEffects() ? 1 : 0;
        statement.bindLong(19, _tmp_9);
        statement.bindLong(20, entity.getMaxMemoryUsage());
        statement.bindLong(21, entity.getCacheSize());
        final int _tmp_10 = entity.getEnableGpuAcceleration() ? 1 : 0;
        statement.bindLong(22, _tmp_10);
        statement.bindString(23, entity.getRenderQuality());
        final int _tmp_11 = entity.getEnableAnalytics() ? 1 : 0;
        statement.bindLong(24, _tmp_11);
        final int _tmp_12 = entity.getEnableCrashReporting() ? 1 : 0;
        statement.bindLong(25, _tmp_12);
        statement.bindLong(26, entity.getDataRetentionDays());
        final int _tmp_13 = entity.getDebugMode() ? 1 : 0;
        statement.bindLong(27, _tmp_13);
        final int _tmp_14 = entity.getEnableExperimentalFeatures() ? 1 : 0;
        statement.bindLong(28, _tmp_14);
        statement.bindString(29, entity.getLogLevel());
        statement.bindLong(30, entity.getLastModified());
        statement.bindLong(31, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateScanQuality = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET defaultScanQuality = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateExportFormat = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET defaultExportFormat = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCloudSync = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET enableCloudSync = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAutoUpload = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET autoUpload = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTheme = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET theme = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLanguage = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET language = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateGpuAcceleration = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET enableGpuAcceleration = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateMaxMemoryUsage = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET maxMemoryUsage = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCacheSize = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET cacheSize = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAnalytics = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET enableAnalytics = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDebugMode = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET debugMode = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastModified = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET lastModified = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSettings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM settings";
        return _query;
      }
    };
  }

  @Override
  public Object insertSettings(final AppSettings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAppSettings.insert(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSettings(final AppSettings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAppSettings.handle(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateScanQuality(final String quality,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateScanQuality.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, quality);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateScanQuality.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateExportFormat(final String format,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateExportFormat.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, format);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateExportFormat.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCloudSync(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCloudSync.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCloudSync.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAutoUpload(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAutoUpload.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAutoUpload.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTheme(final String theme, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTheme.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, theme);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTheme.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLanguage(final String language,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLanguage.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, language);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLanguage.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateGpuAcceleration(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateGpuAcceleration.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateGpuAcceleration.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateMaxMemoryUsage(final long maxMemory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateMaxMemoryUsage.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, maxMemory);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateMaxMemoryUsage.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCacheSize(final long cacheSize,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCacheSize.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cacheSize);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCacheSize.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAnalytics(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAnalytics.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAnalytics.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDebugMode(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDebugMode.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDebugMode.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastModified(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastModified.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastModified.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSettings(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSettings.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSettings.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getSettings(final Continuation<? super AppSettings> $completion) {
    final String _sql = "SELECT * FROM settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AppSettings>() {
      @Override
      @Nullable
      public AppSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDefaultScanQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultScanQuality");
          final int _cursorIndexOfAutoSaveScans = CursorUtil.getColumnIndexOrThrow(_cursor, "autoSaveScans");
          final int _cursorIndexOfMaxScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "maxScanDuration");
          final int _cursorIndexOfFrameRate = CursorUtil.getColumnIndexOrThrow(_cursor, "frameRate");
          final int _cursorIndexOfEnableDepthFiltering = CursorUtil.getColumnIndexOrThrow(_cursor, "enableDepthFiltering");
          final int _cursorIndexOfEnableMeshSmoothing = CursorUtil.getColumnIndexOrThrow(_cursor, "enableMeshSmoothing");
          final int _cursorIndexOfDefaultExportFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultExportFormat");
          final int _cursorIndexOfEnableCompression = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCompression");
          final int _cursorIndexOfCompressionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "compressionLevel");
          final int _cursorIndexOfEnableCloudSync = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCloudSync");
          final int _cursorIndexOfAutoUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "autoUpload");
          final int _cursorIndexOfWifiOnlyUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "wifiOnlyUpload");
          final int _cursorIndexOfCloudStorageLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudStorageLimit");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfShowTutorial = CursorUtil.getColumnIndexOrThrow(_cursor, "showTutorial");
          final int _cursorIndexOfEnableHapticFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "enableHapticFeedback");
          final int _cursorIndexOfEnableSoundEffects = CursorUtil.getColumnIndexOrThrow(_cursor, "enableSoundEffects");
          final int _cursorIndexOfMaxMemoryUsage = CursorUtil.getColumnIndexOrThrow(_cursor, "maxMemoryUsage");
          final int _cursorIndexOfCacheSize = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheSize");
          final int _cursorIndexOfEnableGpuAcceleration = CursorUtil.getColumnIndexOrThrow(_cursor, "enableGpuAcceleration");
          final int _cursorIndexOfRenderQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "renderQuality");
          final int _cursorIndexOfEnableAnalytics = CursorUtil.getColumnIndexOrThrow(_cursor, "enableAnalytics");
          final int _cursorIndexOfEnableCrashReporting = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCrashReporting");
          final int _cursorIndexOfDataRetentionDays = CursorUtil.getColumnIndexOrThrow(_cursor, "dataRetentionDays");
          final int _cursorIndexOfDebugMode = CursorUtil.getColumnIndexOrThrow(_cursor, "debugMode");
          final int _cursorIndexOfEnableExperimentalFeatures = CursorUtil.getColumnIndexOrThrow(_cursor, "enableExperimentalFeatures");
          final int _cursorIndexOfLogLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "logLevel");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final AppSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpDefaultScanQuality;
            _tmpDefaultScanQuality = _cursor.getString(_cursorIndexOfDefaultScanQuality);
            final boolean _tmpAutoSaveScans;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfAutoSaveScans);
            _tmpAutoSaveScans = _tmp != 0;
            final long _tmpMaxScanDuration;
            _tmpMaxScanDuration = _cursor.getLong(_cursorIndexOfMaxScanDuration);
            final int _tmpFrameRate;
            _tmpFrameRate = _cursor.getInt(_cursorIndexOfFrameRate);
            final boolean _tmpEnableDepthFiltering;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfEnableDepthFiltering);
            _tmpEnableDepthFiltering = _tmp_1 != 0;
            final boolean _tmpEnableMeshSmoothing;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfEnableMeshSmoothing);
            _tmpEnableMeshSmoothing = _tmp_2 != 0;
            final String _tmpDefaultExportFormat;
            _tmpDefaultExportFormat = _cursor.getString(_cursorIndexOfDefaultExportFormat);
            final boolean _tmpEnableCompression;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableCompression);
            _tmpEnableCompression = _tmp_3 != 0;
            final int _tmpCompressionLevel;
            _tmpCompressionLevel = _cursor.getInt(_cursorIndexOfCompressionLevel);
            final boolean _tmpEnableCloudSync;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfEnableCloudSync);
            _tmpEnableCloudSync = _tmp_4 != 0;
            final boolean _tmpAutoUpload;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAutoUpload);
            _tmpAutoUpload = _tmp_5 != 0;
            final boolean _tmpWifiOnlyUpload;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfWifiOnlyUpload);
            _tmpWifiOnlyUpload = _tmp_6 != 0;
            final long _tmpCloudStorageLimit;
            _tmpCloudStorageLimit = _cursor.getLong(_cursorIndexOfCloudStorageLimit);
            final String _tmpTheme;
            _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            final String _tmpLanguage;
            _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            final boolean _tmpShowTutorial;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfShowTutorial);
            _tmpShowTutorial = _tmp_7 != 0;
            final boolean _tmpEnableHapticFeedback;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfEnableHapticFeedback);
            _tmpEnableHapticFeedback = _tmp_8 != 0;
            final boolean _tmpEnableSoundEffects;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEnableSoundEffects);
            _tmpEnableSoundEffects = _tmp_9 != 0;
            final long _tmpMaxMemoryUsage;
            _tmpMaxMemoryUsage = _cursor.getLong(_cursorIndexOfMaxMemoryUsage);
            final long _tmpCacheSize;
            _tmpCacheSize = _cursor.getLong(_cursorIndexOfCacheSize);
            final boolean _tmpEnableGpuAcceleration;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfEnableGpuAcceleration);
            _tmpEnableGpuAcceleration = _tmp_10 != 0;
            final String _tmpRenderQuality;
            _tmpRenderQuality = _cursor.getString(_cursorIndexOfRenderQuality);
            final boolean _tmpEnableAnalytics;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfEnableAnalytics);
            _tmpEnableAnalytics = _tmp_11 != 0;
            final boolean _tmpEnableCrashReporting;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfEnableCrashReporting);
            _tmpEnableCrashReporting = _tmp_12 != 0;
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpDebugMode;
            final int _tmp_13;
            _tmp_13 = _cursor.getInt(_cursorIndexOfDebugMode);
            _tmpDebugMode = _tmp_13 != 0;
            final boolean _tmpEnableExperimentalFeatures;
            final int _tmp_14;
            _tmp_14 = _cursor.getInt(_cursorIndexOfEnableExperimentalFeatures);
            _tmpEnableExperimentalFeatures = _tmp_14 != 0;
            final String _tmpLogLevel;
            _tmpLogLevel = _cursor.getString(_cursorIndexOfLogLevel);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            _result = new AppSettings(_tmpId,_tmpDefaultScanQuality,_tmpAutoSaveScans,_tmpMaxScanDuration,_tmpFrameRate,_tmpEnableDepthFiltering,_tmpEnableMeshSmoothing,_tmpDefaultExportFormat,_tmpEnableCompression,_tmpCompressionLevel,_tmpEnableCloudSync,_tmpAutoUpload,_tmpWifiOnlyUpload,_tmpCloudStorageLimit,_tmpTheme,_tmpLanguage,_tmpShowTutorial,_tmpEnableHapticFeedback,_tmpEnableSoundEffects,_tmpMaxMemoryUsage,_tmpCacheSize,_tmpEnableGpuAcceleration,_tmpRenderQuality,_tmpEnableAnalytics,_tmpEnableCrashReporting,_tmpDataRetentionDays,_tmpDebugMode,_tmpEnableExperimentalFeatures,_tmpLogLevel,_tmpLastModified);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<AppSettings> getSettingsLiveData() {
    final String _sql = "SELECT * FROM settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"settings"}, false, new Callable<AppSettings>() {
      @Override
      @Nullable
      public AppSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDefaultScanQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultScanQuality");
          final int _cursorIndexOfAutoSaveScans = CursorUtil.getColumnIndexOrThrow(_cursor, "autoSaveScans");
          final int _cursorIndexOfMaxScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "maxScanDuration");
          final int _cursorIndexOfFrameRate = CursorUtil.getColumnIndexOrThrow(_cursor, "frameRate");
          final int _cursorIndexOfEnableDepthFiltering = CursorUtil.getColumnIndexOrThrow(_cursor, "enableDepthFiltering");
          final int _cursorIndexOfEnableMeshSmoothing = CursorUtil.getColumnIndexOrThrow(_cursor, "enableMeshSmoothing");
          final int _cursorIndexOfDefaultExportFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultExportFormat");
          final int _cursorIndexOfEnableCompression = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCompression");
          final int _cursorIndexOfCompressionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "compressionLevel");
          final int _cursorIndexOfEnableCloudSync = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCloudSync");
          final int _cursorIndexOfAutoUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "autoUpload");
          final int _cursorIndexOfWifiOnlyUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "wifiOnlyUpload");
          final int _cursorIndexOfCloudStorageLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudStorageLimit");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfShowTutorial = CursorUtil.getColumnIndexOrThrow(_cursor, "showTutorial");
          final int _cursorIndexOfEnableHapticFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "enableHapticFeedback");
          final int _cursorIndexOfEnableSoundEffects = CursorUtil.getColumnIndexOrThrow(_cursor, "enableSoundEffects");
          final int _cursorIndexOfMaxMemoryUsage = CursorUtil.getColumnIndexOrThrow(_cursor, "maxMemoryUsage");
          final int _cursorIndexOfCacheSize = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheSize");
          final int _cursorIndexOfEnableGpuAcceleration = CursorUtil.getColumnIndexOrThrow(_cursor, "enableGpuAcceleration");
          final int _cursorIndexOfRenderQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "renderQuality");
          final int _cursorIndexOfEnableAnalytics = CursorUtil.getColumnIndexOrThrow(_cursor, "enableAnalytics");
          final int _cursorIndexOfEnableCrashReporting = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCrashReporting");
          final int _cursorIndexOfDataRetentionDays = CursorUtil.getColumnIndexOrThrow(_cursor, "dataRetentionDays");
          final int _cursorIndexOfDebugMode = CursorUtil.getColumnIndexOrThrow(_cursor, "debugMode");
          final int _cursorIndexOfEnableExperimentalFeatures = CursorUtil.getColumnIndexOrThrow(_cursor, "enableExperimentalFeatures");
          final int _cursorIndexOfLogLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "logLevel");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final AppSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpDefaultScanQuality;
            _tmpDefaultScanQuality = _cursor.getString(_cursorIndexOfDefaultScanQuality);
            final boolean _tmpAutoSaveScans;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfAutoSaveScans);
            _tmpAutoSaveScans = _tmp != 0;
            final long _tmpMaxScanDuration;
            _tmpMaxScanDuration = _cursor.getLong(_cursorIndexOfMaxScanDuration);
            final int _tmpFrameRate;
            _tmpFrameRate = _cursor.getInt(_cursorIndexOfFrameRate);
            final boolean _tmpEnableDepthFiltering;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfEnableDepthFiltering);
            _tmpEnableDepthFiltering = _tmp_1 != 0;
            final boolean _tmpEnableMeshSmoothing;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfEnableMeshSmoothing);
            _tmpEnableMeshSmoothing = _tmp_2 != 0;
            final String _tmpDefaultExportFormat;
            _tmpDefaultExportFormat = _cursor.getString(_cursorIndexOfDefaultExportFormat);
            final boolean _tmpEnableCompression;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableCompression);
            _tmpEnableCompression = _tmp_3 != 0;
            final int _tmpCompressionLevel;
            _tmpCompressionLevel = _cursor.getInt(_cursorIndexOfCompressionLevel);
            final boolean _tmpEnableCloudSync;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfEnableCloudSync);
            _tmpEnableCloudSync = _tmp_4 != 0;
            final boolean _tmpAutoUpload;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAutoUpload);
            _tmpAutoUpload = _tmp_5 != 0;
            final boolean _tmpWifiOnlyUpload;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfWifiOnlyUpload);
            _tmpWifiOnlyUpload = _tmp_6 != 0;
            final long _tmpCloudStorageLimit;
            _tmpCloudStorageLimit = _cursor.getLong(_cursorIndexOfCloudStorageLimit);
            final String _tmpTheme;
            _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            final String _tmpLanguage;
            _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            final boolean _tmpShowTutorial;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfShowTutorial);
            _tmpShowTutorial = _tmp_7 != 0;
            final boolean _tmpEnableHapticFeedback;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfEnableHapticFeedback);
            _tmpEnableHapticFeedback = _tmp_8 != 0;
            final boolean _tmpEnableSoundEffects;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEnableSoundEffects);
            _tmpEnableSoundEffects = _tmp_9 != 0;
            final long _tmpMaxMemoryUsage;
            _tmpMaxMemoryUsage = _cursor.getLong(_cursorIndexOfMaxMemoryUsage);
            final long _tmpCacheSize;
            _tmpCacheSize = _cursor.getLong(_cursorIndexOfCacheSize);
            final boolean _tmpEnableGpuAcceleration;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfEnableGpuAcceleration);
            _tmpEnableGpuAcceleration = _tmp_10 != 0;
            final String _tmpRenderQuality;
            _tmpRenderQuality = _cursor.getString(_cursorIndexOfRenderQuality);
            final boolean _tmpEnableAnalytics;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfEnableAnalytics);
            _tmpEnableAnalytics = _tmp_11 != 0;
            final boolean _tmpEnableCrashReporting;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfEnableCrashReporting);
            _tmpEnableCrashReporting = _tmp_12 != 0;
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpDebugMode;
            final int _tmp_13;
            _tmp_13 = _cursor.getInt(_cursorIndexOfDebugMode);
            _tmpDebugMode = _tmp_13 != 0;
            final boolean _tmpEnableExperimentalFeatures;
            final int _tmp_14;
            _tmp_14 = _cursor.getInt(_cursorIndexOfEnableExperimentalFeatures);
            _tmpEnableExperimentalFeatures = _tmp_14 != 0;
            final String _tmpLogLevel;
            _tmpLogLevel = _cursor.getString(_cursorIndexOfLogLevel);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            _result = new AppSettings(_tmpId,_tmpDefaultScanQuality,_tmpAutoSaveScans,_tmpMaxScanDuration,_tmpFrameRate,_tmpEnableDepthFiltering,_tmpEnableMeshSmoothing,_tmpDefaultExportFormat,_tmpEnableCompression,_tmpCompressionLevel,_tmpEnableCloudSync,_tmpAutoUpload,_tmpWifiOnlyUpload,_tmpCloudStorageLimit,_tmpTheme,_tmpLanguage,_tmpShowTutorial,_tmpEnableHapticFeedback,_tmpEnableSoundEffects,_tmpMaxMemoryUsage,_tmpCacheSize,_tmpEnableGpuAcceleration,_tmpRenderQuality,_tmpEnableAnalytics,_tmpEnableCrashReporting,_tmpDataRetentionDays,_tmpDebugMode,_tmpEnableExperimentalFeatures,_tmpLogLevel,_tmpLastModified);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<AppSettings> getSettingsFlow() {
    final String _sql = "SELECT * FROM settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"settings"}, new Callable<AppSettings>() {
      @Override
      @Nullable
      public AppSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDefaultScanQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultScanQuality");
          final int _cursorIndexOfAutoSaveScans = CursorUtil.getColumnIndexOrThrow(_cursor, "autoSaveScans");
          final int _cursorIndexOfMaxScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "maxScanDuration");
          final int _cursorIndexOfFrameRate = CursorUtil.getColumnIndexOrThrow(_cursor, "frameRate");
          final int _cursorIndexOfEnableDepthFiltering = CursorUtil.getColumnIndexOrThrow(_cursor, "enableDepthFiltering");
          final int _cursorIndexOfEnableMeshSmoothing = CursorUtil.getColumnIndexOrThrow(_cursor, "enableMeshSmoothing");
          final int _cursorIndexOfDefaultExportFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultExportFormat");
          final int _cursorIndexOfEnableCompression = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCompression");
          final int _cursorIndexOfCompressionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "compressionLevel");
          final int _cursorIndexOfEnableCloudSync = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCloudSync");
          final int _cursorIndexOfAutoUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "autoUpload");
          final int _cursorIndexOfWifiOnlyUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "wifiOnlyUpload");
          final int _cursorIndexOfCloudStorageLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudStorageLimit");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfShowTutorial = CursorUtil.getColumnIndexOrThrow(_cursor, "showTutorial");
          final int _cursorIndexOfEnableHapticFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "enableHapticFeedback");
          final int _cursorIndexOfEnableSoundEffects = CursorUtil.getColumnIndexOrThrow(_cursor, "enableSoundEffects");
          final int _cursorIndexOfMaxMemoryUsage = CursorUtil.getColumnIndexOrThrow(_cursor, "maxMemoryUsage");
          final int _cursorIndexOfCacheSize = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheSize");
          final int _cursorIndexOfEnableGpuAcceleration = CursorUtil.getColumnIndexOrThrow(_cursor, "enableGpuAcceleration");
          final int _cursorIndexOfRenderQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "renderQuality");
          final int _cursorIndexOfEnableAnalytics = CursorUtil.getColumnIndexOrThrow(_cursor, "enableAnalytics");
          final int _cursorIndexOfEnableCrashReporting = CursorUtil.getColumnIndexOrThrow(_cursor, "enableCrashReporting");
          final int _cursorIndexOfDataRetentionDays = CursorUtil.getColumnIndexOrThrow(_cursor, "dataRetentionDays");
          final int _cursorIndexOfDebugMode = CursorUtil.getColumnIndexOrThrow(_cursor, "debugMode");
          final int _cursorIndexOfEnableExperimentalFeatures = CursorUtil.getColumnIndexOrThrow(_cursor, "enableExperimentalFeatures");
          final int _cursorIndexOfLogLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "logLevel");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final AppSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpDefaultScanQuality;
            _tmpDefaultScanQuality = _cursor.getString(_cursorIndexOfDefaultScanQuality);
            final boolean _tmpAutoSaveScans;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfAutoSaveScans);
            _tmpAutoSaveScans = _tmp != 0;
            final long _tmpMaxScanDuration;
            _tmpMaxScanDuration = _cursor.getLong(_cursorIndexOfMaxScanDuration);
            final int _tmpFrameRate;
            _tmpFrameRate = _cursor.getInt(_cursorIndexOfFrameRate);
            final boolean _tmpEnableDepthFiltering;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfEnableDepthFiltering);
            _tmpEnableDepthFiltering = _tmp_1 != 0;
            final boolean _tmpEnableMeshSmoothing;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfEnableMeshSmoothing);
            _tmpEnableMeshSmoothing = _tmp_2 != 0;
            final String _tmpDefaultExportFormat;
            _tmpDefaultExportFormat = _cursor.getString(_cursorIndexOfDefaultExportFormat);
            final boolean _tmpEnableCompression;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableCompression);
            _tmpEnableCompression = _tmp_3 != 0;
            final int _tmpCompressionLevel;
            _tmpCompressionLevel = _cursor.getInt(_cursorIndexOfCompressionLevel);
            final boolean _tmpEnableCloudSync;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfEnableCloudSync);
            _tmpEnableCloudSync = _tmp_4 != 0;
            final boolean _tmpAutoUpload;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAutoUpload);
            _tmpAutoUpload = _tmp_5 != 0;
            final boolean _tmpWifiOnlyUpload;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfWifiOnlyUpload);
            _tmpWifiOnlyUpload = _tmp_6 != 0;
            final long _tmpCloudStorageLimit;
            _tmpCloudStorageLimit = _cursor.getLong(_cursorIndexOfCloudStorageLimit);
            final String _tmpTheme;
            _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            final String _tmpLanguage;
            _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            final boolean _tmpShowTutorial;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfShowTutorial);
            _tmpShowTutorial = _tmp_7 != 0;
            final boolean _tmpEnableHapticFeedback;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfEnableHapticFeedback);
            _tmpEnableHapticFeedback = _tmp_8 != 0;
            final boolean _tmpEnableSoundEffects;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEnableSoundEffects);
            _tmpEnableSoundEffects = _tmp_9 != 0;
            final long _tmpMaxMemoryUsage;
            _tmpMaxMemoryUsage = _cursor.getLong(_cursorIndexOfMaxMemoryUsage);
            final long _tmpCacheSize;
            _tmpCacheSize = _cursor.getLong(_cursorIndexOfCacheSize);
            final boolean _tmpEnableGpuAcceleration;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfEnableGpuAcceleration);
            _tmpEnableGpuAcceleration = _tmp_10 != 0;
            final String _tmpRenderQuality;
            _tmpRenderQuality = _cursor.getString(_cursorIndexOfRenderQuality);
            final boolean _tmpEnableAnalytics;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfEnableAnalytics);
            _tmpEnableAnalytics = _tmp_11 != 0;
            final boolean _tmpEnableCrashReporting;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfEnableCrashReporting);
            _tmpEnableCrashReporting = _tmp_12 != 0;
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpDebugMode;
            final int _tmp_13;
            _tmp_13 = _cursor.getInt(_cursorIndexOfDebugMode);
            _tmpDebugMode = _tmp_13 != 0;
            final boolean _tmpEnableExperimentalFeatures;
            final int _tmp_14;
            _tmp_14 = _cursor.getInt(_cursorIndexOfEnableExperimentalFeatures);
            _tmpEnableExperimentalFeatures = _tmp_14 != 0;
            final String _tmpLogLevel;
            _tmpLogLevel = _cursor.getString(_cursorIndexOfLogLevel);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            _result = new AppSettings(_tmpId,_tmpDefaultScanQuality,_tmpAutoSaveScans,_tmpMaxScanDuration,_tmpFrameRate,_tmpEnableDepthFiltering,_tmpEnableMeshSmoothing,_tmpDefaultExportFormat,_tmpEnableCompression,_tmpCompressionLevel,_tmpEnableCloudSync,_tmpAutoUpload,_tmpWifiOnlyUpload,_tmpCloudStorageLimit,_tmpTheme,_tmpLanguage,_tmpShowTutorial,_tmpEnableHapticFeedback,_tmpEnableSoundEffects,_tmpMaxMemoryUsage,_tmpCacheSize,_tmpEnableGpuAcceleration,_tmpRenderQuality,_tmpEnableAnalytics,_tmpEnableCrashReporting,_tmpDataRetentionDays,_tmpDebugMode,_tmpEnableExperimentalFeatures,_tmpLogLevel,_tmpLastModified);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
