package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.BuildConfig
import com.scanner3d.app.repository.AuthRepository
import kotlinx.coroutines.launch

class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val authRepository = AuthRepository(application)
    
    private val _isAuthenticated = MutableLiveData<Boolean>()
    val isAuthenticated: LiveData<Boolean> = _isAuthenticated
    
    private val _appVersion = MutableLiveData<String>()
    val appVersion: LiveData<String> = _appVersion
    
    init {
        _appVersion.value = BuildConfig.VERSION_NAME
    }
    
    fun checkAuthentication() {
        viewModelScope.launch {
            val authenticated = authRepository.isUserAuthenticated()
            _isAuthenticated.value = authenticated
        }
    }
    
    fun logout() {
        viewModelScope.launch {
            authRepository.logout()
            _isAuthenticated.value = false
        }
    }
}
