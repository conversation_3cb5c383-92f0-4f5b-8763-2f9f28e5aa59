package com.scanner3d.app.utils

import android.content.Context
import android.util.Log
import com.scanner3d.app.data.model.Mesh3D
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

class MeshExporter(private val context: Context) {
    
    companion object {
        private const val TAG = "MeshExporter"
    }
    
    fun exportMesh(mesh: Mesh3D, outputFile: File, format: String): Boolean {
        return try {
            when (format.uppercase()) {
                "OBJ" -> exportToOBJ(mesh, outputFile)
                "STL" -> exportToSTL(mesh, outputFile)
                "PLY" -> exportToPLY(mesh, outputFile)
                "GLTF" -> exportToGLTF(mesh, outputFile)
                else -> {
                    Log.e(TAG, "Unsupported export format: $format")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export mesh to $format", e)
            false
        }
    }
    
    private fun exportToOBJ(mesh: Mesh3D, outputFile: File): Boolean {
        return try {
            PrintWriter(FileWriter(outputFile)).use { writer ->
                // Write header
                writer.println("# 3D Scanner Export")
                writer.println("# Generated on ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
                writer.println("# Vertices: ${mesh.vertexCount}")
                writer.println("# Triangles: ${mesh.triangleCount}")
                writer.println()
                
                // Write vertices
                for (i in 0 until mesh.vertexCount) {
                    val x = mesh.vertices[i * 3]
                    val y = mesh.vertices[i * 3 + 1]
                    val z = mesh.vertices[i * 3 + 2]
                    writer.println("v $x $y $z")
                }
                
                // Write vertex normals if available
                mesh.normals?.let { normals ->
                    writer.println()
                    for (i in 0 until mesh.vertexCount) {
                        val nx = normals[i * 3]
                        val ny = normals[i * 3 + 1]
                        val nz = normals[i * 3 + 2]
                        writer.println("vn $nx $ny $nz")
                    }
                }
                
                // Write texture coordinates if available
                mesh.textureCoordinates?.let { uvs ->
                    writer.println()
                    for (i in 0 until mesh.vertexCount) {
                        val u = uvs[i * 2]
                        val v = uvs[i * 2 + 1]
                        writer.println("vt $u $v")
                    }
                }
                
                // Write faces
                writer.println()
                for (i in mesh.indices.indices step 3) {
                    val v1 = mesh.indices[i] + 1     // OBJ uses 1-based indexing
                    val v2 = mesh.indices[i + 1] + 1
                    val v3 = mesh.indices[i + 2] + 1
                    
                    if (mesh.normals != null && mesh.textureCoordinates != null) {
                        writer.println("f $v1/$v1/$v1 $v2/$v2/$v2 $v3/$v3/$v3")
                    } else if (mesh.normals != null) {
                        writer.println("f $v1//$v1 $v2//$v2 $v3//$v3")
                    } else if (mesh.textureCoordinates != null) {
                        writer.println("f $v1/$v1 $v2/$v2 $v3/$v3")
                    } else {
                        writer.println("f $v1 $v2 $v3")
                    }
                }
            }
            
            // Create material file if texture coordinates exist
            if (mesh.textureCoordinates != null) {
                createMTLFile(outputFile, mesh)
            }
            
            Log.d(TAG, "Successfully exported mesh to OBJ: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export to OBJ", e)
            false
        }
    }
    
    private fun exportToSTL(mesh: Mesh3D, outputFile: File): Boolean {
        return try {
            DataOutputStream(FileOutputStream(outputFile)).use { dos ->
                // Write STL header (80 bytes)
                val header = "3D Scanner Export".toByteArray()
                dos.write(header)
                dos.write(ByteArray(80 - header.size)) // Pad to 80 bytes
                
                // Write number of triangles
                dos.writeInt(Integer.reverseBytes(mesh.triangleCount))
                
                // Write triangles
                for (i in mesh.indices.indices step 3) {
                    val v1Index = mesh.indices[i] * 3
                    val v2Index = mesh.indices[i + 1] * 3
                    val v3Index = mesh.indices[i + 2] * 3
                    
                    val v1 = floatArrayOf(mesh.vertices[v1Index], mesh.vertices[v1Index + 1], mesh.vertices[v1Index + 2])
                    val v2 = floatArrayOf(mesh.vertices[v2Index], mesh.vertices[v2Index + 1], mesh.vertices[v2Index + 2])
                    val v3 = floatArrayOf(mesh.vertices[v3Index], mesh.vertices[v3Index + 1], mesh.vertices[v3Index + 2])
                    
                    // Calculate normal
                    val normal = calculateTriangleNormal(v1, v2, v3)
                    
                    // Write normal
                    dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(normal[0]))))
                    dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(normal[1]))))
                    dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(normal[2]))))
                    
                    // Write vertices
                    writeVertex(dos, v1)
                    writeVertex(dos, v2)
                    writeVertex(dos, v3)
                    
                    // Write attribute byte count (usually 0)
                    dos.writeShort(0)
                }
            }
            
            Log.d(TAG, "Successfully exported mesh to STL: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export to STL", e)
            false
        }
    }
    
    private fun exportToPLY(mesh: Mesh3D, outputFile: File): Boolean {
        return try {
            PrintWriter(FileWriter(outputFile)).use { writer ->
                // Write PLY header
                writer.println("ply")
                writer.println("format ascii 1.0")
                writer.println("comment 3D Scanner Export")
                writer.println("element vertex ${mesh.vertexCount}")
                writer.println("property float x")
                writer.println("property float y")
                writer.println("property float z")
                
                if (mesh.normals != null) {
                    writer.println("property float nx")
                    writer.println("property float ny")
                    writer.println("property float nz")
                }
                
                if (mesh.colors != null) {
                    writer.println("property uchar red")
                    writer.println("property uchar green")
                    writer.println("property uchar blue")
                }
                
                writer.println("element face ${mesh.triangleCount}")
                writer.println("property list uchar int vertex_indices")
                writer.println("end_header")
                
                // Write vertices
                for (i in 0 until mesh.vertexCount) {
                    val x = mesh.vertices[i * 3]
                    val y = mesh.vertices[i * 3 + 1]
                    val z = mesh.vertices[i * 3 + 2]
                    
                    var line = "$x $y $z"
                    
                    // Add normals if available
                    mesh.normals?.let { normals ->
                        val nx = normals[i * 3]
                        val ny = normals[i * 3 + 1]
                        val nz = normals[i * 3 + 2]
                        line += " $nx $ny $nz"
                    }
                    
                    // Add colors if available
                    mesh.colors?.let { colors ->
                        val color = colors[i]
                        val r = (color shr 16) and 0xFF
                        val g = (color shr 8) and 0xFF
                        val b = color and 0xFF
                        line += " $r $g $b"
                    }
                    
                    writer.println(line)
                }
                
                // Write faces
                for (i in mesh.indices.indices step 3) {
                    val v1 = mesh.indices[i]
                    val v2 = mesh.indices[i + 1]
                    val v3 = mesh.indices[i + 2]
                    writer.println("3 $v1 $v2 $v3")
                }
            }
            
            Log.d(TAG, "Successfully exported mesh to PLY: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export to PLY", e)
            false
        }
    }
    
    private fun exportToGLTF(mesh: Mesh3D, outputFile: File): Boolean {
        // GLTF export is complex and would require a proper GLTF library
        // For now, we'll export as OBJ as a fallback
        Log.w(TAG, "GLTF export not fully implemented, falling back to OBJ")
        val objFile = File(outputFile.parent, outputFile.nameWithoutExtension + ".obj")
        return exportToOBJ(mesh, objFile)
    }
    
    private fun createMTLFile(objFile: File, mesh: Mesh3D) {
        val mtlFile = File(objFile.parent, objFile.nameWithoutExtension + ".mtl")
        try {
            PrintWriter(FileWriter(mtlFile)).use { writer ->
                writer.println("# Material file for ${objFile.name}")
                writer.println("newmtl material0")
                writer.println("Ka 0.2 0.2 0.2")
                writer.println("Kd 0.8 0.8 0.8")
                writer.println("Ks 0.1 0.1 0.1")
                writer.println("Ns 10.0")
                writer.println("illum 2")
                
                // Add texture map if available
                if (mesh.metadata.hasTexture) {
                    val textureFile = objFile.nameWithoutExtension + "_texture.jpg"
                    writer.println("map_Kd $textureFile")
                }
            }
            
            // Update OBJ file to reference MTL
            val objContent = objFile.readText()
            val updatedContent = "mtllib ${mtlFile.name}\n$objContent"
            objFile.writeText(updatedContent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create MTL file", e)
        }
    }
    
    private fun calculateTriangleNormal(v1: FloatArray, v2: FloatArray, v3: FloatArray): FloatArray {
        val edge1 = floatArrayOf(v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2])
        val edge2 = floatArrayOf(v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2])
        
        val normal = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        // Normalize
        val length = kotlin.math.sqrt(normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2])
        if (length > 0) {
            normal[0] /= length
            normal[1] /= length
            normal[2] /= length
        }
        
        return normal
    }
    
    private fun writeVertex(dos: DataOutputStream, vertex: FloatArray) {
        dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(vertex[0]))))
        dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(vertex[1]))))
        dos.writeFloat(java.lang.Float.intBitsToFloat(Integer.reverseBytes(java.lang.Float.floatToIntBits(vertex[2]))))
    }
    
    fun importMesh(file: File, format: String): Mesh3D? {
        // Import functionality would be implemented here
        // For now, return null as this is primarily an export utility
        Log.w(TAG, "Mesh import not implemented yet")
        return null
    }
}
