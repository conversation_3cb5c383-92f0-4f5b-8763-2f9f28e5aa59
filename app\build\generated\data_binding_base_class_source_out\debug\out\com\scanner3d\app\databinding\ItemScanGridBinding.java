// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemScanGridBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView ivQualityIndicator;

  @NonNull
  public final ImageView ivTextureIndicator;

  @NonNull
  public final ImageView ivThumbnail;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvScanDate;

  @NonNull
  public final TextView tvScanName;

  private ItemScanGridBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView ivQualityIndicator, @NonNull ImageView ivTextureIndicator,
      @NonNull ImageView ivThumbnail, @NonNull TextView tvFileSize, @NonNull TextView tvScanDate,
      @NonNull TextView tvScanName) {
    this.rootView = rootView;
    this.ivQualityIndicator = ivQualityIndicator;
    this.ivTextureIndicator = ivTextureIndicator;
    this.ivThumbnail = ivThumbnail;
    this.tvFileSize = tvFileSize;
    this.tvScanDate = tvScanDate;
    this.tvScanName = tvScanName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemScanGridBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemScanGridBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_scan_grid, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemScanGridBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_quality_indicator;
      ImageView ivQualityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivQualityIndicator == null) {
        break missingId;
      }

      id = R.id.iv_texture_indicator;
      ImageView ivTextureIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivTextureIndicator == null) {
        break missingId;
      }

      id = R.id.iv_thumbnail;
      ImageView ivThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (ivThumbnail == null) {
        break missingId;
      }

      id = R.id.tv_file_size;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tv_scan_date;
      TextView tvScanDate = ViewBindings.findChildViewById(rootView, id);
      if (tvScanDate == null) {
        break missingId;
      }

      id = R.id.tv_scan_name;
      TextView tvScanName = ViewBindings.findChildViewById(rootView, id);
      if (tvScanName == null) {
        break missingId;
      }

      return new ItemScanGridBinding((MaterialCardView) rootView, ivQualityIndicator,
          ivTextureIndicator, ivThumbnail, tvFileSize, tvScanDate, tvScanName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
