{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-68:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8587d926d346ccf2176f046e22a2a17\\transformed\\core-1.41.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,237,282,369,458", "endColumns": "42,44,86,88,70", "endOffsets": "236,281,368,457,528"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "297,344,393,484,577", "endColumns": "46,48,90,92,74", "endOffsets": "339,388,479,572,647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c351a29c398c623fa23a89619fe48\\transformed\\material-1.11.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2443,2520,2594,2644,2695,2761,2825,2893,2968,3040,3101,3172,3239,3299,3387,3467,3530,3613,3698,3772,3837,3913,3961,4035,4099,4175,4253,4315,4379,4442,4508,4588,4668,4744,4825,4879,4934", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2438,2515,2589,2639,2690,2756,2820,2888,2963,3035,3096,3167,3234,3294,3382,3462,3525,3608,3693,3767,3832,3908,3956,4030,4094,4170,4248,4310,4374,4437,4503,4583,4663,4739,4820,4874,4929,4998"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3213,3276,3337,3404,3473,4211,4301,4408,4660,4722,5803,5862,5920,5998,6059,6116,6172,6231,6289,6343,6429,6485,6543,6597,6662,6755,6829,6907,7027,7090,7153,7252,7329,7403,7453,7504,7570,7634,7702,7777,7849,7910,7981,8048,8108,8196,8276,8339,8422,8507,8581,8646,8722,8770,8844,8908,8984,9062,9124,9188,9251,9317,9397,9477,9553,9634,9688,9942", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "292,3271,3332,3399,3468,3545,4296,4403,4476,4717,4795,5857,5915,5993,6054,6111,6167,6226,6284,6338,6424,6480,6538,6592,6657,6750,6824,6902,7022,7085,7148,7247,7324,7398,7448,7499,7565,7629,7697,7772,7844,7905,7976,8043,8103,8191,8271,8334,8417,8502,8576,8641,8717,8765,8839,8903,8979,9057,9119,9183,9246,9312,9392,9472,9548,9629,9683,9738,10006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc343c3e9bd9f0c6efa95dea879d2fd0\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,99", "endOffsets": "149,249"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "9743,9842", "endColumns": "98,99", "endOffsets": "9837,9937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f5f3850a7c512d8f41852114da6284cb\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "652,747,840,940,1022,1119,1227,1304,1379,1471,1565,1656,1752,1847,1941,2037,2129,2221,2313,2391,2487,2582,2677,2774,2870,2968,3119,10011", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "742,835,935,1017,1114,1222,1299,1374,1466,1560,1651,1747,1842,1936,2032,2124,2216,2308,2386,2482,2577,2672,2769,2865,2963,3114,3208,10085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fae1aef513bb18ebfc860e4ba437b55\\transformed\\biometric-1.1.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,738,840,930,1038,1141", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "148,229,328,431,533,631,733,835,925,1033,1136,1232"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4481,4579,4800,4899,5002,5104,5202,5304,5406,5496,5604,5707", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "4574,4655,4894,4997,5099,5197,5299,5401,5491,5599,5702,5798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\70befc9c16dd9e301c153021ec73a9fd\\transformed\\core-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3550,3642,3741,3835,3929,4022,4115,10090", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3637,3736,3830,3924,4017,4110,4206,10186"}}]}]}