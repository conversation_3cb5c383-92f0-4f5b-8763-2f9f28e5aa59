<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.gallery.GalleryActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        <!-- Search Bar -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/search_background"
            app:iconifiedByDefault="false"
            app:queryHint="Search scans..." />

        <!-- Stats Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/surface_light"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/tv_scan_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Scans: 0"
                android:textColor="@color/text_secondary_light"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_total_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Size: 0 MB"
                android:textColor="@color/text_secondary_light"
                android:textSize="14sp" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Scans RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_scans"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:padding="8dp"
                tools:listitem="@layout/item_scan_grid" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/tv_empty_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_empty_gallery"
                    android:tint="@color/text_secondary_light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="No scans yet"
                    android:textColor="@color/text_secondary_light"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Start your first 3D scan to see it here"
                    android:textColor="@color/text_secondary_light"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Loading Progress -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_new_scan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/new_scan"
        android:src="@drawable/ic_add"
        app:backgroundTint="@color/primary_blue"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
