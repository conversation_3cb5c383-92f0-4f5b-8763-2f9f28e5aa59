  Activity android.app  Context android.content  ContextWrapper android.content  
GLSurfaceView android.opengl  ContextThemeWrapper android.view  SurfaceView android.view  View android.view  ComponentActivity androidx.activity  AppCompatActivity androidx.appcompat.app  ComponentActivity androidx.core.app  FragmentActivity androidx.fragment.app  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  Callback androidx.room.RoomDatabase  	Migration androidx.room.migration  MainActivity com.scanner3d.app  ScanningEngine com.scanner3d.app.core  	Companion %com.scanner3d.app.core.ScanningEngine  ScanDao com.scanner3d.app.data.database  Scanner3DDatabase com.scanner3d.app.data.database  SettingsDao com.scanner3d.app.data.database  	Companion 1com.scanner3d.app.data.database.Scanner3DDatabase  DatabaseCallback ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  AppSettings com.scanner3d.app.data.model  Mesh3D com.scanner3d.app.data.model  
ScanEntity com.scanner3d.app.data.model  ScanProgress com.scanner3d.app.data.model  BoundingBox #com.scanner3d.app.data.model.Mesh3D  AuthRepository com.scanner3d.app.repository  CloudStorageRepository com.scanner3d.app.repository  ScanRepository com.scanner3d.app.repository  	Companion +com.scanner3d.app.repository.AuthRepository  	Companion 3com.scanner3d.app.repository.CloudStorageRepository  	Companion +com.scanner3d.app.repository.ScanRepository  AuthenticationActivity com.scanner3d.app.ui.auth  	DepthView com.scanner3d.app.ui.custom  PointCloudView com.scanner3d.app.ui.custom  PointCloudRenderer *com.scanner3d.app.ui.custom.PointCloudView  GalleryActivity com.scanner3d.app.ui.gallery  GalleryAdapter com.scanner3d.app.ui.gallery  	Companion +com.scanner3d.app.ui.gallery.GalleryAdapter  ScanningActivity com.scanner3d.app.ui.scanning  	Companion .com.scanner3d.app.ui.scanning.ScanningActivity  EncryptionManager com.scanner3d.app.utils  FileManager com.scanner3d.app.utils  
MemoryManager com.scanner3d.app.utils  MeshSimplifier com.scanner3d.app.utils  TextureCompressor com.scanner3d.app.utils  	Companion )com.scanner3d.app.utils.EncryptionManager  	Companion #com.scanner3d.app.utils.FileManager  StorageInfo #com.scanner3d.app.utils.FileManager  
CacheStats %com.scanner3d.app.utils.MemoryManager  	Companion %com.scanner3d.app.utils.MemoryManager  	Companion &com.scanner3d.app.utils.MeshSimplifier  VertexAdjacency &com.scanner3d.app.utils.MeshSimplifier  	Companion )com.scanner3d.app.utils.TextureCompressor  CompressionQuality )com.scanner3d.app.utils.TextureCompressor  
AuthViewModel com.scanner3d.app.viewmodel  GalleryViewModel com.scanner3d.app.viewmodel  
MainViewModel com.scanner3d.app.viewmodel  ScanningViewModel com.scanner3d.app.viewmodel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       