plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    // Uncomment when you add Firebase configuration files
    // id 'com.google.gms.google-services'
}

android {
    namespace 'com.scanner3d.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.scanner3d.app"
        minSdk 34
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Enable vector drawables support
        vectorDrawables {
            useSupportLibrary true
        }
        
        // NDK configuration for OpenCV and native libraries (uncomment when needed)
        // ndk {
        //     abiFilters 'arm64-v8a', 'armeabi-v7a'
        // }

        // External native build for OpenCV (uncomment when needed)
        // externalNativeBuild {
        //     cmake {
        //         cppFlags "-std=c++14"
        //         arguments "-DANDROID_STL=c++_shared"
        //     }
        // }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // Enable code shrinking and obfuscation
            shrinkResources true
        }
        debug {
            debuggable true
            minifyEnabled false
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding true
        dataBinding true
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.4'
    }
    
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
    
    // External native build configuration (uncomment when needed)
    // externalNativeBuild {
    //     cmake {
    //         path file('src/main/cpp/CMakeLists.txt')
    //         version '3.22.1'
    //     }
    // }
}

dependencies {
    // Core Android dependencies
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.activity:activity-ktx:1.8.1'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    
    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0'
    
    // Navigation components
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.5'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.5'
    
    // CameraX dependencies
    implementation 'androidx.camera:camera-core:1.3.1'
    implementation 'androidx.camera:camera-camera2:1.3.1'
    implementation 'androidx.camera:camera-lifecycle:1.3.1'
    implementation 'androidx.camera:camera-video:1.3.1'
    implementation 'androidx.camera:camera-view:1.3.1'
    implementation 'androidx.camera:camera-extensions:1.3.1'
    
    // ARCore dependencies
    implementation 'com.google.ar:core:1.41.0'
    implementation 'com.google.ar.sceneform:core:1.17.1'
    implementation 'com.google.ar.sceneform.ux:sceneform-ux:1.17.1'
    
    // OpenCV for Android (uncomment when needed)
    // implementation 'org.opencv:opencv-android:4.8.0'
    
    // TensorFlow Lite
    implementation 'org.tensorflow:tensorflow-lite:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-gpu:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-support:0.4.4'
    
    // Room database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    
    // Firebase dependencies (uncomment when you add Firebase configuration)
    // implementation platform('com.google.firebase:firebase-bom:32.7.0')
    // implementation 'com.google.firebase:firebase-auth-ktx'
    // implementation 'com.google.firebase:firebase-firestore-ktx'
    // implementation 'com.google.firebase:firebase-storage-ktx'
    // implementation 'com.google.firebase:firebase-analytics-ktx'
    // implementation 'com.google.firebase:firebase-crashlytics-ktx'
    
    // Biometric authentication
    implementation 'androidx.biometric:biometric:1.1.0'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // OpenGL and 3D rendering (uncomment if needed)
    // implementation 'org.rajawali3d:rajawali:1.2.1970'
    
    // JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.squareup.moshi:moshi:1.15.0'
    implementation 'com.squareup.moshi:moshi-kotlin:1.15.0'
    kapt 'com.squareup.moshi:moshi-kotlin-codegen:1.15.0'
    
    // Network and HTTP
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-moshi:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Image loading and processing
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'
    
    // Permissions handling
    implementation 'com.karumi:dexter:6.2.3'
    
    // File compression and export
    implementation 'org.apache.commons:commons-compress:1.24.0'
    
    // Compose dependencies (uncomment if using Compose)
    // implementation 'androidx.compose:compose-bom:2023.10.01'
    // implementation 'androidx.compose.ui:ui'
    // implementation 'androidx.compose.ui:ui-graphics'
    // implementation 'androidx.compose.ui:ui-tooling-preview'
    // implementation 'androidx.compose.material3:material3'
    // implementation 'androidx.activity:activity-compose:1.8.1'
    // implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    
    // Testing dependencies
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.1'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'androidx.room:room-testing:2.6.1'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    // androidTestImplementation 'androidx.compose:compose-bom:2023.10.01'
    // androidTestImplementation 'androidx.compose.ui:ui-test-junit4'

    // debugImplementation 'androidx.compose.ui:ui-tooling'
    // debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
