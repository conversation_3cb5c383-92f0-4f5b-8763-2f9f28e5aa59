package com.scanner3d.app.repository

import android.content.Context
import android.net.Uri
import android.util.Log
// Firebase imports for cloud storage
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageMetadata
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.ScanEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.File

class CloudStorageRepository(private val context: Context) {
    
    companion object {
        private const val TAG = "CloudStorageRepository"
        private const val SCANS_COLLECTION = "scans"
        private const val STORAGE_PATH_SCANS = "scans"
        private const val STORAGE_PATH_THUMBNAILS = "thumbnails"
    }
    
    // Firebase instances for cloud storage (with null safety for development)
    private val auth: FirebaseAuth? = try { FirebaseAuth.getInstance() } catch (e: Exception) { null }
    private val firestore: FirebaseFirestore? = try { FirebaseFirestore.getInstance() } catch (e: Exception) { null }
    private val storage: FirebaseStorage? = try { FirebaseStorage.getInstance() } catch (e: Exception) { null }
    private val database = Scanner3DDatabase.getDatabase(context)
    private val scanDao = database.scanDao()

    private val isFirebaseAvailable = auth != null && firestore != null && storage != null
    
    suspend fun uploadScan(scanEntity: ScanEntity): Result<String> = withContext(Dispatchers.IO) {
        try {
            if (!isFirebaseAvailable) {
                Log.d(TAG, "Firebase not available - using local storage only")

                // Simulate successful upload by returning a local file path
                val localPath = "local://${scanEntity.filePath}"

                // Update the scan entity to mark as "uploaded" locally
                val updatedEntity = scanEntity.copy(
                    isUploaded = true,
                    cloudUrl = localPath
                )
                scanDao.updateScan(updatedEntity)

                Log.d(TAG, "Scan upload completed locally: ${localPath}")
                return@withContext Result.success(localPath)
            }

            // Real Firebase upload
            Log.d(TAG, "Uploading scan to Firebase: ${scanEntity.name}")

            val file = File(scanEntity.filePath)
            if (!file.exists()) {
                return@withContext Result.failure(Exception("Scan file not found"))
            }

            val storageRef = storage!!.reference
                .child("scans")
                .child("${auth!!.currentUser?.uid ?: "anonymous"}")
                .child("${scanEntity.id}.${file.extension}")

            // Upload file
            val uploadTask = storageRef.putFile(Uri.fromFile(file)).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()

            // Save metadata to Firestore
            val scanData = mapOf(
                "id" to scanEntity.id,
                "name" to scanEntity.name,
                "description" to scanEntity.description,
                "format" to scanEntity.format,
                "quality" to scanEntity.quality,
                "createdAt" to scanEntity.createdAt,
                "fileSize" to scanEntity.fileSize,
                "downloadUrl" to downloadUrl.toString(),
                "userId" to (auth.currentUser?.uid ?: "anonymous")
            )

            firestore!!.collection("scans")
                .document(scanEntity.id)
                .set(scanData)
                .await()

            // Update local database
            val updatedScan = scanEntity.copy(
                isUploaded = true,
                cloudUrl = downloadUrl.toString()
            )
            scanDao.updateScan(updatedScan)

            Log.d(TAG, "Successfully uploaded scan: ${scanEntity.name}")
            Result.success(downloadUrl.toString())

        } catch (e: Exception) {
            Log.e(TAG, "Error uploading scan", e)
            Result.failure(e)
        }
    }

    suspend fun downloadScan(scanId: String, outputFile: File): Result<File> = withContext(Dispatchers.IO) {
        try {
            // Firebase download disabled - using local storage only
            Log.d(TAG, "Download scan called for: $scanId")

            // Find the scan in local database
            val scan = scanDao.getScanById(scanId)
            if (scan != null && File(scan.filePath).exists()) {
                // Copy to output file if different
                if (scan.filePath != outputFile.absolutePath) {
                    File(scan.filePath).copyTo(outputFile, overwrite = true)
                }
                Result.success(outputFile)
            } else {
                Result.failure(Exception("Scan not found locally: $scanId"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error downloading scan", e)
            Result.failure(e)
        }
    }

    suspend fun syncScans(): Result<List<ScanEntity>> = withContext(Dispatchers.IO) {
        try {
            if (!isFirebaseAvailable) {
                Log.d(TAG, "Firebase not available - returning local scans only")
                val localScans = scanDao.getAllScansList()
                return@withContext Result.success(localScans)
            }

            // Real Firebase sync
            Log.d(TAG, "Syncing scans with Firebase")

            val userId = auth!!.currentUser?.uid ?: "anonymous"
            val cloudScans = firestore!!.collection("scans")
                .whereEqualTo("userId", userId)
                .get()
                .await()

            val syncedScans = mutableListOf<ScanEntity>()

            for (document in cloudScans.documents) {
                val data = document.data ?: continue

                // Check if we already have this scan locally
                val existingScan = scanDao.getScanById(document.id)

                if (existingScan == null) {
                    // Create new scan entity from cloud data
                    val cloudScan = ScanEntity(
                        id = document.id,
                        name = data["name"] as? String ?: "Unknown",
                        description = data["description"] as? String,
                        filePath = "", // Will be downloaded when needed
                        format = data["format"] as? String ?: "OBJ",
                        quality = data["quality"] as? String ?: "MEDIUM",
                        createdAt = data["createdAt"] as? Long ?: System.currentTimeMillis(),
                        fileSize = data["fileSize"] as? Long ?: 0L,
                        isUploaded = true,
                        cloudUrl = data["downloadUrl"] as? String
                    )

                    scanDao.insertScan(cloudScan)
                    syncedScans.add(cloudScan)
                } else {
                    syncedScans.add(existingScan)
                }
            }

            Log.d(TAG, "Successfully synced ${syncedScans.size} scans")
            Result.success(syncedScans)

        } catch (e: Exception) {
            Log.e(TAG, "Error syncing scans", e)
            Result.failure(e)
        }
    }

    suspend fun deleteScanFromCloud(scanId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Firebase delete disabled - removing from local database only
            Log.d(TAG, "Delete scan called for: $scanId")

            val scan = scanDao.getScanById(scanId)
            if (scan != null) {
                // Delete local files
                File(scan.filePath).delete()
                scan.thumbnailPath?.let { File(it).delete() }

                // Remove from database
                scanDao.deleteScan(scan)
                Result.success(Unit)
            } else {
                Result.failure(Exception("Scan not found: $scanId"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error deleting scan", e)
            Result.failure(e)
        }
    }

    fun isUserAuthenticated(): Boolean {
        // Firebase auth disabled - always return true for local mode
        return true
    }

    suspend fun signInAnonymously(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Firebase auth disabled - simulate successful anonymous sign-in
            Log.d(TAG, "Anonymous sign-in simulated")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "Error signing in anonymously", e)
            Result.failure(e)
        }
    }

    private fun getContentType(format: String): String {
        return when (format.lowercase()) {
            "obj" -> "model/obj"
            "stl" -> "model/stl"
            "ply" -> "model/ply"
            "gltf" -> "model/gltf+json"
            "glb" -> "model/gltf-binary"
            else -> "application/octet-stream"
        }
    }

    suspend fun getCloudStorageUsage(): Result<CloudStorageUsage> = withContext(Dispatchers.IO) {
        try {
            // Firebase disabled - return local storage usage
            Log.d(TAG, "Get cloud storage usage called - returning local stats")

            val localScans = scanDao.getAllScansList()
            var totalSize = 0L

            localScans.forEach { scan ->
                totalSize += scan.fileSize
            }

            val usage = CloudStorageUsage(
                totalScans = localScans.size,
                totalSize = totalSize,
                lastSyncTime = System.currentTimeMillis()
            )

            Result.success(usage)

        } catch (e: Exception) {
            Log.e(TAG, "Error getting storage usage", e)
            Result.failure(e)
        }
    }

    data class CloudStorageUsage(
        val totalScans: Int,
        val totalSize: Long,
        val lastSyncTime: Long
    )
}
