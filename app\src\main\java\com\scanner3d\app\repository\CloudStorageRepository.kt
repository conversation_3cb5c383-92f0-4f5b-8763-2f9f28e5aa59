package com.scanner3d.app.repository

import android.content.Context
import android.net.Uri
import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageMetadata
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.ScanEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.File

class CloudStorageRepository(private val context: Context) {
    
    companion object {
        private const val TAG = "CloudStorageRepository"
        private const val SCANS_COLLECTION = "scans"
        private const val STORAGE_PATH_SCANS = "scans"
        private const val STORAGE_PATH_THUMBNAILS = "thumbnails"
    }
    
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    private val storage = FirebaseStorage.getInstance()
    private val database = Scanner3DDatabase.getDatabase(context)
    private val scanDao = database.scanDao()
    
    suspend fun uploadScan(scanEntity: ScanEntity): Result<String> = withContext(Dispatchers.IO) {
        try {
            val userId = auth.currentUser?.uid
                ?: return@withContext Result.failure(Exception("User not authenticated"))
            
            // Upload scan file
            val scanFile = File(scanEntity.filePath)
            val scanStorageRef = storage.reference
                .child(STORAGE_PATH_SCANS)
                .child(userId)
                .child("${scanEntity.id}.${scanEntity.format.lowercase()}")
            
            val scanMetadata = StorageMetadata.Builder()
                .setContentType(getContentType(scanEntity.format))
                .setCustomMetadata("scanId", scanEntity.id)
                .setCustomMetadata("originalName", scanEntity.name)
                .build()
            
            val scanUploadTask = scanStorageRef.putFile(Uri.fromFile(scanFile), scanMetadata).await()
            val scanDownloadUrl = scanUploadTask.storage.downloadUrl.await()
            
            // Upload thumbnail if available
            var thumbnailUrl: String? = null
            scanEntity.thumbnailPath?.let { thumbnailPath ->
                val thumbnailFile = File(thumbnailPath)
                if (thumbnailFile.exists()) {
                    val thumbnailStorageRef = storage.reference
                        .child(STORAGE_PATH_THUMBNAILS)
                        .child(userId)
                        .child("${scanEntity.id}.jpg")
                    
                    val thumbnailMetadata = StorageMetadata.Builder()
                        .setContentType("image/jpeg")
                        .build()
                    
                    val thumbnailUploadTask = thumbnailStorageRef.putFile(Uri.fromFile(thumbnailFile), thumbnailMetadata).await()
                    thumbnailUrl = thumbnailUploadTask.storage.downloadUrl.await().toString()
                }
            }
            
            // Save metadata to Firestore
            val cloudScanData = mapOf(
                "id" to scanEntity.id,
                "name" to scanEntity.name,
                "description" to scanEntity.description,
                "createdAt" to scanEntity.createdAt,
                "modifiedAt" to scanEntity.modifiedAt,
                "scanDuration" to scanEntity.scanDuration,
                "fileUrl" to scanDownloadUrl.toString(),
                "thumbnailUrl" to thumbnailUrl,
                "fileSize" to scanEntity.fileSize,
                "format" to scanEntity.format,
                "quality" to scanEntity.quality,
                "vertexCount" to scanEntity.vertexCount,
                "triangleCount" to scanEntity.triangleCount,
                "hasTexture" to scanEntity.hasTexture,
                "hasColors" to scanEntity.hasColors,
                "boundingBox" to mapOf(
                    "minX" to scanEntity.boundingBoxMinX,
                    "minY" to scanEntity.boundingBoxMinY,
                    "minZ" to scanEntity.boundingBoxMinZ,
                    "maxX" to scanEntity.boundingBoxMaxX,
                    "maxY" to scanEntity.boundingBoxMaxY,
                    "maxZ" to scanEntity.boundingBoxMaxZ
                ),
                "userId" to userId,
                "uploadedAt" to System.currentTimeMillis()
            )
            
            firestore.collection(SCANS_COLLECTION)
                .document(scanEntity.id)
                .set(cloudScanData)
                .await()
            
            // Update local database
            scanDao.updateUploadStatus(scanEntity.id, true, scanDownloadUrl.toString())
            
            Log.d(TAG, "Successfully uploaded scan: ${scanEntity.id}")
            Result.success(scanDownloadUrl.toString())
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to upload scan", e)
            Result.failure(e)
        }
    }
    
    suspend fun downloadScan(scanId: String, outputFile: File): Result<File> = withContext(Dispatchers.IO) {
        try {
            val userId = auth.currentUser?.uid
                ?: return@withContext Result.failure(Exception("User not authenticated"))
            
            // Get scan metadata from Firestore
            val scanDoc = firestore.collection(SCANS_COLLECTION)
                .document(scanId)
                .get()
                .await()
            
            if (!scanDoc.exists()) {
                return@withContext Result.failure(Exception("Scan not found in cloud"))
            }
            
            val fileUrl = scanDoc.getString("fileUrl")
                ?: return@withContext Result.failure(Exception("File URL not found"))
            
            // Download file from Storage
            val storageRef = storage.getReferenceFromUrl(fileUrl)
            storageRef.getFile(outputFile).await()
            
            Log.d(TAG, "Successfully downloaded scan: $scanId")
            Result.success(outputFile)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to download scan", e)
            Result.failure(e)
        }
    }
    
    suspend fun syncScans(): Result<List<ScanEntity>> = withContext(Dispatchers.IO) {
        try {
            val userId = auth.currentUser?.uid
                ?: return@withContext Result.failure(Exception("User not authenticated"))
            
            // Get cloud scans
            val cloudScans = firestore.collection(SCANS_COLLECTION)
                .whereEqualTo("userId", userId)
                .get()
                .await()
            
            val syncedScans = mutableListOf<ScanEntity>()
            
            for (document in cloudScans.documents) {
                val cloudScan = document.data ?: continue
                
                // Check if scan exists locally
                val localScan = scanDao.getScanById(document.id)
                
                if (localScan == null) {
                    // Create local entry for cloud scan
                    val scanEntity = createScanEntityFromCloudData(cloudScan)
                    scanDao.insertScan(scanEntity)
                    syncedScans.add(scanEntity)
                } else if (!localScan.isUploaded) {
                    // Update local scan as uploaded
                    val fileUrl = cloudScan["fileUrl"] as? String
                    scanDao.updateUploadStatus(localScan.id, true, fileUrl)
                    syncedScans.add(localScan.copy(isUploaded = true, cloudUrl = fileUrl))
                }
            }
            
            Log.d(TAG, "Successfully synced ${syncedScans.size} scans")
            Result.success(syncedScans)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to sync scans", e)
            Result.failure(e)
        }
    }
    
    suspend fun deleteScanFromCloud(scanId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val userId = auth.currentUser?.uid
                ?: return@withContext Result.failure(Exception("User not authenticated"))
            
            // Get scan metadata
            val scanDoc = firestore.collection(SCANS_COLLECTION)
                .document(scanId)
                .get()
                .await()
            
            if (scanDoc.exists()) {
                val fileUrl = scanDoc.getString("fileUrl")
                val thumbnailUrl = scanDoc.getString("thumbnailUrl")
                
                // Delete files from Storage
                fileUrl?.let { url ->
                    try {
                        storage.getReferenceFromUrl(url).delete().await()
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to delete scan file from storage", e)
                    }
                }
                
                thumbnailUrl?.let { url ->
                    try {
                        storage.getReferenceFromUrl(url).delete().await()
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to delete thumbnail from storage", e)
                    }
                }
                
                // Delete metadata from Firestore
                firestore.collection(SCANS_COLLECTION)
                    .document(scanId)
                    .delete()
                    .await()
            }
            
            // Update local database
            scanDao.updateUploadStatus(scanId, false, null)
            
            Log.d(TAG, "Successfully deleted scan from cloud: $scanId")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete scan from cloud", e)
            Result.failure(e)
        }
    }
    
    suspend fun getCloudStorageUsage(): Result<CloudStorageUsage> = withContext(Dispatchers.IO) {
        try {
            val userId = auth.currentUser?.uid
                ?: return@withContext Result.failure(Exception("User not authenticated"))
            
            val cloudScans = firestore.collection(SCANS_COLLECTION)
                .whereEqualTo("userId", userId)
                .get()
                .await()
            
            var totalSize = 0L
            var scanCount = 0
            
            for (document in cloudScans.documents) {
                val fileSize = document.getLong("fileSize") ?: 0L
                totalSize += fileSize
                scanCount++
            }
            
            val usage = CloudStorageUsage(
                totalScans = scanCount,
                totalSize = totalSize,
                lastSyncTime = System.currentTimeMillis()
            )
            
            Result.success(usage)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get cloud storage usage", e)
            Result.failure(e)
        }
    }
    
    private fun createScanEntityFromCloudData(cloudData: Map<String, Any>): ScanEntity {
        val boundingBox = cloudData["boundingBox"] as? Map<String, Any> ?: emptyMap()
        
        return ScanEntity(
            id = cloudData["id"] as String,
            name = cloudData["name"] as String,
            description = cloudData["description"] as? String,
            createdAt = cloudData["createdAt"] as Long,
            modifiedAt = cloudData["modifiedAt"] as Long,
            scanDuration = cloudData["scanDuration"] as Long,
            filePath = "", // Will be set when downloaded
            thumbnailPath = null, // Will be set when downloaded
            fileSize = cloudData["fileSize"] as Long,
            format = cloudData["format"] as String,
            quality = cloudData["quality"] as String,
            vertexCount = (cloudData["vertexCount"] as Long).toInt(),
            triangleCount = (cloudData["triangleCount"] as Long).toInt(),
            hasTexture = cloudData["hasTexture"] as Boolean,
            hasColors = cloudData["hasColors"] as Boolean,
            isUploaded = true,
            cloudUrl = cloudData["fileUrl"] as String,
            boundingBoxMinX = (boundingBox["minX"] as? Double)?.toFloat() ?: 0f,
            boundingBoxMinY = (boundingBox["minY"] as? Double)?.toFloat() ?: 0f,
            boundingBoxMinZ = (boundingBox["minZ"] as? Double)?.toFloat() ?: 0f,
            boundingBoxMaxX = (boundingBox["maxX"] as? Double)?.toFloat() ?: 0f,
            boundingBoxMaxY = (boundingBox["maxY"] as? Double)?.toFloat() ?: 0f,
            boundingBoxMaxZ = (boundingBox["maxZ"] as? Double)?.toFloat() ?: 0f
        )
    }
    
    private fun getContentType(format: String): String {
        return when (format.uppercase()) {
            "OBJ" -> "model/obj"
            "STL" -> "model/stl"
            "PLY" -> "model/ply"
            "GLTF" -> "model/gltf+json"
            else -> "application/octet-stream"
        }
    }
    
    data class CloudStorageUsage(
        val totalScans: Int,
        val totalSize: Long,
        val lastSyncTime: Long
    )
}
