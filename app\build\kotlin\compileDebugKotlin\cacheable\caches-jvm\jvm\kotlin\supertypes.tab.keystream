com.scanner3d.app.MainActivity1com.scanner3d.app.data.database.Scanner3DDatabaseLcom.scanner3d.app.data.database.Scanner3DDatabase.Companion.DatabaseCallback(com.scanner3d.app.data.model.AppSettings#com.scanner3d.app.data.model.Mesh3D/com.scanner3d.app.data.model.Mesh3D.BoundingBox0com.scanner3d.app.data.model.Mesh3D.MeshMetadata/com.scanner3d.app.data.model.Mesh3D.MeshQuality+com.scanner3d.app.data.model.PointCloudData'com.scanner3d.app.data.model.ScanEntity)com.scanner3d.app.data.model.ScanProgress0com.scanner3d.app.ui.auth.AuthenticationActivity%com.scanner3d.app.ui.custom.DepthView*com.scanner3d.app.ui.custom.PointCloudView=com.scanner3d.app.ui.custom.PointCloudView.PointCloudRenderer,com.scanner3d.app.ui.gallery.GalleryActivity+com.scanner3d.app.ui.gallery.GalleryAdapter4com.scanner3d.app.ui.gallery.GalleryAdapter.ViewMode:com.scanner3d.app.ui.gallery.GalleryAdapter.GridViewHolder:com.scanner3d.app.ui.gallery.GalleryAdapter.ListViewHolder<com.scanner3d.app.ui.gallery.GalleryAdapter.ScanDiffCallback.com.scanner3d.app.ui.scanning.ScanningActivity6com.scanner3d.app.utils.MemoryManager.MemoryObjectType;com.scanner3d.app.utils.TextureCompressor.CompressionFormat<com.scanner3d.app.utils.TextureCompressor.CompressionQuality)com.scanner3d.app.viewmodel.AuthViewModel4com.scanner3d.app.viewmodel.AuthViewModel.AuthResult,com.scanner3d.app.viewmodel.GalleryViewModel3com.scanner3d.app.viewmodel.GalleryViewModel.SortBy5com.scanner3d.app.viewmodel.GalleryViewModel.FilterBy)com.scanner3d.app.viewmodel.MainViewModel-com.scanner3d.app.viewmodel.ScanningViewModel;com.scanner3d.app.databinding.ActivityAuthenticationBinding1com.scanner3d.app.databinding.ItemScanListBinding1com.scanner3d.app.databinding.ActivityMainBinding1com.scanner3d.app.databinding.ItemScanGridBinding4com.scanner3d.app.databinding.ActivityGalleryBinding5com.scanner3d.app.databinding.ActivityScanningBinding.com.scanner3d.app.ui.model.ModelViewerActivity0com.scanner3d.app.viewmodel.ModelViewerViewModel8com.scanner3d.app.databinding.ActivityModelViewerBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  