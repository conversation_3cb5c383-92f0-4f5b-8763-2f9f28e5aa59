1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scanner3d.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
13-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission
14-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
17    <uses-permission
17-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
21-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
22-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
23-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
24-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
25    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
25-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
26    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
26-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
27
28    <!-- ARCore permissions -->
29    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
29-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-79
29-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:22-76
30    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
30-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:5-81
30-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:22-78
31
32    <!-- Camera features -->
33    <uses-feature
33-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:5-27:35
34        android:name="android.hardware.camera"
34-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-47
35        android:required="true" />
35-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:9-32
36    <uses-feature
36-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:5-30:35
37        android:name="android.hardware.camera.autofocus"
37-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-57
38        android:required="true" />
38-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:30:9-32
39    <uses-feature
39-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:31:5-33:36
40        android:name="android.hardware.camera.flash"
40-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:9-53
41        android:required="false" />
41-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-33
42
43    <!-- ARCore features (optional for development) -->
44    <uses-feature
44-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:36:5-38:36
45        android:name="android.hardware.camera.ar"
45-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:9-50
46        android:required="false" />
46-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-33
47
48    <!-- OpenGL ES 3.0 for 3D rendering -->
49    <uses-feature
49-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:41:5-43:35
50        android:glEsVersion="0x00030000"
50-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:9-41
51        android:required="true" />
51-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-32
52
53    <!-- Sensors -->
54    <uses-feature
54-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:5-48:35
55        android:name="android.hardware.sensor.accelerometer"
55-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-61
56        android:required="true" />
56-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:48:9-32
57    <uses-feature
57-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-51:35
58        android:name="android.hardware.sensor.gyroscope"
58-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-57
59        android:required="true" />
59-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-32
60
61    <queries>
61-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
62        <intent>
62-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
63            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
63-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
63-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
64        </intent>
65
66        <package android:name="com.google.ar.core" />
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
66-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
67        <package android:name="com.android.vending" />
67-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
67-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
68
69        <intent>
69-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
70            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
70-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
70-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
71        </intent>
72    </queries>
73
74    <permission
74-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
75        android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
79
80    <application
80-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:5-121:19
81        android:allowBackup="true"
81-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-35
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
83        android:dataExtractionRules="@xml/data_extraction_rules"
83-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-65
84        android:debuggable="true"
85        android:extractNativeLibs="false"
86        android:fullBackupContent="@xml/backup_rules"
86-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-54
87        android:hardwareAccelerated="true"
87-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-43
88        android:icon="@mipmap/ic_launcher"
88-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-43
89        android:label="@string/app_name"
89-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-41
90        android:roundIcon="@mipmap/ic_launcher_round"
90-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:59:9-54
91        android:supportsRtl="true"
91-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:60:9-35
92        android:testOnly="true"
93        android:theme="@style/Theme.Scanner3D" >
93-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:61:9-47
94
95        <!-- ARCore metadata (optional for development) -->
96        <meta-data
96-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:66:9-68:40
97            android:name="com.google.ar.core"
97-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:13-46
98            android:value="optional" />
98-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-37
99
100        <!-- Main Activity -->
101        <activity
101-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:9-80:20
102            android:name="com.scanner3d.app.MainActivity"
102-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-41
103            android:exported="true"
103-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:13-36
104            android:screenOrientation="portrait"
104-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:13-49
105            android:theme="@style/Theme.Scanner3D.NoActionBar" >
105-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:75:13-63
106            <intent-filter>
106-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:76:13-79:29
107                <action android:name="android.intent.action.MAIN" />
107-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:77:17-69
107-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:77:25-66
108
109                <category android:name="android.intent.category.LAUNCHER" />
109-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:78:17-77
109-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:78:27-74
110            </intent-filter>
111        </activity>
112
113        <!-- Scanning Activity -->
114        <activity
114-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:9-87:66
115            android:name="com.scanner3d.app.ui.scanning.ScanningActivity"
115-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:84:13-57
116            android:exported="false"
116-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:85:13-37
117            android:screenOrientation="portrait"
117-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:13-49
118            android:theme="@style/Theme.Scanner3D.NoActionBar" />
118-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-63
119
120        <!-- Model Viewer Activity -->
121        <activity
121-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:9-94:66
122            android:name="com.scanner3d.app.ui.model.ModelViewerActivity"
122-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:91:13-57
123            android:exported="false"
123-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:92:13-37
124            android:screenOrientation="portrait"
124-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:13-49
125            android:theme="@style/Theme.Scanner3D.NoActionBar" />
125-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-63
126
127        <!-- Gallery Activity -->
128        <activity
128-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:9-101:66
129            android:name="com.scanner3d.app.ui.gallery.GalleryActivity"
129-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:98:13-55
130            android:exported="false"
130-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:99:13-37
131            android:screenOrientation="portrait"
131-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:13-49
132            android:theme="@style/Theme.Scanner3D.NoActionBar" />
132-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-63
133
134        <!-- Authentication Activity -->
135        <activity
135-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:9-108:66
136            android:name="com.scanner3d.app.ui.auth.AuthenticationActivity"
136-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:105:13-59
137            android:exported="false"
137-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:106:13-37
138            android:screenOrientation="portrait"
138-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:107:13-49
139            android:theme="@style/Theme.Scanner3D.NoActionBar" />
139-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-63
140
141        <!-- File Provider for sharing files -->
142        <provider
143            android:name="androidx.core.content.FileProvider"
143-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-62
144            android:authorities="com.scanner3d.app.fileprovider"
144-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:13-64
145            android:exported="false"
145-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:13-37
146            android:grantUriPermissions="true" >
146-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:115:13-47
147            <meta-data
147-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:116:13-118:54
148                android:name="android.support.FILE_PROVIDER_PATHS"
148-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:117:17-67
149                android:resource="@xml/file_paths" />
149-->C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:118:17-51
150        </provider>
151
152        <activity
152-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
153            android:name="com.karumi.dexter.DexterActivity"
153-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
154            android:theme="@style/Dexter.Internal.Theme.Transparent" />
154-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
155
156        <uses-library
156-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
157            android:name="androidx.camera.extensions.impl"
157-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
158            android:required="false" />
158-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
159
160        <service
160-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
161            android:name="androidx.camera.core.impl.MetadataHolderService"
161-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
162            android:enabled="false"
162-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
163            android:exported="false" >
163-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
164            <meta-data
164-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
165                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
165-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
166                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
166-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
167        </service>
168
169        <provider
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
170            android:name="androidx.startup.InitializationProvider"
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
171            android:authorities="com.scanner3d.app.androidx-startup"
171-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
172            android:exported="false" >
172-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
173            <meta-data
173-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.emoji2.text.EmojiCompatInitializer"
174-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
175                android:value="androidx.startup" />
175-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
177-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
178                android:value="androidx.startup" />
178-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
180-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
181                android:value="androidx.startup" />
181-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
182        </provider>
183
184        <uses-library
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
185            android:name="androidx.window.extensions"
185-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
186            android:required="false" />
186-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
187        <uses-library
187-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
188            android:name="androidx.window.sidecar"
188-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
189            android:required="false" />
189-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
190
191        <service
191-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
192            android:name="androidx.room.MultiInstanceInvalidationService"
192-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
193            android:directBootAware="true"
193-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
194            android:exported="false" /> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
194-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
195        <meta-data
195-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
196            android:name="com.google.ar.core.min_apk_version"
196-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
197            android:value="232620000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
197-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
198        <activity
198-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
199            android:name="com.google.ar.core.InstallActivity"
199-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
200            android:configChanges="keyboardHidden|orientation|screenSize"
200-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
201            android:excludeFromRecents="true"
201-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
202            android:exported="false"
202-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
203            android:launchMode="singleTop"
203-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
204            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
204-->[com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
205
206        <receiver
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
207            android:name="androidx.profileinstaller.ProfileInstallReceiver"
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
208            android:directBootAware="false"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
209            android:enabled="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
210            android:exported="true"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
211            android:permission="android.permission.DUMP" >
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
213                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
216                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
219                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
222                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
223            </intent-filter>
224        </receiver>
225    </application>
226
227</manifest>
