{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\857ae526a5ee6c76b63616ddc978cbae\\transformed\\navigation-ui-2.7.5\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "11514,11628", "endColumns": "113,122", "endOffsets": "11623,11746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "44,45,46,47,48,49,50,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4187,4285,4387,4487,4588,4695,4803,11911", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "4280,4382,4482,4583,4690,4798,4913,12007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,285,426,562", "endColumns": "46,47,140,135,90", "endOffsets": "236,284,425,561,652"}, "to": {"startLines": "7,8,9,10,11", "startColumns": "4,4,4,4,4", "startOffsets": "374,425,477,622,762", "endColumns": "50,51,144,139,94", "endOffsets": "420,472,617,757,852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "857,977,1087,1196,1282,1386,1508,1590,1670,1780,1888,1994,2103,2214,2317,2429,2536,2641,2741,2826,2935,3046,3145,3256,3363,3468,3642,11828", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "972,1082,1191,1277,1381,1503,1585,1665,1775,1883,1989,2098,2209,2312,2424,2531,2636,2736,2821,2930,3041,3140,3251,3358,3463,3637,3736,11906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1153,1247,1320,1381,1506,1572,1640,1701,1773,1833,1887,2007,2067,2129,2183,2260,2390,2477,2559,2700,2780,2865,2992,3083,3159,3213,3266,3332,3406,3487,3571,3651,3724,3801,3878,3952,4062,4155,4230,4320,4411,4483,4561,4652,4706,4789,4857,4941,5028,5090,5154,5217,5289,5399,5512,5615,5724,5782,5839", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1148,1242,1315,1376,1501,1567,1635,1696,1768,1828,1882,2002,2062,2124,2178,2255,2385,2472,2554,2695,2775,2860,2987,3078,3154,3208,3261,3327,3401,3482,3566,3646,3719,3796,3873,3947,4057,4150,4225,4315,4406,4478,4556,4647,4701,4784,4852,4936,5023,5085,5149,5212,5284,5394,5507,5610,5719,5777,5834,5911"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3741,3828,3913,3994,4099,4918,5019,5153,5436,5501,6922,6995,7056,7181,7247,7315,7376,7448,7508,7562,7682,7742,7804,7858,7935,8065,8152,8234,8375,8455,8540,8667,8758,8834,8888,8941,9007,9081,9162,9246,9326,9399,9476,9553,9627,9737,9830,9905,9995,10086,10158,10236,10327,10381,10464,10532,10616,10703,10765,10829,10892,10964,11074,11187,11290,11399,11457,11751", "endLines": "6,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "369,3823,3908,3989,4094,4182,5014,5148,5231,5496,5590,6990,7051,7176,7242,7310,7371,7443,7503,7557,7677,7737,7799,7853,7930,8060,8147,8229,8370,8450,8535,8662,8753,8829,8883,8936,9002,9076,9157,9241,9321,9394,9471,9548,9622,9732,9825,9900,9990,10081,10153,10231,10322,10376,10459,10527,10611,10698,10760,10824,10887,10959,11069,11182,11285,11394,11452,11509,11823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,255,387,523,657,796,931,1066,1164,1300,1462", "endColumns": "108,90,131,135,133,138,134,134,97,135,161,119", "endOffsets": "159,250,382,518,652,791,926,1061,1159,1295,1457,1577"}, "to": {"startLines": "54,55,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5236,5345,5595,5727,5863,5997,6136,6271,6406,6504,6640,6802", "endColumns": "108,90,131,135,133,138,134,134,97,135,161,119", "endOffsets": "5340,5431,5722,5858,5992,6131,6266,6401,6499,6635,6797,6917"}}]}]}