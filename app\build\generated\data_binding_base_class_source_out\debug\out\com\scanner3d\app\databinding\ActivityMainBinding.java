// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnSettings;

  @NonNull
  public final Button btnStartScanning;

  @NonNull
  public final Button btnViewGallery;

  @NonNull
  public final ImageView ivAppLogo;

  @NonNull
  public final LinearLayout llMainButtons;

  @NonNull
  public final TextView tvAppTitle;

  @NonNull
  public final TextView tvVersion;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnSettings,
      @NonNull Button btnStartScanning, @NonNull Button btnViewGallery,
      @NonNull ImageView ivAppLogo, @NonNull LinearLayout llMainButtons,
      @NonNull TextView tvAppTitle, @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.btnSettings = btnSettings;
    this.btnStartScanning = btnStartScanning;
    this.btnViewGallery = btnViewGallery;
    this.ivAppLogo = ivAppLogo;
    this.llMainButtons = llMainButtons;
    this.tvAppTitle = tvAppTitle;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_settings;
      Button btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.btn_start_scanning;
      Button btnStartScanning = ViewBindings.findChildViewById(rootView, id);
      if (btnStartScanning == null) {
        break missingId;
      }

      id = R.id.btn_view_gallery;
      Button btnViewGallery = ViewBindings.findChildViewById(rootView, id);
      if (btnViewGallery == null) {
        break missingId;
      }

      id = R.id.iv_app_logo;
      ImageView ivAppLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivAppLogo == null) {
        break missingId;
      }

      id = R.id.ll_main_buttons;
      LinearLayout llMainButtons = ViewBindings.findChildViewById(rootView, id);
      if (llMainButtons == null) {
        break missingId;
      }

      id = R.id.tv_app_title;
      TextView tvAppTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, btnSettings, btnStartScanning,
          btnViewGallery, ivAppLogo, llMainButtons, tvAppTitle, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
