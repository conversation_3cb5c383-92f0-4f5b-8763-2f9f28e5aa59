package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockitoAnnotations
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(AndroidJUnit4::class)
class ScanningViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: ScanningViewModel
    private lateinit var application: Application

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        application = ApplicationProvider.getApplicationContext()
        viewModel = ScanningViewModel(application)
    }

    @Test
    fun `viewModel_initialization_shouldHaveCorrectInitialState`() = runTest {
        // Then
        assertFalse(viewModel.isScanning.value, "Should not be scanning initially")
        assertFalse(viewModel.isPaused.value, "Should not be paused initially")
        assertEquals(0f, viewModel.scanProgress.value, "Initial progress should be 0")
    }

    @Test
    fun `viewModel_startScanning_shouldUpdateState`() = runTest {
        // When
        viewModel.startScanning()
        
        // Then
        assertTrue(viewModel.isScanning.value, "Should be scanning after start")
        assertFalse(viewModel.isPaused.value, "Should not be paused after start")
    }

    @Test
    fun `viewModel_pauseScanning_shouldUpdateState`() = runTest {
        // Given
        viewModel.startScanning()
        
        // When
        viewModel.pauseScanning()
        
        // Then
        assertTrue(viewModel.isScanning.value, "Should still be scanning")
        assertTrue(viewModel.isPaused.value, "Should be paused")
    }

    @Test
    fun `viewModel_resumeScanning_shouldUpdateState`() = runTest {
        // Given
        viewModel.startScanning()
        viewModel.pauseScanning()
        
        // When
        viewModel.resumeScanning()
        
        // Then
        assertTrue(viewModel.isScanning.value, "Should still be scanning")
        assertFalse(viewModel.isPaused.value, "Should not be paused after resume")
    }

    @Test
    fun `viewModel_stopScanning_shouldUpdateState`() = runTest {
        // Given
        viewModel.startScanning()
        
        // When
        viewModel.stopScanning()
        
        // Then
        assertFalse(viewModel.isScanning.value, "Should not be scanning after stop")
        assertFalse(viewModel.isPaused.value, "Should not be paused after stop")
    }

    @Test
    fun `viewModel_qualitySettings_shouldUpdateCorrectly`() = runTest {
        // When
        viewModel.updateScanQuality("HIGH")
        
        // Then
        assertEquals("HIGH", viewModel.currentQuality.value)
    }

    @Test
    fun `viewModel_scanProgress_shouldBeInValidRange`() = runTest {
        // Given
        viewModel.startScanning()
        
        // Allow some time for progress updates
        kotlinx.coroutines.delay(100)
        
        // Then
        val progress = viewModel.scanProgress.value
        assertTrue(progress >= 0f, "Progress should be non-negative")
        assertTrue(progress <= 1f, "Progress should not exceed 100%")
    }

    @Test
    fun `viewModel_errorHandling_shouldManageErrors`() = runTest {
        // When - trigger an error condition
        viewModel.handleCameraError("Test camera error")
        
        // Then
        val errorMessage = viewModel.errorMessage.value
        assertTrue(errorMessage?.contains("camera") == true, "Should show camera error")
    }

    @Test
    fun `viewModel_permissionHandling_shouldTrackPermissions`() = runTest {
        // When
        viewModel.updateCameraPermission(true)
        
        // Then
        assertTrue(viewModel.hasCameraPermission.value, "Should have camera permission")
        
        // When
        viewModel.updateCameraPermission(false)
        
        // Then
        assertFalse(viewModel.hasCameraPermission.value, "Should not have camera permission")
    }
} 