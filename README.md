# 3D Scanner Android App

A professional-grade Android application for 3D scanning using ARCore technology. This app allows users to capture real-world objects and environments as detailed 3D models.

## 🚀 Features

### Core Functionality
- **Real-time 3D Scanning** - Use your phone's camera and ARCore for accurate 3D capture
- **Multiple Export Formats** - Save scans as OBJ, STL, PLY, or glTF files
- **Cloud Synchronization** - Backup and sync scans across devices with Firebase
- **Secure Storage** - Biometric authentication and encrypted local storage
- **Gallery Management** - Organize, preview, and manage your 3D scans

### Advanced Features
- **Mesh Optimization** - Automatic mesh simplification and texture compression
- **Memory Management** - Intelligent caching and performance optimization
- **Quality Control** - Multiple scan quality settings (Low, Medium, High, Ultra)
- **Texture Mapping** - Capture and apply realistic textures to 3D models
- **Batch Processing** - Export multiple scans simultaneously

## 📱 Requirements

- **Android 14 (API 34)** or higher
- **ARCore supported device** - Check [ARCore compatibility](https://developers.google.com/ar/devices)
- **Camera permission** - Required for 3D scanning
- **Storage space** - Minimum 1GB free space recommended
- **Internet connection** - For cloud features (optional)

## 🛠️ Installation & Setup

### Quick Start (Recommended)
1. **Open in Android Studio**:
   - Download/clone this repository
   - Open Android Studio
   - Select "Open an existing project"
   - Choose the `3dscanner` folder
   - Let Android Studio sync and build

2. **Run the setup script** (Windows):
   ```bash
   setup_project.bat
   ```

### Manual Setup
See [BUILD_SETUP_GUIDE.md](BUILD_SETUP_GUIDE.md) for detailed instructions.

## 🏗️ Architecture

### Technology Stack
- **Language**: Kotlin
- **Architecture**: MVVM with Repository pattern
- **Database**: Room (SQLite)
- **Cloud**: Firebase (Firestore, Storage, Auth)
- **3D Processing**: ARCore, OpenGL ES
- **Camera**: CameraX
- **Security**: Android Keystore, Biometric API
- **Testing**: JUnit, Mockito, Espresso

### Project Structure
```
app/
├── src/main/java/com/scanner3d/app/
│   ├── core/              # Core scanning engine
│   ├── data/              # Data models and database
│   ├── repository/        # Data access layer
│   ├── ui/                # User interface components
│   ├── utils/             # Utility classes
│   └── MainActivity.kt    # Main entry point
├── src/test/              # Unit tests
├── src/androidTest/       # Integration tests
└── build.gradle           # App dependencies
```

## 🎯 Usage

### Basic Scanning Workflow
1. **Launch App** - Authenticate with biometric or PIN
2. **Start Scanning** - Tap "Start Scanning" on main screen
3. **Capture Object** - Move camera around object to capture all angles
4. **Process Scan** - Wait for automatic mesh generation
5. **Save & Export** - Choose format and save location

### Advanced Features
- **Quality Settings** - Adjust scan quality in settings
- **Cloud Sync** - Enable automatic backup to Firebase
- **Batch Export** - Select multiple scans for bulk export
- **Mesh Editing** - Basic mesh cleanup and optimization

## 🧪 Testing

### Run Tests
```bash
# Unit tests
./gradlew test

# Integration tests (requires device/emulator)
./gradlew connectedAndroidTest

# All tests
./gradlew check
```

### Test Coverage
- **Unit Tests**: Core logic, utilities, repositories
- **Integration Tests**: Database operations, scanning workflow
- **UI Tests**: User interactions, navigation flow

## 🔧 Configuration

### Firebase Setup (Optional)
1. Create Firebase project at [console.firebase.google.com](https://console.firebase.google.com)
2. Add Android app with package: `com.scanner3d.app`
3. Download `google-services.json` to `app/` directory
4. Uncomment Firebase dependencies in `app/build.gradle`

### OpenCV Setup (Optional)
1. Download OpenCV Android SDK
2. Add to project and uncomment dependencies
3. Required for advanced image processing features

## 📊 Performance

### Optimization Features
- **Memory Management** - Automatic cleanup and garbage collection
- **Mesh Simplification** - Reduce polygon count while preserving detail
- **Texture Compression** - Optimize texture size and quality
- **Caching System** - Intelligent data caching for better performance

### Benchmarks
- **Scan Time**: 30-120 seconds (depending on complexity)
- **Memory Usage**: <500MB during active scanning
- **Export Time**: 5-30 seconds (depending on format and size)
- **Storage**: 1-50MB per scan (depending on quality)

## 🔒 Security

### Data Protection
- **Biometric Authentication** - Fingerprint/Face ID access control
- **Encrypted Storage** - All local data encrypted with Android Keystore
- **Secure Cloud Sync** - Firebase security rules and authentication
- **Privacy Controls** - User control over data sharing and storage

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Follow coding standards and add tests
4. Commit changes: `git commit -m 'Add amazing feature'`
5. Push to branch: `git push origin feature/amazing-feature`
6. Open Pull Request

### Code Style
- Follow [Kotlin coding conventions](https://kotlinlang.org/docs/coding-conventions.html)
- Use meaningful variable and function names
- Add documentation for public APIs
- Include unit tests for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues
- **Build Errors**: Check [BUILD_SETUP_GUIDE.md](BUILD_SETUP_GUIDE.md)
- **ARCore Issues**: Verify device compatibility
- **Performance**: Check available storage and memory

### Getting Help
1. Check existing issues in the repository
2. Review documentation and setup guides
3. Create new issue with detailed description

## 🚀 Roadmap

### Upcoming Features
- [ ] Real-time collaborative scanning
- [ ] AI-powered mesh enhancement
- [ ] VR/AR model viewing
- [ ] Advanced texture editing
- [ ] Machine learning object recognition

### Version History
- **v1.0.0** - Initial release with core scanning features
- **v1.1.0** - Added cloud sync and security features
- **v1.2.0** - Performance optimizations and batch processing

---

**Built with ❤️ for the 3D scanning community**
