package com.scanner3d.app.data.database

import androidx.room.*
import androidx.lifecycle.LiveData
import com.scanner3d.app.data.model.ScanEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ScanDao {
    
    @Query("SELECT * FROM scans ORDER BY createdAt DESC")
    fun getAllScans(): Flow<List<ScanEntity>>

    @Query("SELECT * FROM scans ORDER BY createdAt DESC")
    suspend fun getAllScansList(): List<ScanEntity>

    @Query("SELECT * FROM scans ORDER BY createdAt DESC")
    fun getAllScansLiveData(): LiveData<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE id = :scanId")
    suspend fun getScanById(scanId: String): ScanEntity?
    
    @Query("SELECT * FROM scans WHERE id = :scanId")
    fun getScanByIdLiveData(scanId: String): LiveData<ScanEntity?>
    
    @Query("SELECT * FROM scans WHERE name LIKE :searchQuery OR description LIKE :searchQuery ORDER BY createdAt DESC")
    fun searchScans(searchQuery: String): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE quality = :quality ORDER BY createdAt DESC")
    fun getScansByQuality(quality: String): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE format = :format ORDER BY createdAt DESC")
    fun getScansByFormat(format: String): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE hasTexture = :hasTexture ORDER BY createdAt DESC")
    fun getScansByTextureStatus(hasTexture: Boolean): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE isUploaded = :isUploaded ORDER BY createdAt DESC")
    fun getScansByUploadStatus(isUploaded: Boolean): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getScansByDateRange(startDate: Long, endDate: Long): Flow<List<ScanEntity>>
    
    @Query("SELECT COUNT(*) FROM scans")
    suspend fun getScanCount(): Int
    
    @Query("SELECT SUM(fileSize) FROM scans")
    suspend fun getTotalFileSize(): Long
    
    @Query("SELECT AVG(scanDuration) FROM scans")
    suspend fun getAverageScanDuration(): Double
    
    @Query("SELECT * FROM scans ORDER BY fileSize DESC LIMIT :limit")
    fun getLargestScans(limit: Int = 10): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans ORDER BY createdAt DESC LIMIT :limit")
    fun getRecentScans(limit: Int = 10): Flow<List<ScanEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScan(scan: ScanEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScans(scans: List<ScanEntity>)
    
    @Update
    suspend fun updateScan(scan: ScanEntity)
    
    @Delete
    suspend fun deleteScan(scan: ScanEntity)
    
    @Query("DELETE FROM scans WHERE id = :scanId")
    suspend fun deleteScanById(scanId: String)
    
    @Query("DELETE FROM scans")
    suspend fun deleteAllScans()
    
    @Query("DELETE FROM scans WHERE createdAt < :cutoffDate")
    suspend fun deleteOldScans(cutoffDate: Long)
    
    @Query("UPDATE scans SET isUploaded = :isUploaded, cloudUrl = :cloudUrl WHERE id = :scanId")
    suspend fun updateUploadStatus(scanId: String, isUploaded: Boolean, cloudUrl: String?)
    
    @Query("UPDATE scans SET name = :name, description = :description, modifiedAt = :modifiedAt WHERE id = :scanId")
    suspend fun updateScanMetadata(scanId: String, name: String, description: String?, modifiedAt: Long)
    
    @Query("UPDATE scans SET tags = :tags WHERE id = :scanId")
    suspend fun updateScanTags(scanId: String, tags: String?)
}
