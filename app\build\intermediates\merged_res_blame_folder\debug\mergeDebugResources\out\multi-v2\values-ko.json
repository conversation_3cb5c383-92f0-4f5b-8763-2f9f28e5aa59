{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,233,278,373,468", "endColumns": "42,44,94,94,68", "endOffsets": "232,277,372,467,536"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "299,346,395,494,593", "endColumns": "46,48,98,98,72", "endOffsets": "341,390,489,588,661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3597,3689,3789,3883,3980,4076,4174,10322", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3684,3784,3878,3975,4071,4169,4269,10418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "666,763,857,958,1040,1138,1244,1324,1399,1490,1583,1678,1772,1872,1965,2060,2154,2245,2336,2416,2514,2608,2703,2803,2900,3000,3152,10243", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "758,852,953,1035,1133,1239,1319,1394,1485,1578,1673,1767,1867,1960,2055,2149,2240,2331,2411,2509,2603,2698,2798,2895,2995,3147,3241,10317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,242,346,463,571,687,793,906,1000,1120,1234", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "151,237,341,458,566,682,788,901,995,1115,1229,1332"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4538,4639,4868,4972,5089,5197,5313,5419,5532,5626,5746,5860", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "4634,4720,4967,5084,5192,5308,5414,5527,5621,5741,5855,5958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "9969,10069", "endColumns": "99,101", "endOffsets": "10064,10166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2494,2573,2648,2698,2749,2815,2879,2949,3026,3097,3165,3236,3303,3373,3466,3546,3620,3700,3782,3854,3919,3991,4039,4112,4176,4251,4328,4390,4454,4517,4584,4668,4746,4826,4904,4958,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2489,2568,2643,2693,2744,2810,2874,2944,3021,3092,3160,3231,3298,3368,3461,3541,3615,3695,3777,3849,3914,3986,4034,4107,4171,4246,4323,4385,4449,4512,4579,4663,4741,4821,4899,4953,5008,5080"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3246,3311,3375,3444,3518,4274,4357,4463,4725,4787,5963,6025,6082,6169,6229,6287,6345,6404,6461,6515,6610,6666,6723,6777,6843,6947,7022,7099,7220,7285,7350,7450,7529,7604,7654,7705,7771,7835,7905,7982,8053,8121,8192,8259,8329,8422,8502,8576,8656,8738,8810,8875,8947,8995,9068,9132,9207,9284,9346,9410,9473,9540,9624,9702,9782,9860,9914,10171", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "294,3306,3370,3439,3513,3592,4352,4458,4533,4782,4863,6020,6077,6164,6224,6282,6340,6399,6456,6510,6605,6661,6718,6772,6838,6942,7017,7094,7215,7280,7345,7445,7524,7599,7649,7700,7766,7830,7900,7977,8048,8116,8187,8254,8324,8417,8497,8571,8651,8733,8805,8870,8942,8990,9063,9127,9202,9279,9341,9405,9468,9535,9619,9697,9777,9855,9909,9964,10238"}}]}]}