// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.scanner3d.app.R;
import com.scanner3d.app.ui.custom.PointCloudView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityModelViewerBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton btnResetView;

  @NonNull
  public final ImageButton btnToggleTexture;

  @NonNull
  public final ImageButton btnToggleWireframe;

  @NonNull
  public final LinearLayout controlButtons;

  @NonNull
  public final FloatingActionButton fabInfo;

  @NonNull
  public final FloatingActionButton fabShare;

  @NonNull
  public final ImageView iconColors;

  @NonNull
  public final ImageView iconTexture;

  @NonNull
  public final LinearLayout infoPanel;

  @NonNull
  public final PointCloudView pointCloudView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final View qualityIndicator;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvQuality;

  @NonNull
  public final TextView tvScanDate;

  @NonNull
  public final TextView tvScanName;

  @NonNull
  public final TextView tvTriangleCount;

  @NonNull
  public final TextView tvVertexCount;

  private ActivityModelViewerBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton btnResetView, @NonNull ImageButton btnToggleTexture,
      @NonNull ImageButton btnToggleWireframe, @NonNull LinearLayout controlButtons,
      @NonNull FloatingActionButton fabInfo, @NonNull FloatingActionButton fabShare,
      @NonNull ImageView iconColors, @NonNull ImageView iconTexture,
      @NonNull LinearLayout infoPanel, @NonNull PointCloudView pointCloudView,
      @NonNull ProgressBar progressBar, @NonNull View qualityIndicator, @NonNull Toolbar toolbar,
      @NonNull TextView tvFileSize, @NonNull TextView tvQuality, @NonNull TextView tvScanDate,
      @NonNull TextView tvScanName, @NonNull TextView tvTriangleCount,
      @NonNull TextView tvVertexCount) {
    this.rootView = rootView;
    this.btnResetView = btnResetView;
    this.btnToggleTexture = btnToggleTexture;
    this.btnToggleWireframe = btnToggleWireframe;
    this.controlButtons = controlButtons;
    this.fabInfo = fabInfo;
    this.fabShare = fabShare;
    this.iconColors = iconColors;
    this.iconTexture = iconTexture;
    this.infoPanel = infoPanel;
    this.pointCloudView = pointCloudView;
    this.progressBar = progressBar;
    this.qualityIndicator = qualityIndicator;
    this.toolbar = toolbar;
    this.tvFileSize = tvFileSize;
    this.tvQuality = tvQuality;
    this.tvScanDate = tvScanDate;
    this.tvScanName = tvScanName;
    this.tvTriangleCount = tvTriangleCount;
    this.tvVertexCount = tvVertexCount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityModelViewerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityModelViewerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_model_viewer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityModelViewerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnResetView;
      ImageButton btnResetView = ViewBindings.findChildViewById(rootView, id);
      if (btnResetView == null) {
        break missingId;
      }

      id = R.id.btnToggleTexture;
      ImageButton btnToggleTexture = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleTexture == null) {
        break missingId;
      }

      id = R.id.btnToggleWireframe;
      ImageButton btnToggleWireframe = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleWireframe == null) {
        break missingId;
      }

      id = R.id.controlButtons;
      LinearLayout controlButtons = ViewBindings.findChildViewById(rootView, id);
      if (controlButtons == null) {
        break missingId;
      }

      id = R.id.fabInfo;
      FloatingActionButton fabInfo = ViewBindings.findChildViewById(rootView, id);
      if (fabInfo == null) {
        break missingId;
      }

      id = R.id.fabShare;
      FloatingActionButton fabShare = ViewBindings.findChildViewById(rootView, id);
      if (fabShare == null) {
        break missingId;
      }

      id = R.id.iconColors;
      ImageView iconColors = ViewBindings.findChildViewById(rootView, id);
      if (iconColors == null) {
        break missingId;
      }

      id = R.id.iconTexture;
      ImageView iconTexture = ViewBindings.findChildViewById(rootView, id);
      if (iconTexture == null) {
        break missingId;
      }

      id = R.id.infoPanel;
      LinearLayout infoPanel = ViewBindings.findChildViewById(rootView, id);
      if (infoPanel == null) {
        break missingId;
      }

      id = R.id.pointCloudView;
      PointCloudView pointCloudView = ViewBindings.findChildViewById(rootView, id);
      if (pointCloudView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.qualityIndicator;
      View qualityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (qualityIndicator == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvFileSize;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tvQuality;
      TextView tvQuality = ViewBindings.findChildViewById(rootView, id);
      if (tvQuality == null) {
        break missingId;
      }

      id = R.id.tvScanDate;
      TextView tvScanDate = ViewBindings.findChildViewById(rootView, id);
      if (tvScanDate == null) {
        break missingId;
      }

      id = R.id.tvScanName;
      TextView tvScanName = ViewBindings.findChildViewById(rootView, id);
      if (tvScanName == null) {
        break missingId;
      }

      id = R.id.tvTriangleCount;
      TextView tvTriangleCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTriangleCount == null) {
        break missingId;
      }

      id = R.id.tvVertexCount;
      TextView tvVertexCount = ViewBindings.findChildViewById(rootView, id);
      if (tvVertexCount == null) {
        break missingId;
      }

      return new ActivityModelViewerBinding((CoordinatorLayout) rootView, btnResetView,
          btnToggleTexture, btnToggleWireframe, controlButtons, fabInfo, fabShare, iconColors,
          iconTexture, infoPanel, pointCloudView, progressBar, qualityIndicator, toolbar,
          tvFileSize, tvQuality, tvScanDate, tvScanName, tvTriangleCount, tvVertexCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
