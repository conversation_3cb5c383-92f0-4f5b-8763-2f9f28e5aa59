<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.scanner3d.app"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- Required permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- Camera features -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />

    <!-- ARCore features -->
    <uses-feature
        android:name="android.hardware.camera.ar"
        android:required="true" />

    <!-- OpenGL ES 3.0 -->
    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <!-- Sensors -->
    <uses-feature
        android:name="android.hardware.sensor.accelerometer"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.sensor.gyroscope"
        android:required="true" />

    <queries>
        <intent>
            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
        </intent>

        <package android:name="com.google.ar.core" />
        <package android:name="com.android.vending" />

        <intent>
            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
        </intent>
    </queries>

    <permission
        android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.Scanner3D" >

        <!-- ARCore metadata -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="required" />

        <!-- Main Activity -->
        <activity
            android:name="com.scanner3d.app.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Scanning Activity -->
        <activity
            android:name="com.scanner3d.app.ui.scanning.ScanningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Model Viewer Activity -->
        <activity
            android:name="com.scanner3d.app.ui.model.ModelViewerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Gallery Activity -->
        <activity
            android:name="com.scanner3d.app.ui.gallery.GalleryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Authentication Activity -->
        <activity
            android:name="com.scanner3d.app.ui.auth.AuthenticationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- File Provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.scanner3d.app.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name="com.karumi.dexter.DexterActivity"
            android:theme="@style/Dexter.Internal.Theme.Transparent" />

        <uses-library
            android:name="androidx.camera.extensions.impl"
            android:required="false" />

        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.scanner3d.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" /> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
        <meta-data
            android:name="com.google.ar.core.min_apk_version"
            android:value="232620000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
        <activity
            android:name="com.google.ar.core.InstallActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>