package com.scanner3d.app.data.database

import androidx.room.*
import androidx.lifecycle.LiveData
import com.scanner3d.app.data.model.AppSettings
import kotlinx.coroutines.flow.Flow

@Dao
interface SettingsDao {
    
    @Query("SELECT * FROM settings WHERE id = 1")
    suspend fun getSettings(): AppSettings?
    
    @Query("SELECT * FROM settings WHERE id = 1")
    fun getSettingsLiveData(): LiveData<AppSettings?>
    
    @Query("SELECT * FROM settings WHERE id = 1")
    fun getSettingsFlow(): Flow<AppSettings?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: AppSettings)
    
    @Update
    suspend fun updateSettings(settings: AppSettings)
    
    @Query("UPDATE settings SET defaultScanQuality = :quality WHERE id = 1")
    suspend fun updateScanQuality(quality: String)
    
    @Query("UPDATE settings SET defaultExportFormat = :format WHERE id = 1")
    suspend fun updateExportFormat(format: String)
    
    @Query("UPDATE settings SET enableCloudSync = :enabled WHERE id = 1")
    suspend fun updateCloudSync(enabled: Boolean)
    
    @Query("UPDATE settings SET autoUpload = :enabled WHERE id = 1")
    suspend fun updateAutoUpload(enabled: Boolean)
    
    @Query("UPDATE settings SET theme = :theme WHERE id = 1")
    suspend fun updateTheme(theme: String)
    
    @Query("UPDATE settings SET language = :language WHERE id = 1")
    suspend fun updateLanguage(language: String)
    
    @Query("UPDATE settings SET enableGpuAcceleration = :enabled WHERE id = 1")
    suspend fun updateGpuAcceleration(enabled: Boolean)
    
    @Query("UPDATE settings SET maxMemoryUsage = :maxMemory WHERE id = 1")
    suspend fun updateMaxMemoryUsage(maxMemory: Long)
    
    @Query("UPDATE settings SET cacheSize = :cacheSize WHERE id = 1")
    suspend fun updateCacheSize(cacheSize: Long)
    
    @Query("UPDATE settings SET enableAnalytics = :enabled WHERE id = 1")
    suspend fun updateAnalytics(enabled: Boolean)
    
    @Query("UPDATE settings SET debugMode = :enabled WHERE id = 1")
    suspend fun updateDebugMode(enabled: Boolean)
    
    @Query("UPDATE settings SET lastModified = :timestamp WHERE id = 1")
    suspend fun updateLastModified(timestamp: Long)
    
    @Query("DELETE FROM settings")
    suspend fun deleteAllSettings()
}
