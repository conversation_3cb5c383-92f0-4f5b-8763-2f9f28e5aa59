/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback android.os.Parcelable android.os.Parcelable android.os.Parcelable android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable android.os.Parcelable) (androidx.appcompat.app.AppCompatActivity android.view.View android.opengl.GLSurfaceView& %android.opengl.GLSurfaceView.Renderer) (androidx.appcompat.app.AppCompatActivity) (androidx.recyclerview.widget.ListAdapter kotlin.Enum5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel kotlin.Enum$ #androidx.lifecycle.AndroidViewModel kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding android.opengl.GLSurfaceView& %android.opengl.GLSurfaceView.Renderer) (androidx.appcompat.app.AppCompatActivity) (androidx.recyclerview.widget.ListAdapter kotlin.Enum5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel!  androidx.viewbinding.ViewBinding