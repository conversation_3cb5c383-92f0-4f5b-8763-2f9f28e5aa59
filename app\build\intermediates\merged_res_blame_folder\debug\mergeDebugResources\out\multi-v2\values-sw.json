{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3896,3990,4092,4189,4290,4397,4504,11582", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3985,4087,4184,4285,4392,4499,4614,11678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,894,993,1101,1191,1296,1413,1496,1578,1669,1762,1857,1951,2051,2144,2239,2333,2424,2515,2597,2698,2806,2905,3012,3124,3228,3390,11499", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "889,988,1096,1186,1291,1408,1491,1573,1664,1757,1852,1946,2046,2139,2234,2328,2419,2510,2592,2693,2801,2900,3007,3119,3223,3385,3482,11577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,262,382,515,649,791,923,1066,1164,1291,1421", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "158,257,377,510,644,786,918,1061,1159,1286,1416,1541"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4930,5038,5297,5417,5550,5684,5826,5958,6101,6199,6326,6456", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "5033,5132,5412,5545,5679,5821,5953,6096,6194,6321,6451,6576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,117", "endOffsets": "155,273"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11195,11300", "endColumns": "104,117", "endOffsets": "11295,11413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2859,2947,3026,3080,3133,3199,3271,3353,3443,3528,3600,3675,3746,3819,3925,4022,4096,4191,4288,4362,4447,4547,4600,4685,4753,4841,4931,4993,5057,5120,5187,5304,5416,5527,5638,5696,5753", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2854,2942,3021,3075,3128,3194,3266,3348,3438,3523,3595,3670,3741,3814,3920,4017,4091,4186,4283,4357,4442,4542,4595,4680,4748,4836,4926,4988,5052,5115,5182,5299,5411,5522,5633,5691,5748,5829"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3487,3563,3637,3710,3807,4619,4718,4847,5137,5205,6581,6654,6717,6803,6865,6928,6993,7061,7124,7178,7310,7367,7429,7483,7557,7695,7776,7856,7988,8073,8160,8301,8389,8468,8522,8575,8641,8713,8795,8885,8970,9042,9117,9188,9261,9367,9464,9538,9633,9730,9804,9889,9989,10042,10127,10195,10283,10373,10435,10499,10562,10629,10746,10858,10969,11080,11138,11418", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "304,3558,3632,3705,3802,3891,4713,4842,4925,5200,5292,6649,6712,6798,6860,6923,6988,7056,7119,7173,7305,7362,7424,7478,7552,7690,7771,7851,7983,8068,8155,8296,8384,8463,8517,8570,8636,8708,8790,8880,8965,9037,9112,9183,9256,9362,9459,9533,9628,9725,9799,9884,9984,10037,10122,10190,10278,10368,10430,10494,10557,10624,10741,10853,10964,11075,11133,11190,11494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,287,417,554", "endColumns": "46,49,129,136,97", "endOffsets": "236,286,416,553,651"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "309,360,414,548,689", "endColumns": "50,53,133,140,101", "endOffsets": "355,409,543,684,786"}}]}]}