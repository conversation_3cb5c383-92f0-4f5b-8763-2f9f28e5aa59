{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,288,411,543", "endColumns": "46,50,122,131,85", "endOffsets": "236,287,410,542,628"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "316,367,422,549,685", "endColumns": "50,54,126,135,89", "endOffsets": "362,417,544,680,770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,878,981,1092,1176,1278,1391,1468,1543,1636,1731,1826,1920,2022,2117,2214,2312,2408,2501,2581,2687,2786,2882,2987,3090,3192,3346,11300", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "873,976,1087,1171,1273,1386,1463,1538,1631,1726,1821,1915,2017,2112,2209,2307,2403,2496,2576,2682,2781,2877,2982,3085,3187,3341,3443,11375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3902,3997,4099,4197,4296,4404,4509,11380", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3992,4094,4192,4291,4399,4504,4625,11476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4929,5038,5279,5400,5539,5669,5791,5920,6056,6161,6313,6465", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "5033,5119,5395,5534,5664,5786,5915,6051,6156,6308,6460,6587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1082,1174,1238,1298,1390,1455,1518,1580,1647,1711,1765,1870,1929,1990,2044,2113,2232,2315,2399,2535,2614,2698,2820,2906,2984,3038,3089,3155,3224,3298,3387,3463,3535,3612,3683,3757,3868,3959,4038,4125,4213,4285,4359,4444,4495,4574,4641,4722,4806,4868,4932,4995,5063,5170,5269,5368,5463,5521,5576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "261,360,452,533,635,715,813,935,1014,1077,1169,1233,1293,1385,1450,1513,1575,1642,1706,1760,1865,1924,1985,2039,2108,2227,2310,2394,2530,2609,2693,2815,2901,2979,3033,3084,3150,3219,3293,3382,3458,3530,3607,3678,3752,3863,3954,4033,4120,4208,4280,4354,4439,4490,4569,4636,4717,4801,4863,4927,4990,5058,5165,5264,5363,5458,5516,5571,5649"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3448,3547,3639,3720,3822,4630,4728,4850,5124,5187,6592,6656,6716,6808,6873,6936,6998,7065,7129,7183,7288,7347,7408,7462,7531,7650,7733,7817,7953,8032,8116,8238,8324,8402,8456,8507,8573,8642,8716,8805,8881,8953,9030,9101,9175,9286,9377,9456,9543,9631,9703,9777,9862,9913,9992,10059,10140,10224,10286,10350,10413,10481,10588,10687,10786,10881,10939,11222", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "311,3542,3634,3715,3817,3897,4723,4845,4924,5182,5274,6651,6711,6803,6868,6931,6993,7060,7124,7178,7283,7342,7403,7457,7526,7645,7728,7812,7948,8027,8111,8233,8319,8397,8451,8502,8568,8637,8711,8800,8876,8948,9025,9096,9170,9281,9372,9451,9538,9626,9698,9772,9857,9908,9987,10054,10135,10219,10281,10345,10408,10476,10583,10682,10781,10876,10934,10989,11295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,118", "endOffsets": "159,278"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10994,11103", "endColumns": "108,118", "endOffsets": "11098,11217"}}]}]}