package com.scanner3d.app.ui.auth

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.scanner3d.app.MainActivity
import com.scanner3d.app.databinding.ActivityAuthenticationBinding
import com.scanner3d.app.viewmodel.AuthViewModel

class AuthenticationActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityAuthenticationBinding
    private val viewModel: AuthViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAuthenticationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
        
        // Check if biometric is available and auto-trigger if it's the preferred method
        if (viewModel.isBiometricAvailable() && viewModel.getAuthMethod() == "biometric") {
            viewModel.authenticateWithBiometric(this)
        }
    }
    
    private fun setupUI() {
        binding.apply {
            btnUseBiometric.setOnClickListener {
                viewModel.authenticateWithBiometric(this@AuthenticationActivity)
            }
            
            btnUsePin.setOnClickListener {
                showPinInput()
            }
            
            btnSubmitPin.setOnClickListener {
                val pin = etPin.text.toString()
                if (pin.isNotEmpty()) {
                    viewModel.authenticateWithPin(pin)
                } else {
                    Toast.makeText(this@AuthenticationActivity, "Please enter PIN", Toast.LENGTH_SHORT).show()
                }
            }
        }
        
        // Show/hide biometric button based on availability
        binding.btnUseBiometric.isEnabled = viewModel.isBiometricAvailable()
        
        // Show/hide PIN button based on whether PIN is set
        binding.btnUsePin.isEnabled = viewModel.isPinSet()
        
        // If no authentication method is set up, guide user to set up PIN
        if (!viewModel.isBiometricAvailable() && !viewModel.isPinSet()) {
            showSetupPin()
        }
    }
    
    private fun observeViewModel() {
        viewModel.authenticationResult.observe(this, Observer { result ->
            when (result) {
                AuthViewModel.AuthResult.SUCCESS -> {
                    navigateToMain()
                }
                AuthViewModel.AuthResult.FAILURE -> {
                    Toast.makeText(this, "Authentication failed", Toast.LENGTH_SHORT).show()
                }
                AuthViewModel.AuthResult.ERROR -> {
                    Toast.makeText(this, "Authentication error", Toast.LENGTH_SHORT).show()
                }
            }
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        })
    }
    
    private fun showPinInput() {
        binding.apply {
            llPinInput.visibility = android.view.View.VISIBLE
            llAuthButtons.visibility = android.view.View.GONE
        }
    }
    
    private fun showSetupPin() {
        binding.apply {
            tvAuthSubtitle.text = "Set up a PIN to secure your app"
            btnUsePin.text = "Set up PIN"
            btnUsePin.isEnabled = true
            btnUsePin.setOnClickListener {
                showPinSetup()
            }
        }
    }
    
    private fun showPinSetup() {
        binding.apply {
            llPinInput.visibility = android.view.View.VISIBLE
            llAuthButtons.visibility = android.view.View.GONE
            tvPinLabel.text = "Create a new PIN"
            btnSubmitPin.text = "Set PIN"
            btnSubmitPin.setOnClickListener {
                val pin = etPin.text.toString()
                if (pin.length >= 4) {
                    viewModel.setupPin(pin)
                    Toast.makeText(this@AuthenticationActivity, "PIN set successfully", Toast.LENGTH_SHORT).show()
                    navigateToMain()
                } else {
                    Toast.makeText(this@AuthenticationActivity, "PIN must be at least 4 digits", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
}

