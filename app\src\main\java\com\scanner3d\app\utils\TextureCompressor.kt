package com.scanner3d.app.utils

import android.graphics.*
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import kotlin.math.*

class TextureCompressor {
    
    companion object {
        private const val TAG = "TextureCompressor"
    }
    
    enum class CompressionFormat {
        JPEG, PNG, WEBP, ETC1, ASTC
    }
    
    enum class CompressionQuality {
        LOW(60), MEDIUM(75), HIGH(85), ULTRA(95);
        
        val value: Int
        
        constructor(quality: Int) {
            this.value = quality
        }
    }
    
    suspend fun compressTexture(
        inputBitmap: Bitmap,
        format: CompressionFormat = CompressionFormat.JPEG,
        quality: CompressionQuality = CompressionQuality.HIGH,
        maxSize: Int = 1024
    ): CompressedTexture = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Starting texture compression. Original size: ${inputBitmap.width}x${inputBitmap.height}")
        
        try {
            // Resize if necessary
            val resizedBitmap = resizeTexture(inputBitmap, maxSize)
            
            // Apply compression based on format
            val compressedData = when (format) {
                CompressionFormat.JPEG -> compressToJPEG(resizedBitmap, quality)
                CompressionFormat.PNG -> compressToPNG(resizedBitmap)
                CompressionFormat.WEBP -> compressToWebP(resizedBitmap, quality)
                CompressionFormat.ETC1 -> compressToETC1(resizedBitmap)
                CompressionFormat.ASTC -> compressToASTC(resizedBitmap, quality)
            }
            
            val compressionRatio = inputBitmap.byteCount.toFloat() / compressedData.size.toFloat()
            
            Log.d(TAG, "Texture compression completed. Compression ratio: ${String.format("%.2f", compressionRatio)}")
            
            CompressedTexture(
                data = compressedData,
                width = resizedBitmap.width,
                height = resizedBitmap.height,
                format = format,
                originalSize = inputBitmap.byteCount,
                compressedSize = compressedData.size,
                compressionRatio = compressionRatio
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Texture compression failed", e)
            throw e
        }
    }
    
    private fun resizeTexture(bitmap: Bitmap, maxSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }
        
        val scale = minOf(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    private fun compressToJPEG(bitmap: Bitmap, quality: CompressionQuality): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality.value, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToPNG(bitmap: Bitmap): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToWebP(bitmap: Bitmap, quality: CompressionQuality): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.WEBP, quality.value, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToETC1(bitmap: Bitmap): ByteArray {
        // ETC1 compression is not directly supported in Android Bitmap
        // This would require native implementation or external library
        // For now, fall back to JPEG
        Log.w(TAG, "ETC1 compression not implemented, falling back to JPEG")
        return compressToJPEG(bitmap, CompressionQuality.HIGH)
    }
    
    private fun compressToASTC(bitmap: Bitmap, quality: CompressionQuality): ByteArray {
        // ASTC compression requires specialized implementation
        // For now, fall back to WebP
        Log.w(TAG, "ASTC compression not implemented, falling back to WebP")
        return compressToWebP(bitmap, quality)
    }
    
    suspend fun generateMipmaps(
        bitmap: Bitmap,
        levels: Int = 4
    ): List<Bitmap> = withContext(Dispatchers.Default) {
        
        val mipmaps = mutableListOf<Bitmap>()
        var currentBitmap = bitmap
        
        for (level in 0 until levels) {
            mipmaps.add(currentBitmap)
            
            if (level < levels - 1) {
                val newWidth = maxOf(1, currentBitmap.width / 2)
                val newHeight = maxOf(1, currentBitmap.height / 2)
                
                currentBitmap = Bitmap.createScaledBitmap(
                    currentBitmap, newWidth, newHeight, true
                )
            }
        }
        
        mipmaps
    }
    
    suspend fun optimizeTextureAtlas(
        textures: List<Bitmap>,
        atlasSize: Int = 2048
    ): TextureAtlas = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Creating texture atlas with ${textures.size} textures")
        
        // Sort textures by area (largest first) for better packing
        val sortedTextures = textures.mapIndexed { index, bitmap ->
            IndexedTexture(index, bitmap, bitmap.width * bitmap.height)
        }.sortedByDescending { it.area }
        
        // Create atlas bitmap
        val atlasBitmap = Bitmap.createBitmap(atlasSize, atlasSize, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(atlasBitmap)
        canvas.drawColor(Color.TRANSPARENT)
        
        // Pack textures using simple bin packing algorithm
        val packedTextures = mutableListOf<PackedTexture>()
        val freeRectangles = mutableListOf(Rectangle(0, 0, atlasSize, atlasSize))
        
        for (indexedTexture in sortedTextures) {
            val texture = indexedTexture.bitmap
            val bestFit = findBestFit(freeRectangles, texture.width, texture.height)
            
            if (bestFit != null) {
                // Draw texture to atlas
                canvas.drawBitmap(texture, bestFit.x.toFloat(), bestFit.y.toFloat(), null)
                
                // Calculate UV coordinates
                val uvCoords = floatArrayOf(
                    bestFit.x.toFloat() / atlasSize,
                    bestFit.y.toFloat() / atlasSize,
                    (bestFit.x + texture.width).toFloat() / atlasSize,
                    (bestFit.y + texture.height).toFloat() / atlasSize
                )
                
                packedTextures.add(
                    PackedTexture(
                        originalIndex = indexedTexture.index,
                        x = bestFit.x,
                        y = bestFit.y,
                        width = texture.width,
                        height = texture.height,
                        uvCoords = uvCoords
                    )
                )
                
                // Update free rectangles
                updateFreeRectangles(freeRectangles, bestFit, texture.width, texture.height)
            } else {
                Log.w(TAG, "Could not fit texture ${indexedTexture.index} in atlas")
            }
        }
        
        Log.d(TAG, "Texture atlas created with ${packedTextures.size}/${textures.size} textures")
        
        TextureAtlas(
            atlasBitmap = atlasBitmap,
            packedTextures = packedTextures,
            atlasSize = atlasSize,
            packingEfficiency = calculatePackingEfficiency(packedTextures, atlasSize)
        )
    }
    
    private fun findBestFit(
        freeRectangles: List<Rectangle>,
        width: Int,
        height: Int
    ): Rectangle? {
        var bestFit: Rectangle? = null
        var bestShortSideFit = Int.MAX_VALUE
        var bestLongSideFit = Int.MAX_VALUE
        
        for (rect in freeRectangles) {
            if (rect.width >= width && rect.height >= height) {
                val leftoverHorizontal = rect.width - width
                val leftoverVertical = rect.height - height
                val shortSideFit = minOf(leftoverHorizontal, leftoverVertical)
                val longSideFit = maxOf(leftoverHorizontal, leftoverVertical)
                
                if (shortSideFit < bestShortSideFit || 
                    (shortSideFit == bestShortSideFit && longSideFit < bestLongSideFit)) {
                    bestFit = Rectangle(rect.x, rect.y, width, height)
                    bestShortSideFit = shortSideFit
                    bestLongSideFit = longSideFit
                }
            }
        }
        
        return bestFit
    }
    
    private fun updateFreeRectangles(
        freeRectangles: MutableList<Rectangle>,
        usedRect: Rectangle,
        width: Int,
        height: Int
    ) {
        val iterator = freeRectangles.iterator()
        val newRectangles = mutableListOf<Rectangle>()
        
        while (iterator.hasNext()) {
            val rect = iterator.next()
            
            if (rect.intersects(usedRect)) {
                iterator.remove()
                
                // Split rectangle into smaller free rectangles
                if (rect.x < usedRect.x) {
                    newRectangles.add(Rectangle(rect.x, rect.y, usedRect.x - rect.x, rect.height))
                }
                if (rect.x + rect.width > usedRect.x + width) {
                    newRectangles.add(Rectangle(usedRect.x + width, rect.y, rect.x + rect.width - usedRect.x - width, rect.height))
                }
                if (rect.y < usedRect.y) {
                    newRectangles.add(Rectangle(rect.x, rect.y, rect.width, usedRect.y - rect.y))
                }
                if (rect.y + rect.height > usedRect.y + height) {
                    newRectangles.add(Rectangle(rect.x, usedRect.y + height, rect.width, rect.y + rect.height - usedRect.y - height))
                }
            }
        }
        
        freeRectangles.addAll(newRectangles)
    }
    
    private fun calculatePackingEfficiency(
        packedTextures: List<PackedTexture>,
        atlasSize: Int
    ): Float {
        val totalUsedArea = packedTextures.sumOf { it.width * it.height }
        val totalAtlasArea = atlasSize * atlasSize
        return totalUsedArea.toFloat() / totalAtlasArea.toFloat()
    }
    
    fun saveCompressedTexture(
        compressedTexture: CompressedTexture,
        outputFile: File
    ): Boolean {
        return try {
            FileOutputStream(outputFile).use { fos ->
                fos.write(compressedTexture.data)
            }
            Log.d(TAG, "Compressed texture saved to: ${outputFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save compressed texture", e)
            false
        }
    }
    
    // Data classes
    data class CompressedTexture(
        val data: ByteArray,
        val width: Int,
        val height: Int,
        val format: CompressionFormat,
        val originalSize: Int,
        val compressedSize: Int,
        val compressionRatio: Float
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            
            other as CompressedTexture
            
            if (!data.contentEquals(other.data)) return false
            if (width != other.width) return false
            if (height != other.height) return false
            if (format != other.format) return false
            if (originalSize != other.originalSize) return false
            if (compressedSize != other.compressedSize) return false
            if (compressionRatio != other.compressionRatio) return false
            
            return true
        }
        
        override fun hashCode(): Int {
            var result = data.contentHashCode()
            result = 31 * result + width
            result = 31 * result + height
            result = 31 * result + format.hashCode()
            result = 31 * result + originalSize
            result = 31 * result + compressedSize
            result = 31 * result + compressionRatio.hashCode()
            return result
        }
    }
    
    data class TextureAtlas(
        val atlasBitmap: Bitmap,
        val packedTextures: List<PackedTexture>,
        val atlasSize: Int,
        val packingEfficiency: Float
    )
    
    data class PackedTexture(
        val originalIndex: Int,
        val x: Int,
        val y: Int,
        val width: Int,
        val height: Int,
        val uvCoords: FloatArray
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            
            other as PackedTexture
            
            if (originalIndex != other.originalIndex) return false
            if (x != other.x) return false
            if (y != other.y) return false
            if (width != other.width) return false
            if (height != other.height) return false
            if (!uvCoords.contentEquals(other.uvCoords)) return false
            
            return true
        }
        
        override fun hashCode(): Int {
            var result = originalIndex
            result = 31 * result + x
            result = 31 * result + y
            result = 31 * result + width
            result = 31 * result + height
            result = 31 * result + uvCoords.contentHashCode()
            return result
        }
    }
    
    private data class IndexedTexture(
        val index: Int,
        val bitmap: Bitmap,
        val area: Int
    )
    
    private data class Rectangle(
        val x: Int,
        val y: Int,
        val width: Int,
        val height: Int
    ) {
        fun intersects(other: Rectangle): Boolean {
            return x < other.x + other.width &&
                    x + width > other.x &&
                    y < other.y + other.height &&
                    y + height > other.y
        }
    }
}
