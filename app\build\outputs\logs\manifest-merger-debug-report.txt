-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:107:9-115:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:109:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fd3b38fba3524d38bc0e4daf5273b99\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c664417371ff5614b4d21c49b85918f\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1be7d896a7a09740f262b8764d204bc\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b20cddf847dba612e0e16f1e2e84c046\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\476efbfcb8b866a7b0788c09c05866ec\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9595653f3b15d79b391917a5c404375\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\9800e13be01fa3d01bc10de3a3c75be2\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2802479f55e893d6eec2cd4b1973795\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\034bd1d6b5d4ef25e5c517dba1fa9426\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b53068dcc33b8aa7fe23846d3d15ec1\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\208925f131f6d7fd74e2017b9ae5b75e\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\08372b0b4714dee0e9823f116e744e60\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0060a6d1e9ec36933e7259d41494599\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbea9fcd79b718926845af0383e69ddd\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\647ac4ae376093ec810748bf13793797\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e631caef3437ee2681efbc1bba0c152c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20a1dafa70c64756b8d2d1ecb2c59921\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce18149f9b6e13b53daf57792013ceb0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90c754ff7f976aa957bdef8385fc4b1\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a4e3894532594be57b717e44248df27\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4e0c92d186db4e697e6895f4f520885\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\170778848436104a6347249c2a187fdd\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe8d850a7b6383d0b3b4b81261b7ec5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a322a89d1c7edc4f2a2d2dabcbf0ce50\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd22805cea923fd774d2d73a60c438c\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4dd9c2142ad84b2977e34650b2eecb\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d77792de92cba96e3de5663510f14696\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\62dde98c4a11ce7539b72efa94a291d8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07c3d9235f6963ecacd7c37042fbf37c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4974f768f7da700f517a8936031bf294\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f00f97a0c46151a50338a473a66a43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0c0de69dc330ef0d2047c7d1c4bd885\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a6d33be79cbde52cc357cc6721e36d2\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afcf86bf89c48fbb05fb09cd87d9bfaf\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d6e1a3524c2bfe16a09f7edcea1daed\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c36edfc3e74243efce3442fcf46e780e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51eaf809deebe046a24c25d4ced77043\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\97a6e0f6e30abca851ad5b9743d172a1\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9598a94723f79b9e993a4f848cfb87a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4af4a487a2d41eac02befc73a448142\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\926041289774f474b4f425d49cb4a430\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669f461e5584e746548cf6f9cd3340f1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac16d24a1b9cc2a5a17436854430a947\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78efd35d3f1558efa0fd2654864bf76a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\db3e3bcbf85aa1bb6a32879ff7ac02d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5d47451ebc178aee19d0e62ec7a1100\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e32cc3df4585848e27d5c76158f8b38a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d9e983846ab4d0ddae8defc40f638fd\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c858b016a1232c37b7a8f6b4942ce56b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3521bda98108ee2a0c0d8db0a7979b7\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ceec166d25f3c1360469b8b1d3a8b46\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c82092a3fb42390a8f761c35ef0a7ea\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2160ba4d602cbd2d8cc40a387b2cc621\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e52c31660366a3b135216a2a01587f3c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616897a9fb841c3e8bb0b719baf2d0a\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:8:1-45:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a29fafb990bd8b908185d3172a2f80\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e04988120ed68b4c56d6e434b48e6284\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0f05a6e71693a4f0bcbf36801348b3\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2812e0bb99b488e2f509fdd1e776c67d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b624761874d0dc46f58c580290523d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c3604a7d309ab82897b218a70b73f6\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0abeda04eecbd23506eacc036924c4b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44e153321763103352c792cbfcbfedf3\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6aff7ce4323be15b8a70dbe421f0769\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0df073035e98409ae32b301ccf681f86\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdfa83fb108897f47fa119bb3b078a32\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6a11d53ba672f288da81a70f4564a62\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d38d1ab083b6bb21205913f1e282e9e\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f49ff205b2bc1ad1e49b7a0a5e976eb\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\981988a5141267d8a9befdea1ee5cfbf\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\75946eaf75dfda24dd254a4437749b17\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d9dc16794dba9378d99afa8338841f4\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5ed09607c4402c9ad911e3cb9dc4ed9\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-23:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:23:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:24:5-26:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:9-57
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:9-53
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:5-34:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:34:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-50
uses-feature#0x00030000
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:5-39:35
	android:glEsVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-41
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:39:9-32
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:5-44:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:44:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-61
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:45:5-47:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:9-57
application
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0060a6d1e9ec36933e7259d41494599\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0060a6d1e9ec36933e7259d41494599\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbea9fcd79b718926845af0383e69ddd\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbea9fcd79b718926845af0383e69ddd\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b624761874d0dc46f58c580290523d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b624761874d0dc46f58c580290523d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f49ff205b2bc1ad1e49b7a0a5e976eb\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f49ff205b2bc1ad1e49b7a0a5e976eb\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\981988a5141267d8a9befdea1ee5cfbf\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\981988a5141267d8a9befdea1ee5cfbf\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d9dc16794dba9378d99afa8338841f4\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d9dc16794dba9378d99afa8338841f4\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5ed09607c4402c9ad911e3cb9dc4ed9\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5ed09607c4402c9ad911e3cb9dc4ed9\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:52:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:59:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-64:40
	android:value
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:64:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:13-46
activity#com.scanner3d.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:9-76:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:70:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-75:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:27-74
activity#com.scanner3d.app.ui.scanning.ScanningActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:79:9-83:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:82:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:80:13-57
activity#com.scanner3d.app.ui.model.ModelViewerActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:9-90:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:89:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:88:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-57
activity#com.scanner3d.app.ui.gallery.GalleryActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:9-97:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:96:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:95:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-55
activity#com.scanner3d.app.ui.auth.AuthenticationActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:9-104:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:103:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:102:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-59
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-114:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fd3b38fba3524d38bc0e4daf5273b99\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fd3b38fba3524d38bc0e4daf5273b99\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c664417371ff5614b4d21c49b85918f\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c664417371ff5614b4d21c49b85918f\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1be7d896a7a09740f262b8764d204bc\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1be7d896a7a09740f262b8764d204bc\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b20cddf847dba612e0e16f1e2e84c046\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b20cddf847dba612e0e16f1e2e84c046\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\476efbfcb8b866a7b0788c09c05866ec\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\476efbfcb8b866a7b0788c09c05866ec\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9595653f3b15d79b391917a5c404375\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9595653f3b15d79b391917a5c404375\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\9800e13be01fa3d01bc10de3a3c75be2\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\9800e13be01fa3d01bc10de3a3c75be2\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2802479f55e893d6eec2cd4b1973795\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2802479f55e893d6eec2cd4b1973795\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\034bd1d6b5d4ef25e5c517dba1fa9426\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\034bd1d6b5d4ef25e5c517dba1fa9426\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b53068dcc33b8aa7fe23846d3d15ec1\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b53068dcc33b8aa7fe23846d3d15ec1\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\208925f131f6d7fd74e2017b9ae5b75e\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\208925f131f6d7fd74e2017b9ae5b75e\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\08372b0b4714dee0e9823f116e744e60\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\08372b0b4714dee0e9823f116e744e60\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0060a6d1e9ec36933e7259d41494599\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f0060a6d1e9ec36933e7259d41494599\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbea9fcd79b718926845af0383e69ddd\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbea9fcd79b718926845af0383e69ddd\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\647ac4ae376093ec810748bf13793797\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\647ac4ae376093ec810748bf13793797\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e631caef3437ee2681efbc1bba0c152c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e631caef3437ee2681efbc1bba0c152c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20a1dafa70c64756b8d2d1ecb2c59921\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20a1dafa70c64756b8d2d1ecb2c59921\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad912a0b6595c0fd27017314ccf5845d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce18149f9b6e13b53daf57792013ceb0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce18149f9b6e13b53daf57792013ceb0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90c754ff7f976aa957bdef8385fc4b1\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90c754ff7f976aa957bdef8385fc4b1\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a4e3894532594be57b717e44248df27\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a4e3894532594be57b717e44248df27\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4e0c92d186db4e697e6895f4f520885\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4e0c92d186db4e697e6895f4f520885\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\170778848436104a6347249c2a187fdd\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\170778848436104a6347249c2a187fdd\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe8d850a7b6383d0b3b4b81261b7ec5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe8d850a7b6383d0b3b4b81261b7ec5\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a322a89d1c7edc4f2a2d2dabcbf0ce50\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a322a89d1c7edc4f2a2d2dabcbf0ce50\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd22805cea923fd774d2d73a60c438c\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd22805cea923fd774d2d73a60c438c\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4dd9c2142ad84b2977e34650b2eecb\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4dd9c2142ad84b2977e34650b2eecb\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d77792de92cba96e3de5663510f14696\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d77792de92cba96e3de5663510f14696\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\62dde98c4a11ce7539b72efa94a291d8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\62dde98c4a11ce7539b72efa94a291d8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07c3d9235f6963ecacd7c37042fbf37c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07c3d9235f6963ecacd7c37042fbf37c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4974f768f7da700f517a8936031bf294\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4974f768f7da700f517a8936031bf294\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f00f97a0c46151a50338a473a66a43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f00f97a0c46151a50338a473a66a43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0c0de69dc330ef0d2047c7d1c4bd885\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0c0de69dc330ef0d2047c7d1c4bd885\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a6d33be79cbde52cc357cc6721e36d2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a6d33be79cbde52cc357cc6721e36d2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afcf86bf89c48fbb05fb09cd87d9bfaf\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afcf86bf89c48fbb05fb09cd87d9bfaf\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d6e1a3524c2bfe16a09f7edcea1daed\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d6e1a3524c2bfe16a09f7edcea1daed\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c36edfc3e74243efce3442fcf46e780e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c36edfc3e74243efce3442fcf46e780e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51eaf809deebe046a24c25d4ced77043\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51eaf809deebe046a24c25d4ced77043\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\97a6e0f6e30abca851ad5b9743d172a1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\97a6e0f6e30abca851ad5b9743d172a1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9598a94723f79b9e993a4f848cfb87a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9598a94723f79b9e993a4f848cfb87a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4af4a487a2d41eac02befc73a448142\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4af4a487a2d41eac02befc73a448142\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\926041289774f474b4f425d49cb4a430\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\926041289774f474b4f425d49cb4a430\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669f461e5584e746548cf6f9cd3340f1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669f461e5584e746548cf6f9cd3340f1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac16d24a1b9cc2a5a17436854430a947\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac16d24a1b9cc2a5a17436854430a947\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78efd35d3f1558efa0fd2654864bf76a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78efd35d3f1558efa0fd2654864bf76a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\db3e3bcbf85aa1bb6a32879ff7ac02d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\db3e3bcbf85aa1bb6a32879ff7ac02d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5d47451ebc178aee19d0e62ec7a1100\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5d47451ebc178aee19d0e62ec7a1100\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e32cc3df4585848e27d5c76158f8b38a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e32cc3df4585848e27d5c76158f8b38a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d9e983846ab4d0ddae8defc40f638fd\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d9e983846ab4d0ddae8defc40f638fd\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c858b016a1232c37b7a8f6b4942ce56b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c858b016a1232c37b7a8f6b4942ce56b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3521bda98108ee2a0c0d8db0a7979b7\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3521bda98108ee2a0c0d8db0a7979b7\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ceec166d25f3c1360469b8b1d3a8b46\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ceec166d25f3c1360469b8b1d3a8b46\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c82092a3fb42390a8f761c35ef0a7ea\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c82092a3fb42390a8f761c35ef0a7ea\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2160ba4d602cbd2d8cc40a387b2cc621\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2160ba4d602cbd2d8cc40a387b2cc621\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e52c31660366a3b135216a2a01587f3c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e52c31660366a3b135216a2a01587f3c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616897a9fb841c3e8bb0b719baf2d0a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616897a9fb841c3e8bb0b719baf2d0a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a29fafb990bd8b908185d3172a2f80\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a29fafb990bd8b908185d3172a2f80\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e04988120ed68b4c56d6e434b48e6284\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e04988120ed68b4c56d6e434b48e6284\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0f05a6e71693a4f0bcbf36801348b3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0f05a6e71693a4f0bcbf36801348b3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2812e0bb99b488e2f509fdd1e776c67d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2812e0bb99b488e2f509fdd1e776c67d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b624761874d0dc46f58c580290523d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b624761874d0dc46f58c580290523d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c3604a7d309ab82897b218a70b73f6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c3604a7d309ab82897b218a70b73f6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0abeda04eecbd23506eacc036924c4b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0abeda04eecbd23506eacc036924c4b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44e153321763103352c792cbfcbfedf3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44e153321763103352c792cbfcbfedf3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6aff7ce4323be15b8a70dbe421f0769\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6aff7ce4323be15b8a70dbe421f0769\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0df073035e98409ae32b301ccf681f86\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0df073035e98409ae32b301ccf681f86\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdfa83fb108897f47fa119bb3b078a32\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdfa83fb108897f47fa119bb3b078a32\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6a11d53ba672f288da81a70f4564a62\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6a11d53ba672f288da81a70f4564a62\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d38d1ab083b6bb21205913f1e282e9e\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d38d1ab083b6bb21205913f1e282e9e\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f49ff205b2bc1ad1e49b7a0a5e976eb\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f49ff205b2bc1ad1e49b7a0a5e976eb\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\981988a5141267d8a9befdea1ee5cfbf\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\981988a5141267d8a9befdea1ee5cfbf\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\75946eaf75dfda24dd254a4437749b17\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\75946eaf75dfda24dd254a4437749b17\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d9dc16794dba9378d99afa8338841f4\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d9dc16794dba9378d99afa8338841f4\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5ed09607c4402c9ad911e3cb9dc4ed9\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5ed09607c4402c9ad911e3cb9dc4ed9\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6759dd37ffceb6bf618bbfd4316ef877\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
queries
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fc6275b963e7f02db03b24e9e17199\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4438ae44d00ea03d8820b2af4d8895e\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\015958a604512d531876a2a68dd87397\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f9bb0d763ac0836de210cc7304729a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e954809db4a5cbc3e0e97d4da247e0\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657e125b6dd63f8de9a85075f4d79bb4\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b986efbab3f35a8495809ca7b1ee567d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4281a9e238a025ecf10064f3998b616d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\89a65909e0f15cc36463db54333a1218\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
package#com.google.ar.core
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
package#com.android.vending
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
intent#action:name:com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
action#com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
	android:value
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
	android:exported
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
	android:theme
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa51a49a0fdb8d78736b292657adf1a3\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d9592d2c2a2183d4ca78a12b62f8e35\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
