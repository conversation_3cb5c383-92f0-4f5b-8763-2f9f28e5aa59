-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:107:9-115:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:109:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0ffd089c5b9ec56a5d169a141f6e0b5\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cd7c69d52236f27dd1b4afd59633382\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de7e4adec69d037ede21c43fa9c56e93\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9cafd367fd305e4d5788b67f234c3cf\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fcf9b42cc8748638807691c0b41fbef\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bed6cf14eebe43e594c9054ff5a275\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3abcf818f083752028c030d4b532b52c\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c92f42ddf4003043e4659c792dec4c6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fdf0ff09d1427fc5deaecc2c4f3cc8\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\585ec23ab79773737e947258f0327f61\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5235c14e723dbfe2a81342c008131f77\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc343c3e9bd9f0c6efa95dea879d2fd0\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c351a29c398c623fa23a89619fe48\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d34238975f512a3ab0f032ac0b13be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e0ef6a985d41a215ece56831964353e\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90e25b320a07392708b1a4db1e332b0\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5491f48b5d21281c9d1a28b04e1b6ebf\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3b1bb622a3e496303a762c8acf0b91\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f3850a7c512d8f41852114da6284cb\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7ebe25112460a5d9bb87361f3ccec1b\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5293dd4bf1b2e6452c54c978d80d8f4e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b96bce459ddea8f23697612961ed5af\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bfb0e00929830c5ef6e5b078e2aeea0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ba1f2062dce9b80823f01ba1568fa04\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\75dc5b42a227887e48bc78f5320fcae0\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d76fbc1a2d54ef8c07e98f69a6dd16e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e32a6c58df1ea640cdab52106d420df\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41b323f5d6c59eb83b1fc01fb91f28ac\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\857ddaf12e3a22dafd2915946a69d5c9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad1307a2fee3d5ea33c9fe11ce5c3c65\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\56011b26611d6a0db78c3039562e38b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9df2c60330ca1e07be27260bda5d361f\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe2c731ba4e1472fae16a45e3f4b3ba\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8efc7f69c749743f7182ddbba3e8b127\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38924a7cf6a5ed343081c35cf9aa8b66\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d43f9131f610e7717ecf6f90984b0823\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad0a4b4905bfb74b0df500fa29d49c06\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c967d4b68777462956c02ce7f24cf6e\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b39b4d116b6fa4009449801faf0455\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b62fc38da96b334960145b0558a1a1a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb2c401d2152d5d52477bd93a09a7c4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c73ce3652d0dbcbc9c29c423f2dbc211\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16de0efc744793686a788ba0f47d4011\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\898594dfd6781e6752a9e84c6d4d3e79\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd5bf5b68781bb557d3339484afe9c7b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\efeb8684bbfe8b4f82fad8b5de2fdd5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\01d03bee77d388a31884b1c19ccf9c9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c0b1d761e8a5383268685ffe09ea85b0\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03665e85b5b347934ad2b23a8f25f62c\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1ad1e2e92f508b597ec02ac491e46b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de75b9f21793cae47ccfa932438dd58d\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b45c1f3dff5d23dbf7eb265f05fee3\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca49e5751aecdbf89fb6ce33fa053ca\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\532daa887bf892cdcef8e8d3163989ad\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c838ef2744aa3c71370824d0f4ebad7\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar.sceneform.ux:sceneform-ux:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57ecfa42cf5d7e98ef75b5ebf31b7406\transformed\sceneform-ux-1.17.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar.sceneform:core:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b96b309c0a74f67a002d723ea774927\transformed\core-1.17.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.ar.sceneform:rendering:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee3c67de9e6e73e595b92acb0bf39993\transformed\rendering-1.17.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:8:1-45:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\e78682ab0dd6cdb26cae51cb11dd9b14\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6649326a4543016fcb87096ca105692\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9452a59272e4b6c275b9f952a4bcbbb0\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b7bcb23851063e438e3fdee2f192ad5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f020a46c650cec610a802acea858283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbd1a55921203ffafe6144c91021e4be\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\648f474b3064df877d4561e516f2808d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9fb904a0a55f6fe2e0293e85bbab9a8\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5114920d25536ce09184e087ab84d01\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53078e4262078bd9e01340f736048908\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40a4d110eed7b2c9556d99a70584d35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65c84b017d9c77e4074b8c7c14d0384b\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7455958be38209fad5a9c52b430818\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8e8ecd768129a168bbc3583ebfb32b4\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\827c1ca8a99de1863347c7fb9fdb3654\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.ar.sceneform:sceneform-base:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd6d3b27580c4c8697bc65839ab63e95\transformed\sceneform-base-1.17.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.ar.sceneform:filament-android:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d6b09653697e26e0273d8511067446\transformed\filament-android-1.17.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.support:support-fragment:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef010be1edaa2462b607393c24fa4ace\transformed\support-fragment-27.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\feeafe980e5a4b30cd7ba4c1c4adcbd0\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8339360476b51019071619a43177dd5d\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.support:support-core-ui:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c5b5b2a78e68bdda46eec081a759bc0\transformed\support-core-ui-27.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-utils:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7553e3655bc7c4a69dbe4e9ae0ed8f73\transformed\support-core-utils-27.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-compat:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74fdf6dc01a61c0fc2a5103020a398a8\transformed\support-compat-27.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2beee9d76e998bc1e2d175cf7b547fa3\transformed\livedata-core-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:viewmodel:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6bb223eddeb272b1df53945cda071a0\transformed\viewmodel-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09462038f8df00f0c39d58228d4f47fe\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [android.arch.lifecycle:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc04a405bd66ea0242f3c5583da7fc7\transformed\runtime-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.core:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\87085b2c792e4afa24cdb06a493fb2b1\transformed\runtime-1.1.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-23:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:23:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:24:5-26:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:9-57
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:9-53
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:5-34:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:34:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-50
uses-feature#0x00030000
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:5-39:35
	android:glEsVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-41
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:39:9-32
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:5-44:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:44:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-61
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:45:5-47:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:9-57
application
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c351a29c398c623fa23a89619fe48\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c351a29c398c623fa23a89619fe48\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d34238975f512a3ab0f032ac0b13be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d34238975f512a3ab0f032ac0b13be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f020a46c650cec610a802acea858283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f020a46c650cec610a802acea858283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8e8ecd768129a168bbc3583ebfb32b4\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8e8ecd768129a168bbc3583ebfb32b4\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\827c1ca8a99de1863347c7fb9fdb3654\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\827c1ca8a99de1863347c7fb9fdb3654\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8339360476b51019071619a43177dd5d\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8339360476b51019071619a43177dd5d\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09462038f8df00f0c39d58228d4f47fe\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09462038f8df00f0c39d58228d4f47fe\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:52:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:59:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-64:40
	android:value
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:64:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:13-46
activity#com.scanner3d.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:9-76:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:70:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-75:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:27-74
activity#com.scanner3d.app.ui.scanning.ScanningActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:79:9-83:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:82:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:80:13-57
activity#com.scanner3d.app.ui.model.ModelViewerActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:9-90:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:89:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:88:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-57
activity#com.scanner3d.app.ui.gallery.GalleryActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:9-97:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:96:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:95:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-55
activity#com.scanner3d.app.ui.auth.AuthenticationActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:9-104:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:103:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:102:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-59
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-114:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0ffd089c5b9ec56a5d169a141f6e0b5\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0ffd089c5b9ec56a5d169a141f6e0b5\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cd7c69d52236f27dd1b4afd59633382\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cd7c69d52236f27dd1b4afd59633382\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de7e4adec69d037ede21c43fa9c56e93\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de7e4adec69d037ede21c43fa9c56e93\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9cafd367fd305e4d5788b67f234c3cf\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9cafd367fd305e4d5788b67f234c3cf\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fcf9b42cc8748638807691c0b41fbef\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fcf9b42cc8748638807691c0b41fbef\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bed6cf14eebe43e594c9054ff5a275\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bed6cf14eebe43e594c9054ff5a275\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3abcf818f083752028c030d4b532b52c\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3abcf818f083752028c030d4b532b52c\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c92f42ddf4003043e4659c792dec4c6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c92f42ddf4003043e4659c792dec4c6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fdf0ff09d1427fc5deaecc2c4f3cc8\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\03fdf0ff09d1427fc5deaecc2c4f3cc8\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\585ec23ab79773737e947258f0327f61\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\585ec23ab79773737e947258f0327f61\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5235c14e723dbfe2a81342c008131f77\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5235c14e723dbfe2a81342c008131f77\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc343c3e9bd9f0c6efa95dea879d2fd0\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc343c3e9bd9f0c6efa95dea879d2fd0\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c351a29c398c623fa23a89619fe48\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c351a29c398c623fa23a89619fe48\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d34238975f512a3ab0f032ac0b13be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d34238975f512a3ab0f032ac0b13be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e0ef6a985d41a215ece56831964353e\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e0ef6a985d41a215ece56831964353e\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90e25b320a07392708b1a4db1e332b0\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e90e25b320a07392708b1a4db1e332b0\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5491f48b5d21281c9d1a28b04e1b6ebf\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5491f48b5d21281c9d1a28b04e1b6ebf\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fae1aef513bb18ebfc860e4ba437b55\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3b1bb622a3e496303a762c8acf0b91\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3b1bb622a3e496303a762c8acf0b91\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f3850a7c512d8f41852114da6284cb\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f3850a7c512d8f41852114da6284cb\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7ebe25112460a5d9bb87361f3ccec1b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7ebe25112460a5d9bb87361f3ccec1b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5293dd4bf1b2e6452c54c978d80d8f4e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5293dd4bf1b2e6452c54c978d80d8f4e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b96bce459ddea8f23697612961ed5af\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b96bce459ddea8f23697612961ed5af\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bfb0e00929830c5ef6e5b078e2aeea0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bfb0e00929830c5ef6e5b078e2aeea0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ba1f2062dce9b80823f01ba1568fa04\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ba1f2062dce9b80823f01ba1568fa04\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\75dc5b42a227887e48bc78f5320fcae0\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\75dc5b42a227887e48bc78f5320fcae0\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d76fbc1a2d54ef8c07e98f69a6dd16e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d76fbc1a2d54ef8c07e98f69a6dd16e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e32a6c58df1ea640cdab52106d420df\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e32a6c58df1ea640cdab52106d420df\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41b323f5d6c59eb83b1fc01fb91f28ac\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41b323f5d6c59eb83b1fc01fb91f28ac\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\857ddaf12e3a22dafd2915946a69d5c9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\857ddaf12e3a22dafd2915946a69d5c9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad1307a2fee3d5ea33c9fe11ce5c3c65\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad1307a2fee3d5ea33c9fe11ce5c3c65\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\56011b26611d6a0db78c3039562e38b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\56011b26611d6a0db78c3039562e38b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9df2c60330ca1e07be27260bda5d361f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9df2c60330ca1e07be27260bda5d361f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe2c731ba4e1472fae16a45e3f4b3ba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe2c731ba4e1472fae16a45e3f4b3ba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8efc7f69c749743f7182ddbba3e8b127\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8efc7f69c749743f7182ddbba3e8b127\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38924a7cf6a5ed343081c35cf9aa8b66\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38924a7cf6a5ed343081c35cf9aa8b66\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d43f9131f610e7717ecf6f90984b0823\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d43f9131f610e7717ecf6f90984b0823\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad0a4b4905bfb74b0df500fa29d49c06\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad0a4b4905bfb74b0df500fa29d49c06\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c967d4b68777462956c02ce7f24cf6e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c967d4b68777462956c02ce7f24cf6e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b39b4d116b6fa4009449801faf0455\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b39b4d116b6fa4009449801faf0455\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b62fc38da96b334960145b0558a1a1a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b62fc38da96b334960145b0558a1a1a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb2c401d2152d5d52477bd93a09a7c4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb2c401d2152d5d52477bd93a09a7c4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c73ce3652d0dbcbc9c29c423f2dbc211\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c73ce3652d0dbcbc9c29c423f2dbc211\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16de0efc744793686a788ba0f47d4011\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16de0efc744793686a788ba0f47d4011\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\898594dfd6781e6752a9e84c6d4d3e79\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\898594dfd6781e6752a9e84c6d4d3e79\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd5bf5b68781bb557d3339484afe9c7b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd5bf5b68781bb557d3339484afe9c7b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\efeb8684bbfe8b4f82fad8b5de2fdd5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\efeb8684bbfe8b4f82fad8b5de2fdd5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\01d03bee77d388a31884b1c19ccf9c9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\01d03bee77d388a31884b1c19ccf9c9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c0b1d761e8a5383268685ffe09ea85b0\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c0b1d761e8a5383268685ffe09ea85b0\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03665e85b5b347934ad2b23a8f25f62c\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03665e85b5b347934ad2b23a8f25f62c\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1ad1e2e92f508b597ec02ac491e46b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1ad1e2e92f508b597ec02ac491e46b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de75b9f21793cae47ccfa932438dd58d\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de75b9f21793cae47ccfa932438dd58d\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b45c1f3dff5d23dbf7eb265f05fee3\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\99b45c1f3dff5d23dbf7eb265f05fee3\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca49e5751aecdbf89fb6ce33fa053ca\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca49e5751aecdbf89fb6ce33fa053ca\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\532daa887bf892cdcef8e8d3163989ad\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\532daa887bf892cdcef8e8d3163989ad\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c838ef2744aa3c71370824d0f4ebad7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c838ef2744aa3c71370824d0f4ebad7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar.sceneform.ux:sceneform-ux:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57ecfa42cf5d7e98ef75b5ebf31b7406\transformed\sceneform-ux-1.17.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar.sceneform.ux:sceneform-ux:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57ecfa42cf5d7e98ef75b5ebf31b7406\transformed\sceneform-ux-1.17.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar.sceneform:core:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b96b309c0a74f67a002d723ea774927\transformed\core-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar.sceneform:core:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b96b309c0a74f67a002d723ea774927\transformed\core-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar.sceneform:rendering:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee3c67de9e6e73e595b92acb0bf39993\transformed\rendering-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar.sceneform:rendering:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee3c67de9e6e73e595b92acb0bf39993\transformed\rendering-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\e78682ab0dd6cdb26cae51cb11dd9b14\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\e78682ab0dd6cdb26cae51cb11dd9b14\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6649326a4543016fcb87096ca105692\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6649326a4543016fcb87096ca105692\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9452a59272e4b6c275b9f952a4bcbbb0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9452a59272e4b6c275b9f952a4bcbbb0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b7bcb23851063e438e3fdee2f192ad5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b7bcb23851063e438e3fdee2f192ad5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f020a46c650cec610a802acea858283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f020a46c650cec610a802acea858283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbd1a55921203ffafe6144c91021e4be\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbd1a55921203ffafe6144c91021e4be\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\648f474b3064df877d4561e516f2808d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\648f474b3064df877d4561e516f2808d\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9fb904a0a55f6fe2e0293e85bbab9a8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9fb904a0a55f6fe2e0293e85bbab9a8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5114920d25536ce09184e087ab84d01\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5114920d25536ce09184e087ab84d01\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53078e4262078bd9e01340f736048908\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53078e4262078bd9e01340f736048908\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40a4d110eed7b2c9556d99a70584d35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40a4d110eed7b2c9556d99a70584d35e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65c84b017d9c77e4074b8c7c14d0384b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65c84b017d9c77e4074b8c7c14d0384b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7455958be38209fad5a9c52b430818\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7455958be38209fad5a9c52b430818\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8e8ecd768129a168bbc3583ebfb32b4\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8e8ecd768129a168bbc3583ebfb32b4\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\827c1ca8a99de1863347c7fb9fdb3654\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\827c1ca8a99de1863347c7fb9fdb3654\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.ar.sceneform:sceneform-base:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd6d3b27580c4c8697bc65839ab63e95\transformed\sceneform-base-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar.sceneform:sceneform-base:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd6d3b27580c4c8697bc65839ab63e95\transformed\sceneform-base-1.17.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.ar.sceneform:filament-android:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d6b09653697e26e0273d8511067446\transformed\filament-android-1.17.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.ar.sceneform:filament-android:1.17.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\00d6b09653697e26e0273d8511067446\transformed\filament-android-1.17.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.support:support-fragment:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef010be1edaa2462b607393c24fa4ace\transformed\support-fragment-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef010be1edaa2462b607393c24fa4ace\transformed\support-fragment-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\feeafe980e5a4b30cd7ba4c1c4adcbd0\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\feeafe980e5a4b30cd7ba4c1c4adcbd0\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8339360476b51019071619a43177dd5d\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8339360476b51019071619a43177dd5d\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.android.support:support-core-ui:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c5b5b2a78e68bdda46eec081a759bc0\transformed\support-core-ui-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c5b5b2a78e68bdda46eec081a759bc0\transformed\support-core-ui-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7553e3655bc7c4a69dbe4e9ae0ed8f73\transformed\support-core-utils-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7553e3655bc7c4a69dbe4e9ae0ed8f73\transformed\support-core-utils-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74fdf6dc01a61c0fc2a5103020a398a8\transformed\support-compat-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:27.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74fdf6dc01a61c0fc2a5103020a398a8\transformed\support-compat-27.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2beee9d76e998bc1e2d175cf7b547fa3\transformed\livedata-core-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2beee9d76e998bc1e2d175cf7b547fa3\transformed\livedata-core-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6bb223eddeb272b1df53945cda071a0\transformed\viewmodel-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6bb223eddeb272b1df53945cda071a0\transformed\viewmodel-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09462038f8df00f0c39d58228d4f47fe\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09462038f8df00f0c39d58228d4f47fe\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc04a405bd66ea0242f3c5583da7fc7\transformed\runtime-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfc04a405bd66ea0242f3c5583da7fc7\transformed\runtime-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\87085b2c792e4afa24cdb06a493fb2b1\transformed\runtime-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\87085b2c792e4afa24cdb06a493fb2b1\transformed\runtime-1.1.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2db83d0106d284a671cd87b35049e473\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
queries
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42b153a986fe92f78f39fb1581470411\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2970d4da5b683f281d7fe02b1053ef80\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\53facdbd419d022351aa4ce68043f8b3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0344850b8120661ff7383304db0dcb4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b988c870ee1218c00141d1d23be9af84\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4f9abd86db8b3347c23268b39f05a1\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70befc9c16dd9e301c153021ec73a9fd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e62b7f077ea728ca1bca102ae9fae8f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8a1ad60e413b290ad874edd30efe6a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
package#com.google.ar.core
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
package#com.android.vending
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
intent#action:name:com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
action#com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
	android:value
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
	android:exported
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
	android:theme
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8587d926d346ccf2176f046e22a2a17\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e97241bc618c433d97a1ef3787a49b7\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
