-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:9-119:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:115:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-123:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-123:12
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ab34ff4256c9ebd489eac2527a73d09\transformed\viewbinding-8.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1fd76347ea670c8818b42e1906a4d5\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f182cda7a794c0877d63a5e1e1a11dd0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d51ab36bb0578bd346d1cea7965eb21\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0979160004867a8852302b491188c91f\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e46fadf86c1ac2d83b0f60c8bb586523\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\bff47390bc3121e43d79fa36f4b6ad57\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51a8b1be6781ad6ccce30880f9de290\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4054160278556e062d4ea0b7d8eca303\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2fbb2a044127634c9d3a6fc3f78750e9\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f6831add56bce2cd7b2e058488827af\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd1b59c0557590abbac9f5e69e84cdf8\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40910c13fde692677741cc7560f27af\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccc540591e25493f98037aae4cbcc0db\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a6e194ba6cea471c112f7058082ab29\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\429a30bcc688c4b342e3c444713d0398\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c66876c10aff7cf03c40da97aa8889a3\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb896db10dd09400b4d48637b63b877\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ab9b83c1e59827fd1c649187c4c5e1\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\73319877535ab529488555e86de1d51c\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f9f84bb3fe027a2ce6388dc1fdf464\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\02ff1bbf02f3c4baf3dfd6c701a840f0\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7984f6b32418b6ddaaca3492c4cded88\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\869d11a03347bbd3ead42d4bafa16ed2\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\823eacad108f81ead22f93972bfda8b1\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7780c2ae0fae9afeb605ecbab0a2886\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b4892964480d351ccd6f42a585d08d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\788d32049f569f34730c2b4f6ac2b3e6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54e068ee31819bdf4b32461211ba87b5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c3458466a63717483730c2c4f70e04\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\febe3de926d3f5439117165256b4f75c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\12e9921e3ba941fbd6afd26a05b4e4bd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3849c5c56ab9eb624b2cb1be939eeda6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2ea120487aa810faa67701896fac49b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e79d37237929a608c608dd5b3fab709a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274335007f9712bedbfde253a50c95eb\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a2831bced27b5b8c51875ecb3eeca58\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90e303acb722b16c0ffb7926b1706bc4\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdbb71a5f4e92653f25c4b0c9ae2e802\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3b02d1b9a0f7278b8d13df867408d2f\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90885bd235e49e2e3033ae2ef306da16\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1195cb43549015747896bf52475350\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fabded84fdc37d92a1754651bf348ebf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f229db8acd39cb28a8df19ba615c67d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbbe260c6098b18e6ffafa91d8cbd14\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d65b1e92ee4aa3f6c9619cbb9784450f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\136fda9f057abde54f72597b860952ab\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8591989bf49afec642e7be38999bd6a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae85649159e2e43347c505da06dab66b\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e475d0ef2c50560bd50527779df7f26\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3562f9188f8d4c0deea4e6900b2e30a4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7920752e81797a58de732ad81f87c1d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e645e167170fba622c9be7ae548c267\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:8:1-45:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\aba152902c86939f7a1dfbe8cd4e745a\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096c8b76f035d51f89161cf8f28dc49e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe974c5cef158e3f57f9a8b6ed909e3\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dda61905c6d01c0abd5af966fb4cda79\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dd8f7ee484c54c356111b791ad07385\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75da127defd3bb2b7c476a719d4a6567\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\156ed090f885f493677532b662ca7f6e\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e507877eaa644817562988158f668e27\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c2326a151ed413ed0e794e8b6f3f373\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\903a3c861c43d5c4d869f5abdde5aceb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d123713c8fe24ed804e4071b1adfc314\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cbf6483114a80023953f2f45ff37d73\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\acc5f1f8838239f608df4801af273849\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5dc6531262961ddbd4a8cc27f5f7c3\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6f033fd7285d17a6fe47c5d9cd138d2\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\abb804bfe4c3209cba2a1d527117188b\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd86f354946270ea3deb48f3d038ae6f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9072879637134292d2ede697ee1bf1d\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:22-78
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:5-27:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:5-30:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:30:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-57
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:31:5-33:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:9-53
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:9-50
uses-feature#0x00030000
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:41:5-43:35
	android:glEsVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:9-41
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-32
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:5-48:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:48:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-61
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-51:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-57
application
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:5-121:19
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:5-121:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2fbb2a044127634c9d3a6fc3f78750e9\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2fbb2a044127634c9d3a6fc3f78750e9\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f6831add56bce2cd7b2e058488827af\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f6831add56bce2cd7b2e058488827af\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75da127defd3bb2b7c476a719d4a6567\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75da127defd3bb2b7c476a719d4a6567\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5dc6531262961ddbd4a8cc27f5f7c3\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5dc6531262961ddbd4a8cc27f5f7c3\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6f033fd7285d17a6fe47c5d9cd138d2\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6f033fd7285d17a6fe47c5d9cd138d2\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd86f354946270ea3deb48f3d038ae6f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd86f354946270ea3deb48f3d038ae6f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9072879637134292d2ede697ee1bf1d\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9072879637134292d2ede697ee1bf1d\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:60:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:59:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:61:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:66:9-68:40
	android:value
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:13-46
activity#com.scanner3d.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:9-80:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:75:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:76:13-79:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:77:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:77:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:78:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:78:27-74
activity#com.scanner3d.app.ui.scanning.ScanningActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:9-87:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:85:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:84:13-57
activity#com.scanner3d.app.ui.model.ModelViewerActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:9-94:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:92:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:91:13-57
activity#com.scanner3d.app.ui.gallery.GalleryActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:9-101:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:99:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:98:13-55
activity#com.scanner3d.app.ui.auth.AuthenticationActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:9-108:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:107:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:106:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:105:13-59
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:116:13-118:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:118:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:117:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ab34ff4256c9ebd489eac2527a73d09\transformed\viewbinding-8.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ab34ff4256c9ebd489eac2527a73d09\transformed\viewbinding-8.1.4\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1fd76347ea670c8818b42e1906a4d5\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1fd76347ea670c8818b42e1906a4d5\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f182cda7a794c0877d63a5e1e1a11dd0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f182cda7a794c0877d63a5e1e1a11dd0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d51ab36bb0578bd346d1cea7965eb21\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d51ab36bb0578bd346d1cea7965eb21\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0979160004867a8852302b491188c91f\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\0979160004867a8852302b491188c91f\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e46fadf86c1ac2d83b0f60c8bb586523\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e46fadf86c1ac2d83b0f60c8bb586523\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\bff47390bc3121e43d79fa36f4b6ad57\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\bff47390bc3121e43d79fa36f4b6ad57\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51a8b1be6781ad6ccce30880f9de290\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51a8b1be6781ad6ccce30880f9de290\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4054160278556e062d4ea0b7d8eca303\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4054160278556e062d4ea0b7d8eca303\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2fbb2a044127634c9d3a6fc3f78750e9\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2fbb2a044127634c9d3a6fc3f78750e9\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f6831add56bce2cd7b2e058488827af\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f6831add56bce2cd7b2e058488827af\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd1b59c0557590abbac9f5e69e84cdf8\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd1b59c0557590abbac9f5e69e84cdf8\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40910c13fde692677741cc7560f27af\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40910c13fde692677741cc7560f27af\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccc540591e25493f98037aae4cbcc0db\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccc540591e25493f98037aae4cbcc0db\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddabe16ea9ddf7c286dc8364cabf1745\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a6e194ba6cea471c112f7058082ab29\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a6e194ba6cea471c112f7058082ab29\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\429a30bcc688c4b342e3c444713d0398\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\429a30bcc688c4b342e3c444713d0398\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c66876c10aff7cf03c40da97aa8889a3\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c66876c10aff7cf03c40da97aa8889a3\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb896db10dd09400b4d48637b63b877\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb896db10dd09400b4d48637b63b877\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ab9b83c1e59827fd1c649187c4c5e1\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ab9b83c1e59827fd1c649187c4c5e1\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\73319877535ab529488555e86de1d51c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\73319877535ab529488555e86de1d51c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f9f84bb3fe027a2ce6388dc1fdf464\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5f9f84bb3fe027a2ce6388dc1fdf464\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\02ff1bbf02f3c4baf3dfd6c701a840f0\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\02ff1bbf02f3c4baf3dfd6c701a840f0\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7984f6b32418b6ddaaca3492c4cded88\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7984f6b32418b6ddaaca3492c4cded88\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\869d11a03347bbd3ead42d4bafa16ed2\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\869d11a03347bbd3ead42d4bafa16ed2\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\823eacad108f81ead22f93972bfda8b1\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\823eacad108f81ead22f93972bfda8b1\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7780c2ae0fae9afeb605ecbab0a2886\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7780c2ae0fae9afeb605ecbab0a2886\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b4892964480d351ccd6f42a585d08d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b4892964480d351ccd6f42a585d08d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\788d32049f569f34730c2b4f6ac2b3e6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\788d32049f569f34730c2b4f6ac2b3e6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54e068ee31819bdf4b32461211ba87b5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54e068ee31819bdf4b32461211ba87b5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c3458466a63717483730c2c4f70e04\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f7c3458466a63717483730c2c4f70e04\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\febe3de926d3f5439117165256b4f75c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\febe3de926d3f5439117165256b4f75c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\12e9921e3ba941fbd6afd26a05b4e4bd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\12e9921e3ba941fbd6afd26a05b4e4bd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3849c5c56ab9eb624b2cb1be939eeda6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3849c5c56ab9eb624b2cb1be939eeda6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2ea120487aa810faa67701896fac49b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2ea120487aa810faa67701896fac49b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e79d37237929a608c608dd5b3fab709a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e79d37237929a608c608dd5b3fab709a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274335007f9712bedbfde253a50c95eb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274335007f9712bedbfde253a50c95eb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a2831bced27b5b8c51875ecb3eeca58\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a2831bced27b5b8c51875ecb3eeca58\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90e303acb722b16c0ffb7926b1706bc4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90e303acb722b16c0ffb7926b1706bc4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdbb71a5f4e92653f25c4b0c9ae2e802\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdbb71a5f4e92653f25c4b0c9ae2e802\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3b02d1b9a0f7278b8d13df867408d2f\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3b02d1b9a0f7278b8d13df867408d2f\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90885bd235e49e2e3033ae2ef306da16\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90885bd235e49e2e3033ae2ef306da16\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1195cb43549015747896bf52475350\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1195cb43549015747896bf52475350\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fabded84fdc37d92a1754651bf348ebf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fabded84fdc37d92a1754651bf348ebf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f229db8acd39cb28a8df19ba615c67d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f229db8acd39cb28a8df19ba615c67d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbbe260c6098b18e6ffafa91d8cbd14\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbbe260c6098b18e6ffafa91d8cbd14\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d65b1e92ee4aa3f6c9619cbb9784450f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d65b1e92ee4aa3f6c9619cbb9784450f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\136fda9f057abde54f72597b860952ab\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\136fda9f057abde54f72597b860952ab\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8591989bf49afec642e7be38999bd6a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8591989bf49afec642e7be38999bd6a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae85649159e2e43347c505da06dab66b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae85649159e2e43347c505da06dab66b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e475d0ef2c50560bd50527779df7f26\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e475d0ef2c50560bd50527779df7f26\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3562f9188f8d4c0deea4e6900b2e30a4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3562f9188f8d4c0deea4e6900b2e30a4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7920752e81797a58de732ad81f87c1d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7920752e81797a58de732ad81f87c1d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e645e167170fba622c9be7ae548c267\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e645e167170fba622c9be7ae548c267\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\aba152902c86939f7a1dfbe8cd4e745a\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\aba152902c86939f7a1dfbe8cd4e745a\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096c8b76f035d51f89161cf8f28dc49e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096c8b76f035d51f89161cf8f28dc49e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe974c5cef158e3f57f9a8b6ed909e3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fe974c5cef158e3f57f9a8b6ed909e3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dda61905c6d01c0abd5af966fb4cda79\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dda61905c6d01c0abd5af966fb4cda79\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dd8f7ee484c54c356111b791ad07385\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1dd8f7ee484c54c356111b791ad07385\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75da127defd3bb2b7c476a719d4a6567\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75da127defd3bb2b7c476a719d4a6567\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\156ed090f885f493677532b662ca7f6e\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\156ed090f885f493677532b662ca7f6e\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e507877eaa644817562988158f668e27\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e507877eaa644817562988158f668e27\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c2326a151ed413ed0e794e8b6f3f373\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c2326a151ed413ed0e794e8b6f3f373\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\903a3c861c43d5c4d869f5abdde5aceb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\903a3c861c43d5c4d869f5abdde5aceb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d123713c8fe24ed804e4071b1adfc314\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d123713c8fe24ed804e4071b1adfc314\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cbf6483114a80023953f2f45ff37d73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cbf6483114a80023953f2f45ff37d73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\acc5f1f8838239f608df4801af273849\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\acc5f1f8838239f608df4801af273849\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5dc6531262961ddbd4a8cc27f5f7c3\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5dc6531262961ddbd4a8cc27f5f7c3\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6f033fd7285d17a6fe47c5d9cd138d2\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6f033fd7285d17a6fe47c5d9cd138d2\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\abb804bfe4c3209cba2a1d527117188b\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\abb804bfe4c3209cba2a1d527117188b\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd86f354946270ea3deb48f3d038ae6f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd86f354946270ea3deb48f3d038ae6f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9072879637134292d2ede697ee1bf1d\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9072879637134292d2ede697ee1bf1d\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b93e738283c46478cd9d4bf25da0e00\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
queries
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9381b22b1029de9822e8161bbc966510\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\602bea843d607eea425ff8365681eddc\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72c23afaa42feb1e51ad5958bc40ef81\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5696de7cc23ede19e1f517f25ca5750\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\adc2630105fcd46b60bd65d7a56f990f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ba07615a3caeaba61d2dafcda0299b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3230fd2a7a34684e89ced05b023027\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e404b2167f4628be61952201c68d167\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc5e64aabe2052fce22d9553e18a88af\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
package#com.google.ar.core
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
package#com.android.vending
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
intent#action:name:com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
action#com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
	android:value
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
	android:exported
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
	android:theme
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\955c5ec1b827236d7c0018924d2d14b2\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e911dcbf693b5df6deae993e86babe5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
