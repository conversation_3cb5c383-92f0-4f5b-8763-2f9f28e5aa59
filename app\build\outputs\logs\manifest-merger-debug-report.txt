-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:107:9-115:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:111:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:109:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:108:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:1-119:12
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd4ab03ea82f056a0d4a7e93defd7e17\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\154cd5946e6d70ce8bdef1bceaa67ea7\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de18706db4c4047aa6cb6c436568cf80\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b04085fd66464b0985638976eb5a52d\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a20e9d70c76efb2035b44c4af52780a\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e6924f719162331c0b9b04fd0711e5\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c64ba7623f2c6465d41d4c629492c0fa\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f4d7ff714c104918c83ee25b7caef8c\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffc1e54d245e69c46f28d3fc5345e887\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdd65b177a20b3653391fda79f860486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\99187b142ce1f4df6b06b104fdf98564\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36866d4b2dcf3202b3505f64db5ac044\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7bc507de6eea8b94b2b424380ec10ff\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd67f359f138dd59792a3b156fc79cc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdf1c6310760f3e8ec9ae10f4361ffc3\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c35b3c0d75289cdfd7a5c2e0f823b1d\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d259c972d012ec17011a384e78667746\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3624584e491bf52884281e48bfb7c91a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eba1fadeac71389c08443fad8408d732\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c3b434ebf1baa4b4f185c4d82742fb7\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1c68806c1cbd3e8bd83cd70ce43fb93\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\90931ea03af215c8ac485bf4e3cfca5d\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63ff54561e98d3efcd5b1750f94fd2cb\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a2017bc9cdabb70d8e393a9345e4ef5\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\89aa6437d243f5cd5b7a561c35319165\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65651b69d61ed87f03dfdca9bb335f22\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c192d65f278eab31a1efb9e104bf07\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9a10cc38f566a216281704b9741505a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bfced9a1f1880336970c4371b6094c8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eeb88d4865e24a3e04aaf4b173fe3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b7a2e6fafa5cbfaa7e710a0b8f28fa8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7f6f71de4fa66288728fb74fbaf5917\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f232168a5e9f503deeee635e2fca0dc\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2726f8cee7ffa0d25548bed660b5fd5f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f6d8d3f2d1254a4d375b3d770b5db21\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\225b6253df36812118298ce0168b0d04\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08426a2fcc9d38046334c66a53e22d4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eafecbd1fbd0c1f6979cc8e4ab731ea\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bae2f9282aa3a580021af25788e9ad81\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\825a99d4c3950fcd4aa6e41167ad52fd\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\026390448bfc7f5c1c1a4c6cc2ec2c65\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\256335a5ea3f54fdbb1b8b7cbed2de6d\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4e3591d986761af58212099d154c87b\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74ac3e623d7a1a20f6bcc1c1af7d8c03\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e8023d571a8e7702052021337eb7ae2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acb1c05ec2b5de0eae17642645b54bfd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7870523f381be03945c2ae2b350b995a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8b1f146db077014c250cba9ce528d25\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3d0109f7c2eae6aaca433c0bd7f5d90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6941d74c7ab7962996d34176830e32b6\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\740fac41411f6d546ba3df8692a1e93a\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5491606d8f4c007c86eb4b55fa81e2f\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde012d300f5538be0a3fec38c3791e4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c07989a3a5698f824ecd1b7999a9f360\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d6c07f743127ddd5360d6954f74ebee\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:8:1-45:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b42c3a5b161d604102716c5dee98ca8\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9e7c23061395ce528ff094266f2790\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b27360397b7a5229eb5bb3ea788bfa65\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\074bee8d2e1454a1198ca0178bb97d77\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f592b50139c4bd0aed2e0a58de97990\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\656a488aa119732ce6545f2c2435d004\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45980c1ab77be3ae4f20a05b1525c6d\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e138819e4ee3a3de9f18e708b2b48ced\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1ce1b87897ded0f11c618a585bc7a0b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bff0567b0c6eeea188861c51cea3a0d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e88a6a110032ffe3eee7362c86472a9\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\821feed66bb7b312852f8270860ea527\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d58993a7da3d45b5987e8dc6216c641\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8fdd6f1dbd7ecc4c1ae946e5c4b6026f\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca71908c7fdafe3a3122d5723c1c8012\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab73eb3100e01862391f610aee1e45b\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73bfdc82f56547534f2af440fd99a0c5\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:17:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:18:22-71
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:21:5-23:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:23:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:22:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:24:5-26:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:26:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:25:9-57
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:28:9-53
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:32:5-34:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:34:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:33:9-50
uses-feature#0x00030000
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:37:5-39:35
	android:glEsVersion
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:38:9-41
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:39:9-32
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:42:5-44:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:44:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:43:9-61
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:45:5-47:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:47:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:46:9-57
application
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:49:5-117:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7bc507de6eea8b94b2b424380ec10ff\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7bc507de6eea8b94b2b424380ec10ff\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd67f359f138dd59792a3b156fc79cc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd67f359f138dd59792a3b156fc79cc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:29:5-43:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\074bee8d2e1454a1198ca0178bb97d77\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\074bee8d2e1454a1198ca0178bb97d77\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d58993a7da3d45b5987e8dc6216c641\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d58993a7da3d45b5987e8dc6216c641\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8fdd6f1dbd7ecc4c1ae946e5c4b6026f\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8fdd6f1dbd7ecc4c1ae946e5c4b6026f\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab73eb3100e01862391f610aee1e45b\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab73eb3100e01862391f610aee1e45b\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73bfdc82f56547534f2af440fd99a0c5\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73bfdc82f56547534f2af440fd99a0c5\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:56:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:54:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:58:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:52:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:55:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:59:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:53:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:50:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:57:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:51:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:62:9-64:40
	android:value
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:64:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:63:13-46
activity#com.scanner3d.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:67:9-76:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:70:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:71:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:68:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:72:13-75:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:73:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:74:27-74
activity#com.scanner3d.app.ui.scanning.ScanningActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:79:9-83:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:82:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:83:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:80:13-57
activity#com.scanner3d.app.ui.model.ModelViewerActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:86:9-90:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:89:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:88:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:90:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:87:13-57
activity#com.scanner3d.app.ui.gallery.GalleryActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:93:9-97:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:96:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:95:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:97:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:94:13-55
activity#com.scanner3d.app.ui.auth.AuthenticationActivity
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:100:9-104:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:103:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:102:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:104:13-63
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:101:13-59
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:112:13-114:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:114:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml:113:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd4ab03ea82f056a0d4a7e93defd7e17\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd4ab03ea82f056a0d4a7e93defd7e17\transformed\databinding-adapters-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\154cd5946e6d70ce8bdef1bceaa67ea7\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\154cd5946e6d70ce8bdef1bceaa67ea7\transformed\databinding-ktx-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de18706db4c4047aa6cb6c436568cf80\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\de18706db4c4047aa6cb6c436568cf80\transformed\databinding-runtime-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b04085fd66464b0985638976eb5a52d\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b04085fd66464b0985638976eb5a52d\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a20e9d70c76efb2035b44c4af52780a\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a20e9d70c76efb2035b44c4af52780a\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e6924f719162331c0b9b04fd0711e5\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e6924f719162331c0b9b04fd0711e5\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c64ba7623f2c6465d41d4c629492c0fa\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c64ba7623f2c6465d41d4c629492c0fa\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f4d7ff714c104918c83ee25b7caef8c\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f4d7ff714c104918c83ee25b7caef8c\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffc1e54d245e69c46f28d3fc5345e887\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffc1e54d245e69c46f28d3fc5345e887\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdd65b177a20b3653391fda79f860486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdd65b177a20b3653391fda79f860486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\99187b142ce1f4df6b06b104fdf98564\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\99187b142ce1f4df6b06b104fdf98564\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36866d4b2dcf3202b3505f64db5ac044\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36866d4b2dcf3202b3505f64db5ac044\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7bc507de6eea8b94b2b424380ec10ff\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7bc507de6eea8b94b2b424380ec10ff\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd67f359f138dd59792a3b156fc79cc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd67f359f138dd59792a3b156fc79cc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdf1c6310760f3e8ec9ae10f4361ffc3\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdf1c6310760f3e8ec9ae10f4361ffc3\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c35b3c0d75289cdfd7a5c2e0f823b1d\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c35b3c0d75289cdfd7a5c2e0f823b1d\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d259c972d012ec17011a384e78667746\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d259c972d012ec17011a384e78667746\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0797190b21212baeb7d2979587e3aa46\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3624584e491bf52884281e48bfb7c91a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3624584e491bf52884281e48bfb7c91a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eba1fadeac71389c08443fad8408d732\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eba1fadeac71389c08443fad8408d732\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c3b434ebf1baa4b4f185c4d82742fb7\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c3b434ebf1baa4b4f185c4d82742fb7\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1c68806c1cbd3e8bd83cd70ce43fb93\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1c68806c1cbd3e8bd83cd70ce43fb93\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\90931ea03af215c8ac485bf4e3cfca5d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\90931ea03af215c8ac485bf4e3cfca5d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63ff54561e98d3efcd5b1750f94fd2cb\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63ff54561e98d3efcd5b1750f94fd2cb\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a2017bc9cdabb70d8e393a9345e4ef5\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a2017bc9cdabb70d8e393a9345e4ef5\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\89aa6437d243f5cd5b7a561c35319165\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\89aa6437d243f5cd5b7a561c35319165\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65651b69d61ed87f03dfdca9bb335f22\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65651b69d61ed87f03dfdca9bb335f22\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c192d65f278eab31a1efb9e104bf07\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c192d65f278eab31a1efb9e104bf07\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9a10cc38f566a216281704b9741505a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9a10cc38f566a216281704b9741505a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bfced9a1f1880336970c4371b6094c8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bfced9a1f1880336970c4371b6094c8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eeb88d4865e24a3e04aaf4b173fe3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eeb88d4865e24a3e04aaf4b173fe3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b7a2e6fafa5cbfaa7e710a0b8f28fa8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b7a2e6fafa5cbfaa7e710a0b8f28fa8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7f6f71de4fa66288728fb74fbaf5917\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7f6f71de4fa66288728fb74fbaf5917\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f232168a5e9f503deeee635e2fca0dc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f232168a5e9f503deeee635e2fca0dc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2726f8cee7ffa0d25548bed660b5fd5f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2726f8cee7ffa0d25548bed660b5fd5f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f6d8d3f2d1254a4d375b3d770b5db21\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f6d8d3f2d1254a4d375b3d770b5db21\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\225b6253df36812118298ce0168b0d04\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\225b6253df36812118298ce0168b0d04\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08426a2fcc9d38046334c66a53e22d4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08426a2fcc9d38046334c66a53e22d4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eafecbd1fbd0c1f6979cc8e4ab731ea\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eafecbd1fbd0c1f6979cc8e4ab731ea\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bae2f9282aa3a580021af25788e9ad81\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bae2f9282aa3a580021af25788e9ad81\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\825a99d4c3950fcd4aa6e41167ad52fd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\825a99d4c3950fcd4aa6e41167ad52fd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\026390448bfc7f5c1c1a4c6cc2ec2c65\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\026390448bfc7f5c1c1a4c6cc2ec2c65\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\256335a5ea3f54fdbb1b8b7cbed2de6d\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\256335a5ea3f54fdbb1b8b7cbed2de6d\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4e3591d986761af58212099d154c87b\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4e3591d986761af58212099d154c87b\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74ac3e623d7a1a20f6bcc1c1af7d8c03\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74ac3e623d7a1a20f6bcc1c1af7d8c03\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e8023d571a8e7702052021337eb7ae2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e8023d571a8e7702052021337eb7ae2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acb1c05ec2b5de0eae17642645b54bfd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acb1c05ec2b5de0eae17642645b54bfd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7870523f381be03945c2ae2b350b995a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7870523f381be03945c2ae2b350b995a\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8b1f146db077014c250cba9ce528d25\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8b1f146db077014c250cba9ce528d25\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3d0109f7c2eae6aaca433c0bd7f5d90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3d0109f7c2eae6aaca433c0bd7f5d90\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6941d74c7ab7962996d34176830e32b6\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6941d74c7ab7962996d34176830e32b6\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\740fac41411f6d546ba3df8692a1e93a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\740fac41411f6d546ba3df8692a1e93a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5491606d8f4c007c86eb4b55fa81e2f\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5491606d8f4c007c86eb4b55fa81e2f\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde012d300f5538be0a3fec38c3791e4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde012d300f5538be0a3fec38c3791e4\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c07989a3a5698f824ecd1b7999a9f360\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c07989a3a5698f824ecd1b7999a9f360\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d6c07f743127ddd5360d6954f74ebee\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d6c07f743127ddd5360d6954f74ebee\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b42c3a5b161d604102716c5dee98ca8\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b42c3a5b161d604102716c5dee98ca8\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9e7c23061395ce528ff094266f2790\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9e7c23061395ce528ff094266f2790\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b27360397b7a5229eb5bb3ea788bfa65\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b27360397b7a5229eb5bb3ea788bfa65\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\074bee8d2e1454a1198ca0178bb97d77\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\074bee8d2e1454a1198ca0178bb97d77\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f592b50139c4bd0aed2e0a58de97990\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f592b50139c4bd0aed2e0a58de97990\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\656a488aa119732ce6545f2c2435d004\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\656a488aa119732ce6545f2c2435d004\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45980c1ab77be3ae4f20a05b1525c6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45980c1ab77be3ae4f20a05b1525c6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e138819e4ee3a3de9f18e708b2b48ced\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e138819e4ee3a3de9f18e708b2b48ced\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1ce1b87897ded0f11c618a585bc7a0b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1ce1b87897ded0f11c618a585bc7a0b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bff0567b0c6eeea188861c51cea3a0d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bff0567b0c6eeea188861c51cea3a0d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e88a6a110032ffe3eee7362c86472a9\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e88a6a110032ffe3eee7362c86472a9\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\821feed66bb7b312852f8270860ea527\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\821feed66bb7b312852f8270860ea527\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d58993a7da3d45b5987e8dc6216c641\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d58993a7da3d45b5987e8dc6216c641\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8fdd6f1dbd7ecc4c1ae946e5c4b6026f\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8fdd6f1dbd7ecc4c1ae946e5c4b6026f\transformed\tensorflow-lite-gpu-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca71908c7fdafe3a3122d5723c1c8012\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca71908c7fdafe3a3122d5723c1c8012\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab73eb3100e01862391f610aee1e45b\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab73eb3100e01862391f610aee1e45b\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73bfdc82f56547534f2af440fd99a0c5\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73bfdc82f56547534f2af440fd99a0c5\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\3dscanner\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\06c63438b7cec3f5ab1938d9bc6ca2aa\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
queries
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
MERGED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:20:5-27:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49be6b458c81d88f4d7907b1f775f7b0\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\560b0619dc8844d39afe01e97b454981\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4669808bc60a16dce0c14ecf33122c21\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\420ff15ad4d5c9e39824c1ae744c1de2\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09cbed24211ffa41b7a8be04d3934e8a\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b620139cf4b22e843689e97ca0a17df\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.scanner3d.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae3dccc5aff18b6b52c6a8ac40de27a\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1ef882bc89dbbcdc7c9dc3ed72510b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fe7578705e54e56590414c1a3238ba\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
package#com.google.ar.core
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:21:18-51
package#com.android.vending
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:22:9-55
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:22:18-52
intent#action:name:com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:24:9-26:18
action#com.google.android.play.core.install.BIND_INSTALL_SERVICE
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:25:13-96
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:25:21-93
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:32:9-34:41
	android:value
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:34:13-38
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:33:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:36:9-42:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:39:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:41:13-43
	android:exported
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:38:13-74
	android:theme
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:42:13-77
	android:name
		ADDED from [com.google.ar:core:1.41.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\558d40362b31612b3ec89decd760abb0\transformed\core-1.41.0\AndroidManifest.xml:37:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6cd32a5dbc501756842d7aadc624c52\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
