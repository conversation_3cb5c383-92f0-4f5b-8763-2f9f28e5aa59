{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-60:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "764,874,975,1086,1170,1271,1386,1466,1543,1636,1731,1823,1917,2019,2114,2211,2305,2398,2488,2570,2678,2782,2880,2986,3091,3196,3353,11202", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "869,970,1081,1165,1266,1381,1461,1538,1631,1726,1818,1912,2014,2109,2206,2300,2393,2483,2565,2673,2777,2875,2981,3086,3191,3348,3449,11279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10900,11009", "endColumns": "108,114", "endOffsets": "11004,11119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1046,1136,1205,1265,1356,1419,1483,1542,1609,1671,1726,1849,1907,1968,2023,2095,2232,2313,2395,2525,2599,2673,2805,2891,2968,3019,3073,3139,3210,3287,3368,3447,3520,3594,3664,3738,3839,3925,3999,4088,4180,4254,4327,4416,4467,4547,4614,4697,4781,4843,4907,4970,5039,5133,5234,5327,5425,5480,5538", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "254,329,406,488,581,668,765,894,978,1041,1131,1200,1260,1351,1414,1478,1537,1604,1666,1721,1844,1902,1963,2018,2090,2227,2308,2390,2520,2594,2668,2800,2886,2963,3014,3068,3134,3205,3282,3363,3442,3515,3589,3659,3733,3834,3920,3994,4083,4175,4249,4322,4411,4462,4542,4609,4692,4776,4838,4902,4965,5034,5128,5229,5322,5420,5475,5533,5611"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3454,3529,3606,3688,3781,4592,4689,4818,5102,5165,6498,6567,6627,6718,6781,6845,6904,6971,7033,7088,7211,7269,7330,7385,7457,7594,7675,7757,7887,7961,8035,8167,8253,8330,8381,8435,8501,8572,8649,8730,8809,8882,8956,9026,9100,9201,9287,9361,9450,9542,9616,9689,9778,9829,9909,9976,10059,10143,10205,10269,10332,10401,10495,10596,10689,10787,10842,11124", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "304,3524,3601,3683,3776,3863,4684,4813,4897,5160,5250,6562,6622,6713,6776,6840,6899,6966,7028,7083,7206,7264,7325,7380,7452,7589,7670,7752,7882,7956,8030,8162,8248,8325,8376,8430,8496,8567,8644,8725,8804,8877,8951,9021,9095,9196,9282,9356,9445,9537,9611,9684,9773,9824,9904,9971,10054,10138,10200,10264,10327,10396,10490,10591,10684,10782,10837,10895,11197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3868,3967,4069,4168,4268,4369,4475,11284", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3962,4064,4163,4263,4364,4470,4587,11380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,255,372,500,623,773,890,1014,1111,1245,1383", "endColumns": "111,87,116,127,122,149,116,123,96,133,137,114", "endOffsets": "162,250,367,495,618,768,885,1009,1106,1240,1378,1493"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4902,5014,5255,5372,5500,5623,5773,5890,6014,6111,6245,6383", "endColumns": "111,87,116,127,122,149,116,123,96,133,137,114", "endOffsets": "5009,5097,5367,5495,5618,5768,5885,6009,6106,6240,6378,6493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,234,282,405,531", "endColumns": "43,47,122,125,93", "endOffsets": "233,281,404,530,624"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "309,357,409,536,666", "endColumns": "47,51,126,129,97", "endOffsets": "352,404,531,661,759"}}]}]}