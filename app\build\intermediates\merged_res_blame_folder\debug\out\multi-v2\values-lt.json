{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "76,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "7711,8371,8476,8590", "endColumns": "104,104,113,105", "endOffsets": "7811,8471,8585,8691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1390", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1385,1507"}, "to": {"startLines": "55,56,77,79,80,97,98,157,158,159,160,162,163,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5156,5249,7816,8006,8111,10096,10173,15052,15139,15223,15293,15444,15530,15702,15878,15961", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,82,121", "endOffsets": "5244,5328,7909,8106,8201,10168,10259,15134,15218,15288,15357,15525,15613,15772,15956,16078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "14823,14933", "endColumns": "109,118", "endOffsets": "14928,15047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,3030,3127,3204,3260,3314,3380,3455,3534,3622,3702,3778,3856,3929,4006,4113,4200,4281,4371,4463,4535,4616,4708,4763,4845,4911,4996,5083,5145,5209,5272,5344,5455,5571,5672,5781,5841,5899", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,3025,3122,3199,3255,3309,3375,3450,3529,3617,3697,3773,3851,3924,4001,4108,4195,4276,4366,4458,4530,4611,4703,4758,4840,4906,4991,5078,5140,5204,5267,5339,5450,5566,5667,5776,5836,5894,5976"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3683,3762,3840,3923,4017,4858,4954,5072,8206,8272,10018,10264,10329,10439,10502,10574,10633,10707,10768,10822,10946,11007,11069,11123,11201,11335,11423,11507,11648,11727,11811,11954,12051,12128,12184,12238,12304,12379,12458,12546,12626,12702,12780,12853,12930,13037,13124,13205,13295,13387,13459,13540,13632,13687,13769,13835,13920,14007,14069,14133,14196,14268,14379,14495,14596,14705,14765,15362", "endLines": "7,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "420,3757,3835,3918,4012,4102,4949,5067,5151,8267,8366,10091,10324,10434,10497,10569,10628,10702,10763,10817,10941,11002,11064,11118,11196,11330,11418,11502,11643,11722,11806,11949,12046,12123,12179,12233,12299,12374,12453,12541,12621,12697,12775,12848,12925,13032,13119,13200,13290,13382,13454,13535,13627,13682,13764,13830,13915,14002,14064,14128,14191,14263,14374,14490,14591,14700,14760,14818,15439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,287,425,567", "endColumns": "48,47,137,141,91", "endOffsets": "238,286,424,566,658"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "425,478,530,672,818", "endColumns": "52,51,141,145,95", "endOffsets": "473,525,667,813,909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1030,1134,1247,1334,1436,1558,1641,1721,1815,1911,2008,2104,2207,2303,2401,2497,2591,2685,2768,2877,2985,3085,3195,3300,3406,3582,15618", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1025,1129,1242,1329,1431,1553,1636,1716,1810,1906,2003,2099,2202,2298,2396,2492,2586,2680,2763,2872,2980,3080,3190,3295,3401,3577,3678,15697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5333,5440,5592,5724,5831,5984,6114,6233,6498,6664,6773,6938,7072,7225,7375,7443,7509", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "5435,5587,5719,5826,5979,6109,6228,6334,6659,6768,6933,7067,7220,7370,7438,7504,7594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6339", "endColumns": "158", "endOffsets": "6493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "75,78,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7599,7914,8696,8822,8963,9102,9232,9364,9501,9598,9753,9896", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "7706,8001,8817,8958,9097,9227,9359,9496,9593,9748,9891,10013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "45,46,47,48,49,50,51,166", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4107,4205,4315,4414,4517,4628,4738,15777", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4200,4310,4409,4512,4623,4733,4853,15873"}}]}]}