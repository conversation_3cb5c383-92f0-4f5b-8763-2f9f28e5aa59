package com.scanner3d.app.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.scanner3d.app.data.model.ScanEntity;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ScanDao_Impl implements ScanDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ScanEntity> __insertionAdapterOfScanEntity;

  private final EntityDeletionOrUpdateAdapter<ScanEntity> __deletionAdapterOfScanEntity;

  private final EntityDeletionOrUpdateAdapter<ScanEntity> __updateAdapterOfScanEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteScanById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllScans;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldScans;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUploadStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateScanMetadata;

  private final SharedSQLiteStatement __preparedStmtOfUpdateScanTags;

  public ScanDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfScanEntity = new EntityInsertionAdapter<ScanEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `scans` (`id`,`name`,`description`,`createdAt`,`modifiedAt`,`scanDuration`,`filePath`,`thumbnailPath`,`fileSize`,`format`,`quality`,`vertexCount`,`triangleCount`,`hasTexture`,`hasColors`,`isUploaded`,`cloudUrl`,`tags`,`boundingBoxMinX`,`boundingBoxMinY`,`boundingBoxMinZ`,`boundingBoxMaxX`,`boundingBoxMaxY`,`boundingBoxMaxZ`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ScanEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindLong(4, entity.getCreatedAt());
        statement.bindLong(5, entity.getModifiedAt());
        statement.bindLong(6, entity.getScanDuration());
        statement.bindString(7, entity.getFilePath());
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getThumbnailPath());
        }
        statement.bindLong(9, entity.getFileSize());
        statement.bindString(10, entity.getFormat());
        statement.bindString(11, entity.getQuality());
        statement.bindLong(12, entity.getVertexCount());
        statement.bindLong(13, entity.getTriangleCount());
        final int _tmp = entity.getHasTexture() ? 1 : 0;
        statement.bindLong(14, _tmp);
        final int _tmp_1 = entity.getHasColors() ? 1 : 0;
        statement.bindLong(15, _tmp_1);
        final int _tmp_2 = entity.isUploaded() ? 1 : 0;
        statement.bindLong(16, _tmp_2);
        if (entity.getCloudUrl() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getCloudUrl());
        }
        if (entity.getTags() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getTags());
        }
        statement.bindDouble(19, entity.getBoundingBoxMinX());
        statement.bindDouble(20, entity.getBoundingBoxMinY());
        statement.bindDouble(21, entity.getBoundingBoxMinZ());
        statement.bindDouble(22, entity.getBoundingBoxMaxX());
        statement.bindDouble(23, entity.getBoundingBoxMaxY());
        statement.bindDouble(24, entity.getBoundingBoxMaxZ());
      }
    };
    this.__deletionAdapterOfScanEntity = new EntityDeletionOrUpdateAdapter<ScanEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `scans` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ScanEntity entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfScanEntity = new EntityDeletionOrUpdateAdapter<ScanEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `scans` SET `id` = ?,`name` = ?,`description` = ?,`createdAt` = ?,`modifiedAt` = ?,`scanDuration` = ?,`filePath` = ?,`thumbnailPath` = ?,`fileSize` = ?,`format` = ?,`quality` = ?,`vertexCount` = ?,`triangleCount` = ?,`hasTexture` = ?,`hasColors` = ?,`isUploaded` = ?,`cloudUrl` = ?,`tags` = ?,`boundingBoxMinX` = ?,`boundingBoxMinY` = ?,`boundingBoxMinZ` = ?,`boundingBoxMaxX` = ?,`boundingBoxMaxY` = ?,`boundingBoxMaxZ` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ScanEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindLong(4, entity.getCreatedAt());
        statement.bindLong(5, entity.getModifiedAt());
        statement.bindLong(6, entity.getScanDuration());
        statement.bindString(7, entity.getFilePath());
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getThumbnailPath());
        }
        statement.bindLong(9, entity.getFileSize());
        statement.bindString(10, entity.getFormat());
        statement.bindString(11, entity.getQuality());
        statement.bindLong(12, entity.getVertexCount());
        statement.bindLong(13, entity.getTriangleCount());
        final int _tmp = entity.getHasTexture() ? 1 : 0;
        statement.bindLong(14, _tmp);
        final int _tmp_1 = entity.getHasColors() ? 1 : 0;
        statement.bindLong(15, _tmp_1);
        final int _tmp_2 = entity.isUploaded() ? 1 : 0;
        statement.bindLong(16, _tmp_2);
        if (entity.getCloudUrl() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getCloudUrl());
        }
        if (entity.getTags() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getTags());
        }
        statement.bindDouble(19, entity.getBoundingBoxMinX());
        statement.bindDouble(20, entity.getBoundingBoxMinY());
        statement.bindDouble(21, entity.getBoundingBoxMinZ());
        statement.bindDouble(22, entity.getBoundingBoxMaxX());
        statement.bindDouble(23, entity.getBoundingBoxMaxY());
        statement.bindDouble(24, entity.getBoundingBoxMaxZ());
        statement.bindString(25, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteScanById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scans WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllScans = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scans";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldScans = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scans WHERE createdAt < ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUploadStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE scans SET isUploaded = ?, cloudUrl = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateScanMetadata = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE scans SET name = ?, description = ?, modifiedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateScanTags = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE scans SET tags = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertScan(final ScanEntity scan, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfScanEntity.insert(scan);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertScans(final List<ScanEntity> scans,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfScanEntity.insert(scans);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteScan(final ScanEntity scan, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfScanEntity.handle(scan);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateScan(final ScanEntity scan, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfScanEntity.handle(scan);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteScanById(final String scanId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteScanById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, scanId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteScanById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllScans(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllScans.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllScans.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldScans(final long cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldScans.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldScans.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUploadStatus(final String scanId, final boolean isUploaded,
      final String cloudUrl, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUploadStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isUploaded ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        if (cloudUrl == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, cloudUrl);
        }
        _argIndex = 3;
        _stmt.bindString(_argIndex, scanId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUploadStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateScanMetadata(final String scanId, final String name, final String description,
      final long modifiedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateScanMetadata.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, name);
        _argIndex = 2;
        if (description == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, description);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, modifiedAt);
        _argIndex = 4;
        _stmt.bindString(_argIndex, scanId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateScanMetadata.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateScanTags(final String scanId, final String tags,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateScanTags.acquire();
        int _argIndex = 1;
        if (tags == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, tags);
        }
        _argIndex = 2;
        _stmt.bindString(_argIndex, scanId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateScanTags.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ScanEntity>> getAllScans() {
    final String _sql = "SELECT * FROM scans ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllScansList(final Continuation<? super List<ScanEntity>> $completion) {
    final String _sql = "SELECT * FROM scans ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<ScanEntity>> getAllScansLiveData() {
    final String _sql = "SELECT * FROM scans ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"scans"}, false, new Callable<List<ScanEntity>>() {
      @Override
      @Nullable
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getScanById(final String scanId,
      final Continuation<? super ScanEntity> $completion) {
    final String _sql = "SELECT * FROM scans WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, scanId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ScanEntity>() {
      @Override
      @Nullable
      public ScanEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final ScanEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _result = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<ScanEntity> getScanByIdLiveData(final String scanId) {
    final String _sql = "SELECT * FROM scans WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, scanId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"scans"}, false, new Callable<ScanEntity>() {
      @Override
      @Nullable
      public ScanEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final ScanEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _result = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> searchScans(final String searchQuery) {
    final String _sql = "SELECT * FROM scans WHERE name LIKE ? OR description LIKE ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchQuery);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getScansByQuality(final String quality) {
    final String _sql = "SELECT * FROM scans WHERE quality = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, quality);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getScansByFormat(final String format) {
    final String _sql = "SELECT * FROM scans WHERE format = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, format);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getScansByTextureStatus(final boolean hasTexture) {
    final String _sql = "SELECT * FROM scans WHERE hasTexture = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final int _tmp = hasTexture ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp_1 != 0;
            final boolean _tmpHasColors;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_2 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_3 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getScansByUploadStatus(final boolean isUploaded) {
    final String _sql = "SELECT * FROM scans WHERE isUploaded = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final int _tmp = isUploaded ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp_1 != 0;
            final boolean _tmpHasColors;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_2 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_3 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getScansByDateRange(final long startDate, final long endDate) {
    final String _sql = "SELECT * FROM scans WHERE createdAt BETWEEN ? AND ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getScanCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM scans";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalFileSize(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(fileSize) FROM scans";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageScanDuration(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(scanDuration) FROM scans";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @NonNull
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final double _tmp;
            _tmp = _cursor.getDouble(0);
            _result = _tmp;
          } else {
            _result = 0.0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ScanEntity>> getLargestScans(final int limit) {
    final String _sql = "SELECT * FROM scans ORDER BY fileSize DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ScanEntity>> getRecentScans(final int limit) {
    final String _sql = "SELECT * FROM scans ORDER BY createdAt DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scans"}, new Callable<List<ScanEntity>>() {
      @Override
      @NonNull
      public List<ScanEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfModifiedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedAt");
          final int _cursorIndexOfScanDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "scanDuration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfFormat = CursorUtil.getColumnIndexOrThrow(_cursor, "format");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVertexCount = CursorUtil.getColumnIndexOrThrow(_cursor, "vertexCount");
          final int _cursorIndexOfTriangleCount = CursorUtil.getColumnIndexOrThrow(_cursor, "triangleCount");
          final int _cursorIndexOfHasTexture = CursorUtil.getColumnIndexOrThrow(_cursor, "hasTexture");
          final int _cursorIndexOfHasColors = CursorUtil.getColumnIndexOrThrow(_cursor, "hasColors");
          final int _cursorIndexOfIsUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isUploaded");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudUrl");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfBoundingBoxMinX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinX");
          final int _cursorIndexOfBoundingBoxMinY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinY");
          final int _cursorIndexOfBoundingBoxMinZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMinZ");
          final int _cursorIndexOfBoundingBoxMaxX = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxX");
          final int _cursorIndexOfBoundingBoxMaxY = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxY");
          final int _cursorIndexOfBoundingBoxMaxZ = CursorUtil.getColumnIndexOrThrow(_cursor, "boundingBoxMaxZ");
          final List<ScanEntity> _result = new ArrayList<ScanEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScanEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpModifiedAt;
            _tmpModifiedAt = _cursor.getLong(_cursorIndexOfModifiedAt);
            final long _tmpScanDuration;
            _tmpScanDuration = _cursor.getLong(_cursorIndexOfScanDuration);
            final String _tmpFilePath;
            _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final String _tmpFormat;
            _tmpFormat = _cursor.getString(_cursorIndexOfFormat);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final int _tmpVertexCount;
            _tmpVertexCount = _cursor.getInt(_cursorIndexOfVertexCount);
            final int _tmpTriangleCount;
            _tmpTriangleCount = _cursor.getInt(_cursorIndexOfTriangleCount);
            final boolean _tmpHasTexture;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasTexture);
            _tmpHasTexture = _tmp != 0;
            final boolean _tmpHasColors;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfHasColors);
            _tmpHasColors = _tmp_1 != 0;
            final boolean _tmpIsUploaded;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsUploaded);
            _tmpIsUploaded = _tmp_2 != 0;
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final float _tmpBoundingBoxMinX;
            _tmpBoundingBoxMinX = _cursor.getFloat(_cursorIndexOfBoundingBoxMinX);
            final float _tmpBoundingBoxMinY;
            _tmpBoundingBoxMinY = _cursor.getFloat(_cursorIndexOfBoundingBoxMinY);
            final float _tmpBoundingBoxMinZ;
            _tmpBoundingBoxMinZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMinZ);
            final float _tmpBoundingBoxMaxX;
            _tmpBoundingBoxMaxX = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxX);
            final float _tmpBoundingBoxMaxY;
            _tmpBoundingBoxMaxY = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxY);
            final float _tmpBoundingBoxMaxZ;
            _tmpBoundingBoxMaxZ = _cursor.getFloat(_cursorIndexOfBoundingBoxMaxZ);
            _item = new ScanEntity(_tmpId,_tmpName,_tmpDescription,_tmpCreatedAt,_tmpModifiedAt,_tmpScanDuration,_tmpFilePath,_tmpThumbnailPath,_tmpFileSize,_tmpFormat,_tmpQuality,_tmpVertexCount,_tmpTriangleCount,_tmpHasTexture,_tmpHasColors,_tmpIsUploaded,_tmpCloudUrl,_tmpTags,_tmpBoundingBoxMinX,_tmpBoundingBoxMinY,_tmpBoundingBoxMinZ,_tmpBoundingBoxMaxX,_tmpBoundingBoxMaxY,_tmpBoundingBoxMaxZ);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
