package com.scanner3d.app.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.Image
import android.util.Log
// import com.google.ar.core.Pose  // ARCore disabled
import com.scanner3d.app.core.ScanningEngine
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.nio.ByteBuffer
import kotlin.math.*

class TextureMapper {
    
    companion object {
        private const val TAG = "TextureMapper"
        private const val TEXTURE_SIZE = 1024
        private const val UV_SCALE = 1.0f
    }
    
    suspend fun applyTextures(
        mesh: Mesh3D,
        capturedFrames: List<ScanningEngine.CapturedFrame>
    ): Mesh3D = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Starting texture mapping with ${capturedFrames.size} frames")
        
        try {
            // Generate UV coordinates for the mesh
            val uvCoordinates = generateUVCoordinates(mesh)
            
            // Create texture atlas from captured frames
            val textureAtlas = createTextureAtlas(mesh, capturedFrames, uvCoordinates)
            
            // Update mesh metadata
            val updatedMetadata = mesh.metadata.copy(
                hasTexture = true,
                estimatedFileSize = mesh.metadata.estimatedFileSize + estimateTextureSize(textureAtlas)
            )
            
            mesh.copy(
                textureCoordinates = uvCoordinates,
                metadata = updatedMetadata
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying textures", e)
            throw e
        }
    }
    
    private fun generateUVCoordinates(mesh: Mesh3D): FloatArray {
        val vertexCount = mesh.vertexCount
        val uvCoordinates = FloatArray(vertexCount * 2) // 2 coordinates per vertex (u, v)
        
        // Simple planar projection for UV mapping
        // In a more sophisticated implementation, you would use:
        // - Conformal mapping
        // - Angle-based flattening
        // - Seam detection and unwrapping
        
        val boundingBox = mesh.boundingBox
        val width = boundingBox.width
        val height = boundingBox.height
        
        for (i in 0 until vertexCount) {
            val vertexIndex = i * 3
            val x = mesh.vertices[vertexIndex]
            val y = mesh.vertices[vertexIndex + 1]
            val z = mesh.vertices[vertexIndex + 2]
            
            // Project to UV space using bounding box normalization
            val u = ((x - boundingBox.minX) / width).coerceIn(0f, 1f)
            val v = ((y - boundingBox.minY) / height).coerceIn(0f, 1f)
            
            uvCoordinates[i * 2] = u * UV_SCALE
            uvCoordinates[i * 2 + 1] = v * UV_SCALE
        }
        
        Log.d(TAG, "Generated UV coordinates for $vertexCount vertices")
        return uvCoordinates
    }
    
    private fun createTextureAtlas(
        mesh: Mesh3D,
        capturedFrames: List<ScanningEngine.CapturedFrame>,
        uvCoordinates: FloatArray
    ): Bitmap {
        
        // Create texture atlas bitmap
        val atlas = Bitmap.createBitmap(TEXTURE_SIZE, TEXTURE_SIZE, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(atlas)
        
        // Initialize with a neutral color
        canvas.drawColor(android.graphics.Color.GRAY)
        
        // Process each captured frame
        capturedFrames.forEach { frame ->
            try {
                val bitmap = convertImageToBitmap(frame.colorImage)
                if (bitmap != null) {
                    projectFrameToAtlas(bitmap, frame.poseData, mesh, uvCoordinates, canvas)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to process frame for texture mapping", e)
            }
        }
        
        Log.d(TAG, "Created texture atlas of size ${TEXTURE_SIZE}x${TEXTURE_SIZE}")
        return atlas
    }
    
    private fun convertImageToBitmap(image: Image): Bitmap? {
        return try {
            val buffer: ByteBuffer = image.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)
            BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to convert Image to Bitmap", e)
            null
        }
    }
    
    private fun projectFrameToAtlas(
        frameBitmap: Bitmap,
        poseData: FloatArray?, // Simplified pose data instead of ARCore Pose
        mesh: Mesh3D,
        uvCoordinates: FloatArray,
        atlasCanvas: android.graphics.Canvas
    ) {
        if (poseData == null) return

        // Simplified texture mapping without OpenCV
        // Using basic projection and sampling

        val paint = android.graphics.Paint().apply {
            isAntiAlias = true
            alpha = 128 // Semi-transparent for blending
        }

        // Simple texture mapping - sample colors from frame center
        // This is a placeholder implementation without complex 3D projection
        val centerX = frameBitmap.width / 2
        val centerY = frameBitmap.height / 2
        val centerColor = frameBitmap.getPixel(centerX, centerY)

        // Apply color to random UV coordinates as a basic texture
        val vertexCount = mesh.vertexCount
        for (i in 0 until vertexCount step 10) { // Sample every 10th vertex
            if (i * 2 + 1 < uvCoordinates.size) {
                val u = uvCoordinates[i * 2]
                val v = uvCoordinates[i * 2 + 1]

                val atlasX = (u * TEXTURE_SIZE).toInt().coerceIn(0, TEXTURE_SIZE - 1)
                val atlasY = (v * TEXTURE_SIZE).toInt().coerceIn(0, TEXTURE_SIZE - 1)

                paint.color = centerColor
                atlasCanvas.drawCircle(atlasX.toFloat(), atlasY.toFloat(), 3f, paint)
            }
        }
    }
    
    // OpenCV-dependent function removed - using simplified texture mapping
    // This function was removed because it depends on OpenCV Mat class
    // Texture mapping is now handled with simplified algorithms without complex 3D transformations
    
    private fun estimateTextureSize(textureAtlas: Bitmap): Long {
        // Estimate compressed texture size (assuming JPEG compression)
        val uncompressedSize = textureAtlas.width * textureAtlas.height * 4L // ARGB
        return uncompressedSize / 10 // Rough compression ratio
    }
    
    fun generateTextureCoordinatesForTriangle(
        v1: FloatArray,
        v2: FloatArray,
        v3: FloatArray,
        boundingBox: Mesh3D.BoundingBox
    ): FloatArray {
        // Generate UV coordinates for a single triangle
        val uvCoords = FloatArray(6) // 2 coordinates per vertex
        
        for (i in 0..2) {
            val vertex = when (i) {
                0 -> v1
                1 -> v2
                else -> v3
            }
            
            val u = ((vertex[0] - boundingBox.minX) / boundingBox.width).coerceIn(0f, 1f)
            val v = ((vertex[1] - boundingBox.minY) / boundingBox.height).coerceIn(0f, 1f)
            
            uvCoords[i * 2] = u
            uvCoords[i * 2 + 1] = v
        }
        
        return uvCoords
    }
    
    fun blendTextures(texture1: Bitmap, texture2: Bitmap, alpha: Float): Bitmap {
        val result = Bitmap.createBitmap(texture1.width, texture1.height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(result)
        
        val paint = android.graphics.Paint().apply {
            isAntiAlias = true
        }
        
        // Draw first texture
        canvas.drawBitmap(texture1, 0f, 0f, paint)
        
        // Draw second texture with alpha blending
        paint.alpha = (alpha * 255).toInt()
        canvas.drawBitmap(texture2, 0f, 0f, paint)
        
        return result
    }
}

