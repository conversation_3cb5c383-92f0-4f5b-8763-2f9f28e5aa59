<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_scan_list" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\item_scan_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_scan_list_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="203" endOffset="51"/></Target><Target id="@+id/iv_thumbnail" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="26" endOffset="59"/></Target><Target id="@+id/tv_scan_name" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="43" endOffset="43"/></Target><Target id="@+id/tv_quality" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="61" endOffset="31"/></Target><Target id="@+id/tv_scan_description" view="TextView"><Expressions/><location startLine="64" startOffset="8" endLine="78" endOffset="68"/></Target><Target id="@+id/ll_stats" view="LinearLayout"><Expressions/><location startLine="81" startOffset="8" endLine="110" endOffset="22"/></Target><Target id="@+id/tv_vertex_count" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="99" endOffset="45"/></Target><Target id="@+id/tv_triangle_count" view="TextView"><Expressions/><location startLine="101" startOffset="12" endLine="108" endOffset="46"/></Target><Target id="@+id/ll_info" view="LinearLayout"><Expressions/><location startLine="113" startOffset="8" endLine="150" endOffset="22"/></Target><Target id="@+id/tv_scan_date" view="TextView"><Expressions/><location startLine="124" startOffset="12" endLine="131" endOffset="43"/></Target><Target id="@+id/tv_scan_duration" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="140" endOffset="37"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="142" startOffset="12" endLine="148" endOffset="37"/></Target><Target id="@+id/chip_texture" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="163" startOffset="12" endLine="173" endOffset="44"/></Target><Target id="@+id/chip_colors" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="175" startOffset="12" endLine="185" endOffset="44"/></Target><Target id="@+id/chip_uploaded" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="187" startOffset="12" endLine="197" endOffset="44"/></Target></Targets></Layout>