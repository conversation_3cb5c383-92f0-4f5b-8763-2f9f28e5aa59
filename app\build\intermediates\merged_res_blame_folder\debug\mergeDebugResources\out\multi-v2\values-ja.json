{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-68:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc343c3e9bd9f0c6efa95dea879d2fd0\\transformed\\navigation-ui-2.7.5\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,109", "endOffsets": "149,259"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10032,10131", "endColumns": "98,109", "endOffsets": "10126,10236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f5f3850a7c512d8f41852114da6284cb\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "692,789,882,987,1069,1167,1275,1353,1428,1519,1612,1707,1801,1901,1994,2089,2183,2274,2365,2443,2545,2643,2738,2841,2937,3033,3181,10314", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "784,877,982,1064,1162,1270,1348,1423,1514,1607,1702,1796,1896,1989,2084,2178,2269,2360,2438,2540,2638,2733,2836,2932,3028,3176,3273,10388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8587d926d346ccf2176f046e22a2a17\\transformed\\core-1.41.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,281,378,478", "endColumns": "45,44,96,99,80", "endOffsets": "235,280,377,477,558"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "303,353,402,503,607", "endColumns": "49,48,100,103,84", "endOffsets": "348,397,498,602,687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fae1aef513bb18ebfc860e4ba437b55\\transformed\\biometric-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,157,242,345,458,572,691,797,913,1009,1128,1247", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "152,237,340,453,567,686,792,908,1004,1123,1242,1350"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4575,4677,4909,5012,5125,5239,5358,5464,5580,5676,5795,5914", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "4672,4757,5007,5120,5234,5353,5459,5575,5671,5790,5909,6017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\70befc9c16dd9e301c153021ec73a9fd\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3641,3733,3833,3927,4023,4116,4209,10393", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3728,3828,3922,4018,4111,4204,4305,10489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c351a29c398c623fa23a89619fe48\\transformed\\material-1.11.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2511,2590,2665,2718,2769,2835,2905,2975,3052,3122,3186,3257,3325,3388,3479,3558,3621,3701,3783,3855,3926,3998,4046,4118,4182,4257,4334,4396,4460,4523,4590,4676,4762,4843,4926,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2506,2585,2660,2713,2764,2830,2900,2970,3047,3117,3181,3252,3320,3383,3474,3553,3616,3696,3778,3850,3921,3993,4041,4113,4177,4252,4329,4391,4455,4518,4585,4671,4757,4838,4921,4978,5033,5106"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3278,3345,3409,3478,3559,4310,4395,4499,4762,4825,6022,6086,6144,6225,6286,6350,6405,6464,6521,6575,6668,6724,6781,6835,6901,7001,7077,7158,7280,7342,7404,7505,7584,7659,7712,7763,7829,7899,7969,8046,8116,8180,8251,8319,8382,8473,8552,8615,8695,8777,8849,8920,8992,9040,9112,9176,9251,9328,9390,9454,9517,9584,9670,9756,9837,9920,9977,10241", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "298,3340,3404,3473,3554,3636,4390,4494,4570,4820,4904,6081,6139,6220,6281,6345,6400,6459,6516,6570,6663,6719,6776,6830,6896,6996,7072,7153,7275,7337,7399,7500,7579,7654,7707,7758,7824,7894,7964,8041,8111,8175,8246,8314,8377,8468,8547,8610,8690,8772,8844,8915,8987,9035,9107,9171,9246,9323,9385,9449,9512,9579,9665,9751,9832,9915,9972,10027,10309"}}]}]}