package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.utils.FileManager
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

class GalleryViewModel(application: Application) : AndroidViewModel(application) {
    
    private val database = Scanner3DDatabase.getDatabase(application)
    private val scanDao = database.scanDao()
    private val fileManager = FileManager(application)
    
    private val _scans = MutableLiveData<List<ScanEntity>>()
    val scans: LiveData<List<ScanEntity>> = _scans
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _scanStats = MutableLiveData<ScanStats>()
    val scanStats: LiveData<ScanStats> = _scanStats
    
    private var currentSortBy = SortBy.DATE_CREATED
    private var currentFilterBy = FilterBy.ALL
    
    data class ScanStats(
        val totalScans: Int,
        val totalSize: Long,
        val averageScanTime: Long
    )
    
    enum class SortBy {
        DATE_CREATED, NAME, SIZE, QUALITY
    }
    
    enum class FilterBy {
        ALL, HIGH_QUALITY, WITH_TEXTURE, RECENT
    }
    
    fun loadScans() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                scanDao.getAllScans().collect { scanList ->
                    val filteredAndSorted = applyFilterAndSort(scanList)
                    _scans.value = filteredAndSorted
                    updateStats(scanList)
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load scans: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun refreshScans() {
        loadScans()
    }
    
    fun searchScans(query: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val searchQuery = "%$query%"
                scanDao.searchScans(searchQuery).collect { scanList ->
                    val filteredAndSorted = applyFilterAndSort(scanList)
                    _scans.value = filteredAndSorted
                }
            } catch (e: Exception) {
                _errorMessage.value = "Search failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteScan(scanId: String) {
        viewModelScope.launch {
            try {
                // Get scan info before deletion
                val scan = scanDao.getScanById(scanId)
                scan?.let {
                    // Delete the actual files
                    fileManager.deleteScanFiles(it)
                    // Delete from database
                    scanDao.deleteScanById(scanId)
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete scan: ${e.message}"
            }
        }
    }
    
    fun renameScan(scanId: String, newName: String) {
        viewModelScope.launch {
            try {
                scanDao.updateScanMetadata(
                    scanId = scanId,
                    name = newName,
                    description = null,
                    modifiedAt = System.currentTimeMillis()
                )
            } catch (e: Exception) {
                _errorMessage.value = "Failed to rename scan: ${e.message}"
            }
        }
    }
    
    fun setSortBy(sortBy: SortBy) {
        currentSortBy = sortBy
        _scans.value?.let { scanList ->
            _scans.value = applyFilterAndSort(scanList)
        }
    }
    
    fun setFilterBy(filterBy: FilterBy) {
        currentFilterBy = filterBy
        _scans.value?.let { scanList ->
            _scans.value = applyFilterAndSort(scanList)
        }
    }
    
    private fun applyFilterAndSort(scans: List<ScanEntity>): List<ScanEntity> {
        // Apply filter
        val filtered = when (currentFilterBy) {
            FilterBy.ALL -> scans
            FilterBy.HIGH_QUALITY -> scans.filter { it.quality == "HIGH" || it.quality == "ULTRA" }
            FilterBy.WITH_TEXTURE -> scans.filter { it.hasTexture }
            FilterBy.RECENT -> {
                val oneWeekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
                scans.filter { it.createdAt > oneWeekAgo }
            }
        }
        
        // Apply sort
        return when (currentSortBy) {
            SortBy.DATE_CREATED -> filtered.sortedByDescending { it.createdAt }
            SortBy.NAME -> filtered.sortedBy { it.name.lowercase() }
            SortBy.SIZE -> filtered.sortedByDescending { it.fileSize }
            SortBy.QUALITY -> filtered.sortedBy { 
                when (it.quality) {
                    "LOW" -> 0
                    "MEDIUM" -> 1
                    "HIGH" -> 2
                    "ULTRA" -> 3
                    else -> 0
                }
            }
        }
    }
    
    private fun updateStats(scans: List<ScanEntity>) {
        val totalScans = scans.size
        val totalSize = scans.sumOf { it.fileSize }
        val averageScanTime = if (scans.isNotEmpty()) {
            scans.map { it.scanDuration }.average().toLong()
        } else {
            0L
        }
        
        _scanStats.value = ScanStats(totalScans, totalSize, averageScanTime)
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
}
