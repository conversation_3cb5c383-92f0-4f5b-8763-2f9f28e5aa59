package com.scanner3d.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.data.model.AppSettings

@Database(
    entities = [ScanEntity::class, AppSettings::class],
    version = 1,
    exportSchema = false
)
abstract class Scanner3DDatabase : RoomDatabase() {
    
    abstract fun scanDao(): ScanDao
    abstract fun settingsDao(): SettingsDao
    
    companion object {
        @Volatile
        private var INSTANCE: Scanner3DDatabase? = null
        
        private const val DATABASE_NAME = "scanner3d_database"
        
        fun getDatabase(context: Context): Scanner3DDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    Scanner3DDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // Initialize default settings
                db.execSQL("""
                    INSERT INTO settings (
                        id, defaultScanQuality, autoSaveScans, maxScanDuration, frameRate,
                        enableDepthFiltering, enableMeshSmoothing, defaultExportFormat,
                        enableCompression, compressionLevel, enableCloudSync, autoUpload,
                        wifiOnlyUpload, cloudStorageLimit, theme, language, showTutorial,
                        enableHapticFeedback, enableSoundEffects, maxMemoryUsage, cacheSize,
                        enableGpuAcceleration, renderQuality, enableAnalytics, enableCrashReporting,
                        dataRetentionDays, debugMode, enableExperimentalFeatures, logLevel,
                        lastModified
                    ) VALUES (
                        1, 'HIGH', 1, 300000, 30, 1, 1, 'OBJ', 1, 5, 0, 0, 1, 1073741824,
                        'SYSTEM', 'en', 1, 1, 1, 2147483648, 524288000, 1, 'HIGH', 1, 1, 30,
                        0, 0, 'INFO', ${System.currentTimeMillis()}
                    )
                """.trimIndent())
            }
        }
        
        // Migration examples for future versions
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example migration - add new column
                // database.execSQL("ALTER TABLE scans ADD COLUMN newColumn TEXT")
            }
        }
        
        fun destroyInstance() {
            INSTANCE = null
        }
    }
}
