{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-68:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c351a29c398c623fa23a89619fe48\\transformed\\material-1.11.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3521,3598,3678,3761,3855,4678,4773,4900,5188,5252,6675,6745,6812,6921,6984,7051,7110,7184,7247,7301,7416,7474,7536,7590,7665,7794,7884,7973,8114,8196,8278,8417,8503,8587,8647,8698,8764,8837,8915,9001,9082,9154,9231,9306,9377,9478,9572,9651,9747,9841,9915,9991,10077,10130,10217,10283,10368,10459,10521,10585,10648,10717,10819,10920,11016,11117,11181,11459", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "328,3593,3673,3756,3850,3937,4768,4895,4979,5247,5350,6740,6807,6916,6979,7046,7105,7179,7242,7296,7411,7469,7531,7585,7660,7789,7879,7968,8109,8191,8273,8412,8498,8582,8642,8693,8759,8832,8910,8996,9077,9149,9226,9301,9372,9473,9567,9646,9742,9836,9910,9986,10072,10125,10212,10278,10363,10454,10516,10580,10643,10712,10814,10915,11011,11112,11176,11231,11537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f5f3850a7c512d8f41852114da6284cb\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,955,1050,1150,1232,1332,1449,1534,1612,1703,1796,1891,1985,2079,2172,2267,2362,2453,2545,2629,2739,2845,2945,3053,3159,3261,3422,11542", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "950,1045,1145,1227,1327,1444,1529,1607,1698,1791,1886,1980,2074,2167,2262,2357,2448,2540,2624,2734,2840,2940,3048,3154,3256,3417,3516,11621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc343c3e9bd9f0c6efa95dea879d2fd0\\transformed\\navigation-ui-2.7.5\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,121", "endOffsets": "151,273"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11236,11337", "endColumns": "100,121", "endOffsets": "11332,11454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8587d926d346ccf2176f046e22a2a17\\transformed\\core-1.41.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,243,297,443,596", "endColumns": "52,53,145,152,90", "endOffsets": "242,296,442,595,686"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "333,390,448,598,755", "endColumns": "56,57,149,156,94", "endOffsets": "385,443,593,750,845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\70befc9c16dd9e301c153021ec73a9fd\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3942,4044,4146,4247,4347,4455,4559,11626", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "4039,4141,4242,4342,4450,4554,4673,11722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fae1aef513bb18ebfc860e4ba437b55\\transformed\\biometric-1.1.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4984,5098,5355,5475,5606,5734,5881,6013,6158,6255,6394,6534", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "5093,5183,5470,5601,5729,5876,6008,6153,6250,6389,6529,6670"}}]}]}