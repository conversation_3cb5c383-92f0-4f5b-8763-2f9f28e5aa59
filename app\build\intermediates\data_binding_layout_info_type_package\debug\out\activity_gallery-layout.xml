<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_gallery" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\activity_gallery.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_gallery_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="142" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="66"/></Target><Target id="@+id/search_view" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="45"/></Target><Target id="@+id/tv_scan_count" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="41"/></Target><Target id="@+id/tv_total_size" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="41"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="62" startOffset="4" endLine="128" endOffset="59"/></Target><Target id="@+id/recycler_view_scans" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="73" startOffset="12" endLine="79" endOffset="57"/></Target><Target id="@+id/tv_empty_state" view="LinearLayout"><Expressions/><location startLine="82" startOffset="12" endLine="116" endOffset="26"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="119" startOffset="12" endLine="124" endOffset="43"/></Target><Target id="@+id/fab_new_scan" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="131" startOffset="4" endLine="140" endOffset="33"/></Target></Targets></Layout>