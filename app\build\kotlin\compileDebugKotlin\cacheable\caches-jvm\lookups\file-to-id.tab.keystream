3app/src/main/java/com/scanner3d/app/MainActivity.kt:app/src/main/java/com/scanner3d/app/core/ScanningEngine.kt<app/src/main/java/com/scanner3d/app/data/database/ScanDao.ktFapp/src/main/java/com/scanner3d/app/data/database/Scanner3DDatabase.kt@app/src/main/java/com/scanner3d/app/data/database/SettingsDao.kt=app/src/main/java/com/scanner3d/app/data/model/AppSettings.kt8app/src/main/java/com/scanner3d/app/data/model/Mesh3D.kt@app/src/main/java/com/scanner3d/app/data/model/PointCloudData.kt<app/src/main/java/com/scanner3d/app/data/model/ScanEntity.kt>app/src/main/java/com/scanner3d/app/data/model/ScanProgress.kt@app/src/main/java/com/scanner3d/app/repository/AuthRepository.ktHapp/src/main/java/com/scanner3d/app/repository/CloudStorageRepository.kt@app/src/main/java/com/scanner3d/app/repository/ScanRepository.ktEapp/src/main/java/com/scanner3d/app/ui/auth/AuthenticationActivity.kt:app/src/main/java/com/scanner3d/app/ui/custom/DepthView.kt?app/src/main/java/com/scanner3d/app/ui/custom/PointCloudView.ktAapp/src/main/java/com/scanner3d/app/ui/gallery/GalleryActivity.kt@app/src/main/java/com/scanner3d/app/ui/gallery/GalleryAdapter.ktCapp/src/main/java/com/scanner3d/app/ui/scanning/ScanningActivity.kt>app/src/main/java/com/scanner3d/app/utils/EncryptionManager.kt8app/src/main/java/com/scanner3d/app/utils/FileManager.kt:app/src/main/java/com/scanner3d/app/utils/MemoryManager.kt9app/src/main/java/com/scanner3d/app/utils/MeshExporter.kt:app/src/main/java/com/scanner3d/app/utils/MeshGenerator.kt;app/src/main/java/com/scanner3d/app/utils/MeshSimplifier.kt>app/src/main/java/com/scanner3d/app/utils/TextureCompressor.kt:app/src/main/java/com/scanner3d/app/utils/TextureMapper.kt?app/src/main/java/com/scanner3d/app/utils/ThumbnailGenerator.kt>app/src/main/java/com/scanner3d/app/viewmodel/AuthViewModel.ktAapp/src/main/java/com/scanner3d/app/viewmodel/GalleryViewModel.kt>app/src/main/java/com/scanner3d/app/viewmodel/MainViewModel.ktBapp/src/main/java/com/scanner3d/app/viewmodel/ScanningViewModel.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    