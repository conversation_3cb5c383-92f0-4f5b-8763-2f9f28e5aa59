  Application android.app  ActivityAuthenticationBinding android.app.Activity  ActivityGalleryBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityScanningBinding android.app.Activity  
AuthViewModel android.app.Activity  GalleryAdapter android.app.Activity  GalleryViewModel android.app.Activity  
MainViewModel android.app.Activity  ScanningViewModel android.app.Activity  Context android.content  SharedPreferences android.content  ActivityAuthenticationBinding android.content.Context  ActivityGalleryBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityScanningBinding android.content.Context  
AuthViewModel android.content.Context  GalleryAdapter android.content.Context  GalleryViewModel android.content.Context  
MainViewModel android.content.Context  ScanningViewModel android.content.Context  ActivityAuthenticationBinding android.content.ContextWrapper  ActivityGalleryBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityScanningBinding android.content.ContextWrapper  
AuthViewModel android.content.ContextWrapper  GalleryAdapter android.content.ContextWrapper  GalleryViewModel android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  ScanningViewModel android.content.ContextWrapper  Bitmap android.graphics  Config android.hardware.camera2  
ImageAnalysis android.hardware.camera2  ImageCapture android.hardware.camera2  
PointCloud android.hardware.camera2  Preview android.hardware.camera2  Recorder android.hardware.camera2  Session android.hardware.camera2  VideoCapture android.hardware.camera2  
GLSurfaceView android.opengl  AttributeSet android.opengl.GLSurfaceView  Context android.opengl.GLSurfaceView  
GLSurfaceView android.opengl.GLSurfaceView  Int android.opengl.GLSurfaceView  PointCloudData android.opengl.GLSurfaceView  Renderer android.opengl.GLSurfaceView  
Parcelable 
android.os  AttributeSet android.util  LruCache android.util  View android.view  ActivityAuthenticationBinding  android.view.ContextThemeWrapper  ActivityGalleryBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityScanningBinding  android.view.ContextThemeWrapper  
AuthViewModel  android.view.ContextThemeWrapper  GalleryAdapter  android.view.ContextThemeWrapper  GalleryViewModel  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  ScanningViewModel  android.view.ContextThemeWrapper  AttributeSet android.view.SurfaceView  Context android.view.SurfaceView  
GLSurfaceView android.view.SurfaceView  Int android.view.SurfaceView  PointCloudData android.view.SurfaceView  AttributeSet android.view.View  Bitmap android.view.View  Context android.view.View  
FloatArray android.view.View  
GLSurfaceView android.view.View  Int android.view.View  PointCloudData android.view.View  ActivityAuthenticationBinding #androidx.activity.ComponentActivity  ActivityGalleryBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityScanningBinding #androidx.activity.ComponentActivity  
AuthViewModel #androidx.activity.ComponentActivity  GalleryAdapter #androidx.activity.ComponentActivity  GalleryViewModel #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  ScanningViewModel #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  ActivityAuthenticationBinding (androidx.appcompat.app.AppCompatActivity  ActivityGalleryBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityScanningBinding (androidx.appcompat.app.AppCompatActivity  
AuthViewModel (androidx.appcompat.app.AppCompatActivity  GalleryAdapter (androidx.appcompat.app.AppCompatActivity  GalleryViewModel (androidx.appcompat.app.AppCompatActivity  
MainViewModel (androidx.appcompat.app.AppCompatActivity  ScanningViewModel (androidx.appcompat.app.AppCompatActivity  Config androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageCapture androidx.camera.core  
PointCloud androidx.camera.core  Preview androidx.camera.core  Recorder androidx.camera.core  Session androidx.camera.core  VideoCapture androidx.camera.core  ProcessCameraProvider androidx.camera.lifecycle  ActivityAuthenticationBinding #androidx.core.app.ComponentActivity  ActivityGalleryBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityScanningBinding #androidx.core.app.ComponentActivity  
AuthViewModel #androidx.core.app.ComponentActivity  GalleryAdapter #androidx.core.app.ComponentActivity  GalleryViewModel #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  ScanningViewModel #androidx.core.app.ComponentActivity  ActivityAuthenticationBinding &androidx.fragment.app.FragmentActivity  ActivityGalleryBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityScanningBinding &androidx.fragment.app.FragmentActivity  
AuthViewModel &androidx.fragment.app.FragmentActivity  GalleryAdapter &androidx.fragment.app.FragmentActivity  GalleryViewModel &androidx.fragment.app.FragmentActivity  
MainViewModel &androidx.fragment.app.FragmentActivity  ScanningViewModel &androidx.fragment.app.FragmentActivity  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  PointCloudData #androidx.lifecycle.AndroidViewModel  
ScanEntity #androidx.lifecycle.AndroidViewModel  ScanProgress #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  PointCloudData androidx.lifecycle.ViewModel  
ScanEntity androidx.lifecycle.ViewModel  ScanProgress androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  
ScanEntity (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  
ViewHolder )androidx.recyclerview.widget.RecyclerView  
ScanEntity 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Callback androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  RoomDatabase androidx.room.RoomDatabase  ScanDao androidx.room.RoomDatabase  Scanner3DDatabase androidx.room.RoomDatabase  SettingsDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  SupportSQLiteDatabase #androidx.room.RoomDatabase.Callback  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  	MasterKey androidx.security.crypto  SupportSQLiteDatabase androidx.sqlite.db  Config com.google.ar.core  
ImageAnalysis com.google.ar.core  ImageCapture com.google.ar.core  
PointCloud com.google.ar.core  Preview com.google.ar.core  Recorder com.google.ar.core  Session com.google.ar.core  VideoCapture com.google.ar.core  ActivityMainBinding com.scanner3d.app.MainActivity  
MainViewModel com.scanner3d.app.MainActivity  Boolean com.scanner3d.app.core  Config com.scanner3d.app.core  
ImageAnalysis com.scanner3d.app.core  ImageCapture com.scanner3d.app.core  
PointCloud com.scanner3d.app.core  Preview com.scanner3d.app.core  Recorder com.scanner3d.app.core  Session com.scanner3d.app.core  VideoCapture com.scanner3d.app.core  Boolean %com.scanner3d.app.core.ScanningEngine  Config %com.scanner3d.app.core.ScanningEngine  Context %com.scanner3d.app.core.ScanningEngine  ExecutorService %com.scanner3d.app.core.ScanningEngine  
ImageAnalysis %com.scanner3d.app.core.ScanningEngine  ImageCapture %com.scanner3d.app.core.ScanningEngine  
PointCloud %com.scanner3d.app.core.ScanningEngine  PointCloudData %com.scanner3d.app.core.ScanningEngine  Preview %com.scanner3d.app.core.ScanningEngine  ProcessCameraProvider %com.scanner3d.app.core.ScanningEngine  Recorder %com.scanner3d.app.core.ScanningEngine  ScanProgress %com.scanner3d.app.core.ScanningEngine  Session %com.scanner3d.app.core.ScanningEngine  	StateFlow %com.scanner3d.app.core.ScanningEngine  VideoCapture %com.scanner3d.app.core.ScanningEngine  Boolean /com.scanner3d.app.core.ScanningEngine.Companion  Config /com.scanner3d.app.core.ScanningEngine.Companion  Context /com.scanner3d.app.core.ScanningEngine.Companion  ExecutorService /com.scanner3d.app.core.ScanningEngine.Companion  
ImageAnalysis /com.scanner3d.app.core.ScanningEngine.Companion  ImageCapture /com.scanner3d.app.core.ScanningEngine.Companion  
PointCloud /com.scanner3d.app.core.ScanningEngine.Companion  PointCloudData /com.scanner3d.app.core.ScanningEngine.Companion  Preview /com.scanner3d.app.core.ScanningEngine.Companion  ProcessCameraProvider /com.scanner3d.app.core.ScanningEngine.Companion  Recorder /com.scanner3d.app.core.ScanningEngine.Companion  ScanProgress /com.scanner3d.app.core.ScanningEngine.Companion  Session /com.scanner3d.app.core.ScanningEngine.Companion  	StateFlow /com.scanner3d.app.core.ScanningEngine.Companion  VideoCapture /com.scanner3d.app.core.ScanningEngine.Companion  AppSettings com.scanner3d.app.data.database  Boolean com.scanner3d.app.data.database  Dao com.scanner3d.app.data.database  Delete com.scanner3d.app.data.database  Double com.scanner3d.app.data.database  Insert com.scanner3d.app.data.database  Int com.scanner3d.app.data.database  List com.scanner3d.app.data.database  Long com.scanner3d.app.data.database  OnConflictStrategy com.scanner3d.app.data.database  Query com.scanner3d.app.data.database  ScanDao com.scanner3d.app.data.database  
ScanEntity com.scanner3d.app.data.database  Scanner3DDatabase com.scanner3d.app.data.database  SettingsDao com.scanner3d.app.data.database  String com.scanner3d.app.data.database  Update com.scanner3d.app.data.database  Volatile com.scanner3d.app.data.database  Boolean 'com.scanner3d.app.data.database.ScanDao  Delete 'com.scanner3d.app.data.database.ScanDao  Double 'com.scanner3d.app.data.database.ScanDao  Flow 'com.scanner3d.app.data.database.ScanDao  Insert 'com.scanner3d.app.data.database.ScanDao  Int 'com.scanner3d.app.data.database.ScanDao  List 'com.scanner3d.app.data.database.ScanDao  LiveData 'com.scanner3d.app.data.database.ScanDao  Long 'com.scanner3d.app.data.database.ScanDao  OnConflictStrategy 'com.scanner3d.app.data.database.ScanDao  Query 'com.scanner3d.app.data.database.ScanDao  
ScanEntity 'com.scanner3d.app.data.database.ScanDao  String 'com.scanner3d.app.data.database.ScanDao  Update 'com.scanner3d.app.data.database.ScanDao  Context 1com.scanner3d.app.data.database.Scanner3DDatabase  	Migration 1com.scanner3d.app.data.database.Scanner3DDatabase  RoomDatabase 1com.scanner3d.app.data.database.Scanner3DDatabase  ScanDao 1com.scanner3d.app.data.database.Scanner3DDatabase  Scanner3DDatabase 1com.scanner3d.app.data.database.Scanner3DDatabase  SettingsDao 1com.scanner3d.app.data.database.Scanner3DDatabase  SupportSQLiteDatabase 1com.scanner3d.app.data.database.Scanner3DDatabase  Volatile 1com.scanner3d.app.data.database.Scanner3DDatabase  Context ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  	Migration ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  RoomDatabase ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  ScanDao ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  Scanner3DDatabase ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  SettingsDao ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  SupportSQLiteDatabase ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  Volatile ;com.scanner3d.app.data.database.Scanner3DDatabase.Companion  SupportSQLiteDatabase Lcom.scanner3d.app.data.database.Scanner3DDatabase.Companion.DatabaseCallback  AppSettings +com.scanner3d.app.data.database.SettingsDao  Boolean +com.scanner3d.app.data.database.SettingsDao  Flow +com.scanner3d.app.data.database.SettingsDao  Insert +com.scanner3d.app.data.database.SettingsDao  LiveData +com.scanner3d.app.data.database.SettingsDao  Long +com.scanner3d.app.data.database.SettingsDao  OnConflictStrategy +com.scanner3d.app.data.database.SettingsDao  Query +com.scanner3d.app.data.database.SettingsDao  String +com.scanner3d.app.data.database.SettingsDao  Update +com.scanner3d.app.data.database.SettingsDao  AppSettings com.scanner3d.app.data.model  Boolean com.scanner3d.app.data.model  Float com.scanner3d.app.data.model  Int com.scanner3d.app.data.model  Long com.scanner3d.app.data.model  Mesh3D com.scanner3d.app.data.model  PointCloudData com.scanner3d.app.data.model  
ScanEntity com.scanner3d.app.data.model  ScanProgress com.scanner3d.app.data.model  String com.scanner3d.app.data.model  Triple com.scanner3d.app.data.model  Boolean (com.scanner3d.app.data.model.AppSettings  Int (com.scanner3d.app.data.model.AppSettings  Long (com.scanner3d.app.data.model.AppSettings  Mesh3D (com.scanner3d.app.data.model.AppSettings  
PrimaryKey (com.scanner3d.app.data.model.AppSettings  String (com.scanner3d.app.data.model.AppSettings  BoundingBox #com.scanner3d.app.data.model.Mesh3D  Float #com.scanner3d.app.data.model.Mesh3D  MeshQuality #com.scanner3d.app.data.model.Mesh3D  
Parcelable #com.scanner3d.app.data.model.Mesh3D  	Parcelize #com.scanner3d.app.data.model.Mesh3D  Triple #com.scanner3d.app.data.model.Mesh3D  Float /com.scanner3d.app.data.model.Mesh3D.BoundingBox  Triple /com.scanner3d.app.data.model.Mesh3D.BoundingBox  Boolean 'com.scanner3d.app.data.model.ScanEntity  Float 'com.scanner3d.app.data.model.ScanEntity  Int 'com.scanner3d.app.data.model.ScanEntity  Long 'com.scanner3d.app.data.model.ScanEntity  Mesh3D 'com.scanner3d.app.data.model.ScanEntity  
PrimaryKey 'com.scanner3d.app.data.model.ScanEntity  String 'com.scanner3d.app.data.model.ScanEntity  Boolean )com.scanner3d.app.data.model.ScanProgress  Float )com.scanner3d.app.data.model.ScanProgress  Int )com.scanner3d.app.data.model.ScanProgress  Long )com.scanner3d.app.data.model.ScanProgress  String )com.scanner3d.app.data.model.ScanProgress  ActivityAuthenticationBinding com.scanner3d.app.databinding  ActivityGalleryBinding com.scanner3d.app.databinding  ActivityMainBinding com.scanner3d.app.databinding  ActivityScanningBinding com.scanner3d.app.databinding  Context +com.scanner3d.app.repository.AuthRepository  SharedPreferences +com.scanner3d.app.repository.AuthRepository  Context 5com.scanner3d.app.repository.AuthRepository.Companion  SharedPreferences 5com.scanner3d.app.repository.AuthRepository.Companion  Context 3com.scanner3d.app.repository.CloudStorageRepository  Context =com.scanner3d.app.repository.CloudStorageRepository.Companion  Context +com.scanner3d.app.repository.ScanRepository  Context 5com.scanner3d.app.repository.ScanRepository.Companion  ActivityAuthenticationBinding 0com.scanner3d.app.ui.auth.AuthenticationActivity  
AuthViewModel 0com.scanner3d.app.ui.auth.AuthenticationActivity  Bitmap com.scanner3d.app.ui.custom  
FloatArray com.scanner3d.app.ui.custom  Int com.scanner3d.app.ui.custom  AttributeSet %com.scanner3d.app.ui.custom.DepthView  Bitmap %com.scanner3d.app.ui.custom.DepthView  Context %com.scanner3d.app.ui.custom.DepthView  
FloatArray %com.scanner3d.app.ui.custom.DepthView  Int %com.scanner3d.app.ui.custom.DepthView  AttributeSet *com.scanner3d.app.ui.custom.PointCloudView  Context *com.scanner3d.app.ui.custom.PointCloudView  
GLSurfaceView *com.scanner3d.app.ui.custom.PointCloudView  Int *com.scanner3d.app.ui.custom.PointCloudView  PointCloudData *com.scanner3d.app.ui.custom.PointCloudView  PointCloudData =com.scanner3d.app.ui.custom.PointCloudView.PointCloudRenderer  GalleryAdapter com.scanner3d.app.ui.gallery  Unit com.scanner3d.app.ui.gallery  ActivityGalleryBinding ,com.scanner3d.app.ui.gallery.GalleryActivity  GalleryAdapter ,com.scanner3d.app.ui.gallery.GalleryActivity  GalleryViewModel ,com.scanner3d.app.ui.gallery.GalleryActivity  
ScanEntity +com.scanner3d.app.ui.gallery.GalleryAdapter  Unit +com.scanner3d.app.ui.gallery.GalleryAdapter  
ScanEntity 5com.scanner3d.app.ui.gallery.GalleryAdapter.Companion  Unit 5com.scanner3d.app.ui.gallery.GalleryAdapter.Companion  ActivityScanningBinding .com.scanner3d.app.ui.scanning.ScanningActivity  ScanningViewModel .com.scanner3d.app.ui.scanning.ScanningActivity  ActivityScanningBinding 8com.scanner3d.app.ui.scanning.ScanningActivity.Companion  ScanningViewModel 8com.scanner3d.app.ui.scanning.ScanningActivity.Companion  Any com.scanner3d.app.utils  EncryptionManager com.scanner3d.app.utils  Float com.scanner3d.app.utils  Int com.scanner3d.app.utils  Long com.scanner3d.app.utils  
MemoryManager com.scanner3d.app.utils  String com.scanner3d.app.utils  Volatile com.scanner3d.app.utils  Context )com.scanner3d.app.utils.EncryptionManager  EncryptionManager )com.scanner3d.app.utils.EncryptionManager  KeyStore )com.scanner3d.app.utils.EncryptionManager  	MasterKey )com.scanner3d.app.utils.EncryptionManager  Volatile )com.scanner3d.app.utils.EncryptionManager  Context 3com.scanner3d.app.utils.EncryptionManager.Companion  EncryptionManager 3com.scanner3d.app.utils.EncryptionManager.Companion  KeyStore 3com.scanner3d.app.utils.EncryptionManager.Companion  	MasterKey 3com.scanner3d.app.utils.EncryptionManager.Companion  Volatile 3com.scanner3d.app.utils.EncryptionManager.Companion  Context #com.scanner3d.app.utils.FileManager  File #com.scanner3d.app.utils.FileManager  Float #com.scanner3d.app.utils.FileManager  Long #com.scanner3d.app.utils.FileManager  Context -com.scanner3d.app.utils.FileManager.Companion  File -com.scanner3d.app.utils.FileManager.Companion  Float -com.scanner3d.app.utils.FileManager.Companion  Long -com.scanner3d.app.utils.FileManager.Companion  Float /com.scanner3d.app.utils.FileManager.StorageInfo  Long /com.scanner3d.app.utils.FileManager.StorageInfo  Any %com.scanner3d.app.utils.MemoryManager  Bitmap %com.scanner3d.app.utils.MemoryManager  Context %com.scanner3d.app.utils.MemoryManager  Float %com.scanner3d.app.utils.MemoryManager  Int %com.scanner3d.app.utils.MemoryManager  Long %com.scanner3d.app.utils.MemoryManager  LruCache %com.scanner3d.app.utils.MemoryManager  
MemoryManager %com.scanner3d.app.utils.MemoryManager  String %com.scanner3d.app.utils.MemoryManager  Volatile %com.scanner3d.app.utils.MemoryManager  
WeakReference %com.scanner3d.app.utils.MemoryManager  Float 0com.scanner3d.app.utils.MemoryManager.CacheStats  Int 0com.scanner3d.app.utils.MemoryManager.CacheStats  Long 0com.scanner3d.app.utils.MemoryManager.CacheStats  Any /com.scanner3d.app.utils.MemoryManager.Companion  Bitmap /com.scanner3d.app.utils.MemoryManager.Companion  Context /com.scanner3d.app.utils.MemoryManager.Companion  Float /com.scanner3d.app.utils.MemoryManager.Companion  Int /com.scanner3d.app.utils.MemoryManager.Companion  Long /com.scanner3d.app.utils.MemoryManager.Companion  LruCache /com.scanner3d.app.utils.MemoryManager.Companion  
MemoryManager /com.scanner3d.app.utils.MemoryManager.Companion  String /com.scanner3d.app.utils.MemoryManager.Companion  Volatile /com.scanner3d.app.utils.MemoryManager.Companion  
WeakReference /com.scanner3d.app.utils.MemoryManager.Companion  Int &com.scanner3d.app.utils.MeshSimplifier  Int 0com.scanner3d.app.utils.MeshSimplifier.Companion  Int 6com.scanner3d.app.utils.MeshSimplifier.VertexAdjacency  Int )com.scanner3d.app.utils.TextureCompressor  Int 3com.scanner3d.app.utils.TextureCompressor.Companion  Int <com.scanner3d.app.utils.TextureCompressor.CompressionQuality  
AuthViewModel com.scanner3d.app.viewmodel  Boolean com.scanner3d.app.viewmodel  GalleryViewModel com.scanner3d.app.viewmodel  List com.scanner3d.app.viewmodel  
MainViewModel com.scanner3d.app.viewmodel  ScanningViewModel com.scanner3d.app.viewmodel  String com.scanner3d.app.viewmodel  Application )com.scanner3d.app.viewmodel.AuthViewModel  
AuthResult )com.scanner3d.app.viewmodel.AuthViewModel  Boolean )com.scanner3d.app.viewmodel.AuthViewModel  LiveData )com.scanner3d.app.viewmodel.AuthViewModel  Application ,com.scanner3d.app.viewmodel.GalleryViewModel  Boolean ,com.scanner3d.app.viewmodel.GalleryViewModel  List ,com.scanner3d.app.viewmodel.GalleryViewModel  LiveData ,com.scanner3d.app.viewmodel.GalleryViewModel  
ScanEntity ,com.scanner3d.app.viewmodel.GalleryViewModel  	ScanStats ,com.scanner3d.app.viewmodel.GalleryViewModel  String ,com.scanner3d.app.viewmodel.GalleryViewModel  Application )com.scanner3d.app.viewmodel.MainViewModel  Boolean )com.scanner3d.app.viewmodel.MainViewModel  LiveData )com.scanner3d.app.viewmodel.MainViewModel  String )com.scanner3d.app.viewmodel.MainViewModel  Application -com.scanner3d.app.viewmodel.ScanningViewModel  Boolean -com.scanner3d.app.viewmodel.ScanningViewModel  LiveData -com.scanner3d.app.viewmodel.ScanningViewModel  PointCloudData -com.scanner3d.app.viewmodel.ScanningViewModel  ScanProgress -com.scanner3d.app.viewmodel.ScanningViewModel  String -com.scanner3d.app.viewmodel.ScanningViewModel  File java.io  AppSettings 	java.lang  OnConflictStrategy 	java.lang  Recorder 	java.lang  
ScanEntity 	java.lang  VideoCapture 	java.lang  
WeakReference 
java.lang.ref  KeyStore 
java.security  ExecutorService java.util.concurrent  Any kotlin  AppSettings kotlin  Array kotlin  Boolean kotlin  Double kotlin  Float kotlin  
FloatArray kotlin  Int kotlin  Long kotlin  OnConflictStrategy kotlin  Recorder kotlin  
ScanEntity kotlin  String kotlin  Triple kotlin  Unit kotlin  VideoCapture kotlin  Volatile kotlin  arrayOf kotlin  AppSettings kotlin.annotation  OnConflictStrategy kotlin.annotation  Recorder kotlin.annotation  
ScanEntity kotlin.annotation  Triple kotlin.annotation  VideoCapture kotlin.annotation  Volatile kotlin.annotation  AppSettings kotlin.collections  List kotlin.collections  OnConflictStrategy kotlin.collections  Recorder kotlin.collections  
ScanEntity kotlin.collections  Triple kotlin.collections  VideoCapture kotlin.collections  Volatile kotlin.collections  AppSettings kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Recorder kotlin.comparisons  
ScanEntity kotlin.comparisons  Triple kotlin.comparisons  VideoCapture kotlin.comparisons  Volatile kotlin.comparisons  AppSettings 	kotlin.io  OnConflictStrategy 	kotlin.io  Recorder 	kotlin.io  
ScanEntity 	kotlin.io  Triple 	kotlin.io  VideoCapture 	kotlin.io  Volatile 	kotlin.io  AppSettings 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Recorder 
kotlin.jvm  
ScanEntity 
kotlin.jvm  Triple 
kotlin.jvm  VideoCapture 
kotlin.jvm  Volatile 
kotlin.jvm  Bitmap kotlin.math  AppSettings 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Recorder 
kotlin.ranges  
ScanEntity 
kotlin.ranges  Triple 
kotlin.ranges  VideoCapture 
kotlin.ranges  Volatile 
kotlin.ranges  KClass kotlin.reflect  AppSettings kotlin.sequences  OnConflictStrategy kotlin.sequences  Recorder kotlin.sequences  
ScanEntity kotlin.sequences  Triple kotlin.sequences  VideoCapture kotlin.sequences  Volatile kotlin.sequences  AppSettings kotlin.text  OnConflictStrategy kotlin.text  Recorder kotlin.text  
ScanEntity kotlin.text  Triple kotlin.text  VideoCapture kotlin.text  Volatile kotlin.text  Volatile kotlinx.coroutines  Flow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  	Parcelize kotlinx.parcelize                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         