{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,248,355,467,583,703,819,934,1026,1158,1283", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "156,243,350,462,578,698,814,929,1021,1153,1278,1383"}, "to": {"startLines": "73,76,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6821,7115,7827,7934,8046,8162,8282,8398,8513,8605,8737,8862", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "6922,7197,7929,8041,8157,8277,8393,8508,8600,8732,8857,8962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3700,3793,3893,3990,4089,4185,4287,14216", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3788,3888,3985,4084,4180,4282,4382,14312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5740", "endColumns": "131", "endOffsets": "5867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "74,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "6927,7529,7624,7730", "endColumns": "95,94,105,96", "endOffsets": "7018,7619,7725,7822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,234,280,382,485", "endColumns": "43,45,101,102,78", "endOffsets": "233,279,381,484,563"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "311,359,409,515,622", "endColumns": "47,49,105,106,82", "endOffsets": "354,404,510,617,700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1282", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1277,1393"}, "to": {"startLines": "53,54,75,77,78,95,96,155,156,157,158,160,161,163,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4655,4738,7023,7202,7298,9029,9107,13539,13621,13699,13765,13907,13985,14146,14317,14382", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,64,115", "endOffsets": "4733,4810,7110,7293,7375,9102,9185,13616,13694,13760,13826,13980,14061,14211,14377,14493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,109", "endOffsets": "154,264"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13325,13429", "endColumns": "103,109", "endOffsets": "13424,13534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,970,1055,1117,1175,1260,1323,1385,1443,1509,1571,1626,1722,1779,1838,1894,1961,2066,2146,2227,2356,2429,2500,2614,2696,2772,2823,2874,2940,3006,3079,3160,3235,3303,3376,3447,3514,3612,3697,3764,3851,3939,4013,4081,4166,4217,4295,4359,4439,4521,4583,4647,4710,4776,4871,4966,5051,5142,5197,5252", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "256,327,395,470,552,633,722,824,901,965,1050,1112,1170,1255,1318,1380,1438,1504,1566,1621,1717,1774,1833,1889,1956,2061,2141,2222,2351,2424,2495,2609,2691,2767,2818,2869,2935,3001,3074,3155,3230,3298,3371,3442,3509,3607,3692,3759,3846,3934,4008,4076,4161,4212,4290,4354,4434,4516,4578,4642,4705,4771,4866,4961,5046,5137,5192,5247,5323"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3323,3394,3462,3537,3619,4387,4476,4578,7380,7444,8967,9190,9248,9333,9396,9458,9516,9582,9644,9699,9795,9852,9911,9967,10034,10139,10219,10300,10429,10502,10573,10687,10769,10845,10896,10947,11013,11079,11152,11233,11308,11376,11449,11520,11587,11685,11770,11837,11924,12012,12086,12154,12239,12290,12368,12432,12512,12594,12656,12720,12783,12849,12944,13039,13124,13215,13270,13831", "endLines": "5,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "306,3389,3457,3532,3614,3695,4471,4573,4650,7439,7524,9024,9243,9328,9391,9453,9511,9577,9639,9694,9790,9847,9906,9962,10029,10134,10214,10295,10424,10497,10568,10682,10764,10840,10891,10942,11008,11074,11147,11228,11303,11371,11444,11515,11582,11680,11765,11832,11919,12007,12081,12149,12234,12285,12363,12427,12507,12589,12651,12715,12778,12844,12939,13034,13119,13210,13265,13320,13902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "705,803,901,1007,1093,1196,1313,1391,1467,1558,1651,1743,1837,1937,2030,2125,2218,2309,2400,2480,2580,2680,2776,2878,2978,3077,3227,14066", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "798,896,1002,1088,1191,1308,1386,1462,1553,1646,1738,1832,1932,2025,2120,2213,2304,2395,2475,2575,2675,2771,2873,2973,3072,3222,3318,14141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4815,4917,5056,5178,5280,5407,5530,5638,5872,6000,6103,6248,6371,6506,6633,6693,6750", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4912,5051,5173,5275,5402,5525,5633,5735,5995,6098,6243,6366,6501,6628,6688,6745,6816"}}]}]}