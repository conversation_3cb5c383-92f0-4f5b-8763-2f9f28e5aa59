package com.scanner3d.app.repository

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.database.ScanDao
import com.scanner3d.app.data.model.Mesh3D
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.utils.FileManager
import com.scanner3d.app.utils.MeshExporter
import com.scanner3d.app.utils.ThumbnailGenerator
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.robolectric.RobolectricTestRunner
import java.io.File

@RunWith(RobolectricTestRunner::class)
class ScanRepositoryTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private lateinit var database: Scanner3DDatabase
    private lateinit var scanDao: ScanDao
    private lateinit var scanRepository: ScanRepository
    private lateinit var context: Context
    
    @Mock
    private lateinit var mockFileManager: FileManager
    
    @Mock
    private lateinit var mockMeshExporter: MeshExporter
    
    @Mock
    private lateinit var mockThumbnailGenerator: ThumbnailGenerator
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        context = ApplicationProvider.getApplicationContext()
        
        database = Room.inMemoryDatabaseBuilder(context, Scanner3DDatabase::class.java)
            .allowMainThreadQueries()
            .build()
        
        scanDao = database.scanDao()
        scanRepository = ScanRepository(context)
    }
    
    @After
    fun tearDown() {
        database.close()
    }
    
    @Test
    fun `saveScan should save mesh and create database entry`() = runBlocking {
        // Arrange
        val mesh = createTestMesh()
        val scanName = "Test Scan"
        val description = "Test Description"
        val format = "OBJ"
        
        val mockFile = mock<File>()
        whenever(mockFile.absolutePath).thenReturn("/test/path/scan.obj")
        whenever(mockFile.length()).thenReturn(1024L)
        
        // Act
        val result = scanRepository.saveScan(mesh, scanName, description, format)
        
        // Assert
        assertTrue("Save should succeed", result.isSuccess)
        assertNotNull("Scan ID should be returned", result.getOrNull())
        
        // Verify scan was saved to database
        val scanId = result.getOrNull()!!
        val savedScan = scanDao.getScanById(scanId)
        assertNotNull("Scan should exist in database", savedScan)
        assertEquals("Name should match", scanName, savedScan?.name)
        assertEquals("Description should match", description, savedScan?.description)
        assertEquals("Format should match", format, savedScan?.format)
    }
    
    @Test
    fun `deleteScan should remove scan and files`() = runBlocking {
        // Arrange
        val scanEntity = createTestScanEntity()
        scanDao.insertScan(scanEntity)
        
        // Act
        val result = scanRepository.deleteScan(scanEntity.id)
        
        // Assert
        assertTrue("Delete should succeed", result.isSuccess)
        
        // Verify scan was removed from database
        val deletedScan = scanDao.getScanById(scanEntity.id)
        assertNull("Scan should not exist in database", deletedScan)
    }
    
    @Test
    fun `getAllScans should return all scans from database`() = runBlocking {
        // Arrange
        val scan1 = createTestScanEntity("scan1")
        val scan2 = createTestScanEntity("scan2")
        scanDao.insertScan(scan1)
        scanDao.insertScan(scan2)
        
        // Act
        val scans = scanRepository.getAllScans().first()
        
        // Assert
        assertEquals("Should return 2 scans", 2, scans.size)
        assertTrue("Should contain scan1", scans.any { it.id == "scan1" })
        assertTrue("Should contain scan2", scans.any { it.id == "scan2" })
    }
    
    @Test
    fun `updateScanMetadata should update scan information`() = runBlocking {
        // Arrange
        val scanEntity = createTestScanEntity()
        scanDao.insertScan(scanEntity)
        
        val newName = "Updated Name"
        val newDescription = "Updated Description"
        
        // Act
        val result = scanRepository.updateScanMetadata(scanEntity.id, newName, newDescription)
        
        // Assert
        assertTrue("Update should succeed", result.isSuccess)
        
        // Verify scan was updated
        val updatedScan = scanDao.getScanById(scanEntity.id)
        assertNotNull("Scan should still exist", updatedScan)
        assertEquals("Name should be updated", newName, updatedScan?.name)
        assertEquals("Description should be updated", newDescription, updatedScan?.description)
        assertTrue("Modified time should be updated", 
            updatedScan?.modifiedAt ?: 0 > scanEntity.modifiedAt)
    }
    
    @Test
    fun `exportScan should export mesh to specified format`() = runBlocking {
        // Arrange
        val scanEntity = createTestScanEntity()
        scanDao.insertScan(scanEntity)
        
        val outputFile = File.createTempFile("export", ".stl")
        val targetFormat = "STL"
        
        // Mock mesh exporter
        whenever(mockMeshExporter.importMesh(any(), any())).thenReturn(createTestMesh())
        whenever(mockMeshExporter.exportMesh(any(), any(), any())).thenReturn(true)
        
        // Act
        val result = scanRepository.exportScan(scanEntity.id, targetFormat, outputFile)
        
        // Assert
        assertTrue("Export should succeed", result.isSuccess)
        assertEquals("Should return output file", outputFile, result.getOrNull())
    }
    
    @Test
    fun `getScanStats should return correct statistics`() = runBlocking {
        // Arrange
        val scan1 = createTestScanEntity("scan1").copy(fileSize = 1000L, scanDuration = 30000L)
        val scan2 = createTestScanEntity("scan2").copy(fileSize = 2000L, scanDuration = 60000L)
        scanDao.insertScan(scan1)
        scanDao.insertScan(scan2)
        
        // Act
        val stats = scanRepository.getScanStats()
        
        // Assert
        assertEquals("Should have 2 scans", 2, stats.totalScans)
        assertEquals("Total size should be 3000", 3000L, stats.totalSize)
        assertEquals("Average duration should be 45000", 45000L, stats.averageScanDuration)
    }
    
    @Test
    fun `cleanupOldScans should remove scans older than retention period`() = runBlocking {
        // Arrange
        val oldTime = System.currentTimeMillis() - (10 * 24 * 60 * 60 * 1000L) // 10 days ago
        val recentTime = System.currentTimeMillis() - (1 * 24 * 60 * 60 * 1000L) // 1 day ago
        
        val oldScan = createTestScanEntity("old").copy(createdAt = oldTime)
        val recentScan = createTestScanEntity("recent").copy(createdAt = recentTime)
        
        scanDao.insertScan(oldScan)
        scanDao.insertScan(recentScan)
        
        // Act
        val result = scanRepository.cleanupOldScans(7) // 7 days retention
        
        // Assert
        assertTrue("Cleanup should succeed", result.isSuccess)
        assertEquals("Should delete 1 scan", 1, result.getOrNull())
        
        // Verify old scan was removed and recent scan remains
        val remainingScans = scanDao.getAllScans().first()
        assertEquals("Should have 1 remaining scan", 1, remainingScans.size)
        assertEquals("Recent scan should remain", "recent", remainingScans[0].id)
    }
    
    @Test
    fun `saveScan should handle mesh export failure`() = runBlocking {
        // Arrange
        val mesh = createTestMesh()
        
        // Mock export failure
        whenever(mockMeshExporter.exportMesh(any(), any(), any())).thenReturn(false)
        
        // Act
        val result = scanRepository.saveScan(mesh, "Test", null, "OBJ")
        
        // Assert
        assertTrue("Save should fail", result.isFailure)
        assertTrue("Should contain export error", 
            result.exceptionOrNull()?.message?.contains("Failed to export mesh") == true)
    }
    
    private fun createTestMesh(): Mesh3D {
        val vertices = floatArrayOf(
            0f, 0f, 0f,  // vertex 0
            1f, 0f, 0f,  // vertex 1
            0f, 1f, 0f   // vertex 2
        )
        val indices = intArrayOf(0, 1, 2)
        val boundingBox = Mesh3D.BoundingBox(0f, 0f, 0f, 1f, 1f, 0f)
        val metadata = Mesh3D.MeshMetadata(
            createdAt = System.currentTimeMillis(),
            scanDuration = 30000L,
            quality = Mesh3D.MeshQuality.MEDIUM,
            hasTexture = false,
            hasColors = false,
            estimatedFileSize = 1024L,
            scannerVersion = "1.0"
        )
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 3,
            triangleCount = 1,
            boundingBox = boundingBox,
            metadata = metadata
        )
    }
    
    private fun createTestScanEntity(id: String = "test-scan"): ScanEntity {
        return ScanEntity(
            id = id,
            name = "Test Scan",
            description = "Test Description",
            createdAt = System.currentTimeMillis(),
            modifiedAt = System.currentTimeMillis(),
            scanDuration = 30000L,
            filePath = "/test/path/scan.obj",
            thumbnailPath = "/test/path/thumbnail.jpg",
            fileSize = 1024L,
            format = "OBJ",
            quality = "MEDIUM",
            vertexCount = 1000,
            triangleCount = 2000,
            hasTexture = false,
            hasColors = false,
            boundingBoxMinX = 0f,
            boundingBoxMinY = 0f,
            boundingBoxMinZ = 0f,
            boundingBoxMaxX = 1f,
            boundingBoxMaxY = 1f,
            boundingBoxMaxZ = 1f
        )
    }
}
