<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.auth.AuthenticationActivity">

    <!-- App Logo -->
    <ImageView
        android:id="@+id/iv_auth_logo"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="80dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_3d_scanner"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Auth Title -->
    <TextView
        android:id="@+id/tv_auth_title"
        style="@style/Scanner3D.Text.Title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/auth_title"
        android:textSize="24sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_auth_logo" />

    <!-- Auth Subtitle -->
    <TextView
        android:id="@+id/tv_auth_subtitle"
        style="@style/Scanner3D.Text.Body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="32dp"
        android:gravity="center"
        android:text="@string/auth_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_auth_title" />

    <!-- Authentication Buttons Container -->
    <LinearLayout
        android:id="@+id/ll_auth_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_auth_subtitle"
        app:layout_constraintVertical_bias="0.4">

        <!-- Biometric Authentication Button -->
        <Button
            android:id="@+id/btn_use_biometric"
            style="@style/Scanner3D.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/ic_fingerprint"
            android:drawablePadding="12dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/use_biometric"
            android:textAllCaps="false" />

        <!-- PIN Authentication Button -->
        <Button
            android:id="@+id/btn_use_pin"
            style="@style/Scanner3D.Button.Secondary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_pin"
            android:drawablePadding="12dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/use_pin"
            android:textAllCaps="false" />

    </LinearLayout>

    <!-- PIN Input Container -->
    <LinearLayout
        android:id="@+id/ll_pin_input"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_auth_subtitle"
        app:layout_constraintVertical_bias="0.4">

        <!-- PIN Label -->
        <TextView
            android:id="@+id/tv_pin_label"
            style="@style/Scanner3D.Text.Body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp"
            android:text="@string/enter_pin" />

        <!-- PIN Input Field -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:boxStrokeColor="@color/primary_blue"
            app:hintTextColor="@color/primary_blue">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_pin"
                android:inputType="numberPassword"
                android:maxLength="6"
                android:textAlignment="center"
                android:textSize="18sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Submit PIN Button -->
        <Button
            android:id="@+id/btn_submit_pin"
            style="@style/Scanner3D.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/ok"
            android:textAllCaps="false" />

    </LinearLayout>

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
