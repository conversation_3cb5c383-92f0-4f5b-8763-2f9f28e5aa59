package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.repository.AuthRepository
import kotlinx.coroutines.launch

class AuthViewModel(application: Application) : AndroidViewModel(application) {
    
    private val authRepository = AuthRepository(application)
    
    private val _authenticationResult = MutableLiveData<AuthResult>()
    val authenticationResult: LiveData<AuthResult> = _authenticationResult
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    enum class AuthResult {
        SUCCESS, FAILURE, ERROR
    }
    
    fun isBiometricAvailable(): Boolean {
        return authRepository.isBiometricAvailable()
    }
    
    fun isPinSet(): Boolean {
        return authRepository.isPinSet()
    }
    
    fun getAuthMethod(): String? {
        return authRepository.getAuthMethod()
    }
    
    fun authenticateWithBiometric(activity: FragmentActivity) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = authRepository.authenticateWithBiometric(activity)
                _authenticationResult.value = if (result) AuthResult.SUCCESS else AuthResult.FAILURE
            } catch (e: Exception) {
                _authenticationResult.value = AuthResult.ERROR
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun authenticateWithPin(pin: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = authRepository.authenticateWithPin(pin)
                _authenticationResult.value = if (result) AuthResult.SUCCESS else AuthResult.FAILURE
            } catch (e: Exception) {
                _authenticationResult.value = AuthResult.ERROR
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun setupPin(pin: String) {
        viewModelScope.launch {
            try {
                authRepository.setPinHash(pin)
                authRepository.setAuthMethod("pin")
                authRepository.setUserAuthenticated(true)
                _authenticationResult.value = AuthResult.SUCCESS
            } catch (e: Exception) {
                _authenticationResult.value = AuthResult.ERROR
            }
        }
    }
}

