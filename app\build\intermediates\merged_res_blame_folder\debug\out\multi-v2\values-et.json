{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3913,4008,4110,4208,4311,4417,4522,15218", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4003,4105,4203,4306,4412,4517,4637,15314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "74,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7439,8051,8151,8258", "endColumns": "99,99,106,98", "endOffsets": "7534,8146,8253,8352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6111", "endColumns": "139", "endOffsets": "6246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "14276,14385", "endColumns": "108,120", "endOffsets": "14380,14501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1051,1136,1204,1268,1355,1419,1483,1542,1614,1678,1732,1851,1911,1972,2026,2099,2232,2316,2409,2547,2627,2706,2832,2920,2999,3054,3105,3171,3244,3323,3409,3488,3561,3636,3710,3782,3895,3983,4060,4151,4243,4315,4389,4480,4534,4616,4685,4768,4854,4916,4980,5043,5111,5214,5317,5414,5515,5574,5629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "261,341,420,505,597,684,783,900,982,1046,1131,1199,1263,1350,1414,1478,1537,1609,1673,1727,1846,1906,1967,2021,2094,2227,2311,2404,2542,2622,2701,2827,2915,2994,3049,3100,3166,3239,3318,3404,3483,3556,3631,3705,3777,3890,3978,4055,4146,4238,4310,4384,4475,4529,4611,4680,4763,4849,4911,4975,5038,5106,5209,5312,5409,5510,5569,5624,5705"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3490,3570,3649,3734,3826,4642,4741,4858,7902,7966,9614,9851,9915,10002,10066,10130,10189,10261,10325,10379,10498,10558,10619,10673,10746,10879,10963,11056,11194,11274,11353,11479,11567,11646,11701,11752,11818,11891,11970,12056,12135,12208,12283,12357,12429,12542,12630,12707,12798,12890,12962,13036,13127,13181,13263,13332,13415,13501,13563,13627,13690,13758,13861,13964,14061,14162,14221,14815", "endLines": "5,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "311,3565,3644,3729,3821,3908,4736,4853,4935,7961,8046,9677,9910,9997,10061,10125,10184,10256,10320,10374,10493,10553,10614,10668,10741,10874,10958,11051,11189,11269,11348,11474,11562,11641,11696,11747,11813,11886,11965,12051,12130,12203,12278,12352,12424,12537,12625,12702,12793,12885,12957,13031,13122,13176,13258,13327,13410,13496,13558,13622,13685,13753,13856,13959,14056,14157,14216,14271,14891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1339", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1334,1456"}, "to": {"startLines": "53,54,75,77,78,95,96,155,156,157,158,160,161,163,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4940,5033,7539,7725,7820,9682,9760,14506,14597,14681,14749,14896,14978,15146,15319,15390", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,70,121", "endOffsets": "5028,5108,7630,7815,7897,9755,9846,14592,14676,14744,14810,14973,15058,15213,15385,15507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5113,5216,5378,5505,5613,5763,5892,6008,6251,6411,6519,6683,6815,6970,7115,7178,7243", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "5211,5373,5500,5608,5758,5887,6003,6106,6406,6514,6678,6810,6965,7110,7173,7238,7326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,253,371,500,629,758,889,1020,1119,1258,1393", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "158,248,366,495,624,753,884,1015,1114,1253,1388,1505"}, "to": {"startLines": "73,76,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7331,7635,8357,8475,8604,8733,8862,8993,9124,9223,9362,9497", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "7434,7720,8470,8599,8728,8857,8988,9119,9218,9357,9492,9609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,874,973,1084,1170,1272,1389,1470,1547,1639,1733,1829,1931,2040,2134,2235,2329,2421,2514,2597,2708,2812,2911,3021,3123,3222,3388,15063", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "869,968,1079,1165,1267,1384,1465,1542,1634,1728,1824,1926,2035,2129,2230,2324,2416,2509,2592,2703,2807,2906,3016,3118,3217,3383,3485,15141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,238,286,404,528", "endColumns": "47,47,117,123,93", "endOffsets": "237,285,403,527,621"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "316,368,420,542,670", "endColumns": "51,51,121,127,97", "endOffsets": "363,415,537,665,763"}}]}]}