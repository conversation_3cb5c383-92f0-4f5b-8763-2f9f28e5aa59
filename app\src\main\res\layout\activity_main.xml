<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".MainActivity">

    <!-- App Logo/Icon -->
    <ImageView
        android:id="@+id/iv_app_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="80dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_3d_scanner"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- App Title -->
    <TextView
        android:id="@+id/tv_app_title"
        style="@style/Scanner3D.Text.Headline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/main_title"
        android:textSize="32sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_app_logo" />

    <!-- Main Action Buttons Container -->
    <LinearLayout
        android:id="@+id/ll_main_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_title"
        app:layout_constraintVertical_bias="0.4">

        <!-- Start Scanning Button -->
        <Button
            android:id="@+id/btn_start_scanning"
            style="@style/Scanner3D.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/ic_camera"
            android:drawablePadding="12dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/start_scanning"
            android:textAllCaps="false" />

        <!-- View Gallery Button -->
        <Button
            android:id="@+id/btn_view_gallery"
            style="@style/Scanner3D.Button.Secondary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/ic_gallery"
            android:drawablePadding="12dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/view_gallery"
            android:textAllCaps="false" />

        <!-- Settings Button -->
        <Button
            android:id="@+id/btn_settings"
            style="@style/Scanner3D.Button.Secondary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_settings"
            android:drawablePadding="12dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/settings"
            android:textAllCaps="false" />

    </LinearLayout>

    <!-- Version Info -->
    <TextView
        android:id="@+id/tv_version"
        style="@style/Scanner3D.Text.Caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/version"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
