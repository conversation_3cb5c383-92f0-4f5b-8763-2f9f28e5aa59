package com.scanner3d.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ScanProgress(
    val isActive: Boolean = false,
    val isPaused: Boolean = false,
    val isProcessing: Boolean = false,
    val isComplete: Boolean = false,
    val hasError: Boolean = false,
    val progress: Float = 0f, // 0.0 to 1.0
    val processingStage: String = "",
    val errorMessage: String? = null,
    val frameCount: Int = 0,
    val estimatedTimeRemaining: Long = 0L // in milliseconds
) : Parcelable {
    
    val progressPercentage: Int
        get() = (progress * 100).toInt()
    
    val isInProgress: Boolean
        get() = isActive && !isComplete && !hasError
    
    val canPause: Boolean
        get() = isActive && !isProcessing && !isComplete
    
    val canResume: Boolean
        get() = isActive && isPaused && !isComplete
    
    val canStop: Boolean
        get() = isActive && !isComplete
}

