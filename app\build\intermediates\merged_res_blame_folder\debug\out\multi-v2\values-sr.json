{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-62:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1125,1217,1285,1348,1451,1511,1577,1633,1704,1764,1818,1930,1987,2048,2102,2178,2303,2389,2472,2610,2691,2774,2905,2993,3071,3125,3181,3247,3321,3399,3488,3570,3645,3721,3796,3867,3974,4064,4137,4229,4325,4397,4473,4569,4622,4704,4771,4858,4945,5007,5071,5134,5203,5308,5418,5514,5622,5680,5740", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "316,392,468,548,655,748,842,973,1054,1120,1212,1280,1343,1446,1506,1572,1628,1699,1759,1813,1925,1982,2043,2097,2173,2298,2384,2467,2605,2686,2769,2900,2988,3066,3120,3176,3242,3316,3394,3483,3565,3640,3716,3791,3862,3969,4059,4132,4224,4320,4392,4468,4564,4617,4699,4766,4853,4940,5002,5066,5129,5198,5303,5413,5509,5617,5675,5735,5815"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3527,3603,3679,3759,3866,4685,4779,4910,5195,5261,6629,6697,6760,6863,6923,6989,7045,7116,7176,7230,7342,7399,7460,7514,7590,7715,7801,7884,8022,8103,8186,8317,8405,8483,8537,8593,8659,8733,8811,8900,8982,9057,9133,9208,9279,9386,9476,9549,9641,9737,9809,9885,9981,10034,10116,10183,10270,10357,10419,10483,10546,10615,10720,10830,10926,11034,11092,11382", "endLines": "6,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "366,3598,3674,3754,3861,3954,4774,4905,4986,5256,5348,6692,6755,6858,6918,6984,7040,7111,7171,7225,7337,7394,7455,7509,7585,7710,7796,7879,8017,8098,8181,8312,8400,8478,8532,8588,8654,8728,8806,8895,8977,9052,9128,9203,9274,9381,9471,9544,9636,9732,9804,9880,9976,10029,10111,10178,10265,10352,10414,10478,10541,10610,10715,10825,10921,11029,11087,11147,11457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,287,399,513", "endColumns": "46,49,111,113,84", "endOffsets": "236,286,398,512,597"}, "to": {"startLines": "7,8,9,10,11", "startColumns": "4,4,4,4,4", "startOffsets": "371,422,476,592,710", "endColumns": "50,53,115,117,88", "endOffsets": "417,471,587,705,794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "44,45,46,47,48,49,50,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3959,4057,4159,4256,4360,4464,4569,11549", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4052,4154,4251,4355,4459,4564,4680,11645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1037,1137,1276,1409", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1032,1132,1271,1404,1530"}, "to": {"startLines": "54,55,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4991,5102,5353,5474,5608,5739,5866,5997,6131,6231,6370,6503", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "5097,5190,5469,5603,5734,5861,5992,6126,6226,6365,6498,6624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "799,906,1007,1113,1199,1303,1425,1509,1590,1681,1774,1869,1963,2063,2156,2251,2356,2447,2538,2624,2729,2835,2938,3044,3153,3260,3430,11462", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "901,1002,1108,1194,1298,1420,1504,1585,1676,1769,1864,1958,2058,2151,2246,2351,2442,2533,2619,2724,2830,2933,3039,3148,3255,3425,3522,11544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "11152,11259", "endColumns": "106,122", "endOffsets": "11254,11377"}}]}]}