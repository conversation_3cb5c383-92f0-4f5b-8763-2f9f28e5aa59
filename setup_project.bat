@echo off
echo ========================================
echo 3D Scanner Android App Setup
echo ========================================
echo.

echo Checking project structure...
if not exist "app\build.gradle" (
    echo ERROR: app\build.gradle not found!
    echo Please run this script from the 3dscanner project root directory.
    pause
    exit /b 1
)

echo ✓ Project structure looks good
echo.

echo Checking for Android SDK...
if not defined ANDROID_HOME (
    echo WARNING: ANDROID_HOME environment variable not set
    echo Please set it to your Android SDK path, for example:
    echo set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
)

echo Checking for Java...
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found in PATH
    echo Please install Java JDK 8 or higher
    pause
    exit /b 1
)
echo ✓ Java found

echo.
echo Checking for Gradle wrapper...
if not exist "gradlew.bat" (
    echo ERROR: Gradle wrapper not found
    echo Please run this from Android Studio or install Gradle manually
    pause
    exit /b 1
)

echo ✓ Gradle wrapper found
echo.

echo Creating local.properties file...
if not exist "local.properties" (
    if defined ANDROID_HOME (
        echo sdk.dir=%ANDROID_HOME% > local.properties
        echo ✓ Created local.properties with ANDROID_HOME
    ) else (
        echo sdk.dir=C:\Users\<USER>\AppData\Local\Android\Sdk > local.properties
        echo ✓ Created local.properties with default SDK path
        echo   Please verify the SDK path is correct
    )
) else (
    echo ✓ local.properties already exists
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open Android Studio
echo 2. Select "Open an existing project"
echo 3. Choose this folder: %CD%
echo 4. Let Android Studio sync the project
echo.
echo Or build from command line:
echo   gradlew.bat assembleDebug
echo.
echo See BUILD_SETUP_GUIDE.md for detailed instructions
echo.
pause
