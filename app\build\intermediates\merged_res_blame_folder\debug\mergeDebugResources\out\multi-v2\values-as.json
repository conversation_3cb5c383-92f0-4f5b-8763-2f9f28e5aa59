{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,10978", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,11074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "119,120", "startColumns": "4,4", "startOffsets": "10592,10697", "endColumns": "104,116", "endOffsets": "10692,10809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1048,1137,1202,1261,1347,1411,1475,1538,1608,1672,1726,1831,1889,1951,2005,2077,2194,2281,2364,2504,2581,2662,2789,2880,2957,3011,3062,3128,3198,3275,3362,3437,3508,3585,3654,3723,3830,3921,3993,4082,4171,4245,4317,4403,4453,4532,4598,4678,4762,4824,4888,4951,5020,5120,5215,5307,5399,5457,5512", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "267,349,427,504,590,674,776,899,978,1043,1132,1197,1256,1342,1406,1470,1533,1603,1667,1721,1826,1884,1946,2000,2072,2189,2276,2359,2499,2576,2657,2784,2875,2952,3006,3057,3123,3193,3270,3357,3432,3503,3580,3649,3718,3825,3916,3988,4077,4166,4240,4312,4398,4448,4527,4593,4673,4757,4819,4883,4946,5015,5115,5210,5302,5394,5452,5507,5588"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,4730,4795,6217,6282,6341,6427,6491,6555,6618,6688,6752,6806,6911,6969,7031,7085,7157,7274,7361,7444,7584,7661,7742,7869,7960,8037,8091,8142,8208,8278,8355,8442,8517,8588,8665,8734,8803,8910,9001,9073,9162,9251,9325,9397,9483,9533,9612,9678,9758,9842,9904,9968,10031,10100,10200,10295,10387,10479,10537,10814", "endLines": "5,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,121", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,4790,4879,6277,6336,6422,6486,6550,6613,6683,6747,6801,6906,6964,7026,7080,7152,7269,7356,7439,7579,7656,7737,7864,7955,8032,8086,8137,8203,8273,8350,8437,8512,8583,8660,8729,8798,8905,8996,9068,9157,9246,9320,9392,9478,9528,9607,9673,9753,9837,9899,9963,10026,10095,10195,10290,10382,10474,10532,10587,10890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,259,383,504,633,766,892,1064,1170,1310,1452", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "163,254,378,499,628,761,887,1059,1165,1305,1447,1587"}, "to": {"startLines": "48,49,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4526,4639,4884,5008,5129,5258,5391,5517,5689,5795,5935,6077", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "4634,4725,5003,5124,5253,5386,5512,5684,5790,5930,6072,6212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,10895", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,10973"}}]}]}