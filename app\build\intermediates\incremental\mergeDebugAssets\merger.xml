<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.gorisse.thomas.sceneform:core:1.23.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.12\transforms\2e32b9569d0aeb01079acf7ce75795c1\transformed\core-1.23.0\assets"><file name="environments/default_environment_ibl.ktx" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\2e32b9569d0aeb01079acf7ce75795c1\transformed\core-1.23.0\assets\environments\default_environment_ibl.ktx"/><file name="environments/default_environment_skybox.ktx" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\2e32b9569d0aeb01079acf7ce75795c1\transformed\core-1.23.0\assets\environments\default_environment_skybox.ktx"/></source></dataSet><dataSet config="io.github.sceneview:sceneview:0.10.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets"><file name="sceneview/materials/camera_stream_depth.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\camera_stream_depth.filamat"/><file name="sceneview/materials/camera_stream_flat.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\camera_stream_flat.filamat"/><file name="sceneview/materials/face_mesh.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\face_mesh.filamat"/><file name="sceneview/materials/face_mesh_occluder.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\face_mesh_occluder.filamat"/><file name="sceneview/materials/image_texture.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\image_texture.filamat"/><file name="sceneview/materials/opaque_colored.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\opaque_colored.filamat"/><file name="sceneview/materials/opaque_textured.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\opaque_textured.filamat"/><file name="sceneview/materials/plane_renderer.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\plane_renderer.filamat"/><file name="sceneview/materials/plane_renderer_shadow.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\plane_renderer_shadow.filamat"/><file name="sceneview/materials/transparent_colored.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\transparent_colored.filamat"/><file name="sceneview/materials/transparent_textured.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\transparent_textured.filamat"/><file name="sceneview/materials/video_stream_chroma_key.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\video_stream_chroma_key.filamat"/><file name="sceneview/materials/video_stream_plain.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\video_stream_plain.filamat"/><file name="sceneview/materials/view_renderable.filamat" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\materials\view_renderable.filamat"/><file name="sceneview/models/cursor.glb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\models\cursor.glb"/><file name="sceneview/models/node_selector.glb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\models\node_selector.glb"/><file name="sceneview/models/plane.glb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\models\plane.glb"/><file name="sceneview/textures/plane_renderer.png" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\1278c02e233dc393e8b4ded3c854a6b3\transformed\sceneview-0.10.0\assets\sceneview\textures\plane_renderer.png"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\debug\assets"/></dataSet></merger>