package com.scanner3d.app.utils

import android.app.ActivityManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.util.LruCache
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.max

class MemoryManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "MemoryManager"
        private const val MEMORY_CACHE_SIZE_RATIO = 0.125f // 1/8 of available memory
        private const val LOW_MEMORY_THRESHOLD = 0.85f
        private const val CRITICAL_MEMORY_THRESHOLD = 0.95f
        
        @Volatile
        private var INSTANCE: MemoryManager? = null
        
        fun getInstance(context: Context): MemoryManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MemoryManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val memoryInfo = ActivityManager.MemoryInfo()
    
    // Memory caches
    private val bitmapCache: LruCache<String, Bitmap>
    private val meshCache: LruCache<String, WeakReference<Any>>
    private val textureCache: LruCache<String, WeakReference<Bitmap>>
    
    // Memory monitoring
    private val memoryMonitoringJob = SupervisorJob()
    private val memoryMonitoringScope = CoroutineScope(Dispatchers.Default + memoryMonitoringJob)
    
    // Memory usage tracking
    private val allocatedObjects = ConcurrentHashMap<String, MemoryObject>()
    private var totalAllocatedMemory = 0L
    
    // Callbacks
    private val memoryWarningCallbacks = mutableListOf<() -> Unit>()
    private val lowMemoryCallbacks = mutableListOf<() -> Unit>()
    
    init {
        val maxMemory = Runtime.getRuntime().maxMemory()
        val cacheSize = (maxMemory * MEMORY_CACHE_SIZE_RATIO).toInt()
        
        bitmapCache = object : LruCache<String, Bitmap>(cacheSize) {
            override fun sizeOf(key: String, bitmap: Bitmap): Int {
                return bitmap.byteCount
            }
            
            override fun entryRemoved(evicted: Boolean, key: String, oldValue: Bitmap, newValue: Bitmap?) {
                if (evicted) {
                    Log.d(TAG, "Bitmap evicted from cache: $key")
                }
            }
        }
        
        meshCache = LruCache(50) // Max 50 mesh objects
        textureCache = LruCache(100) // Max 100 texture references
        
        startMemoryMonitoring()
    }
    
    fun getMemoryInfo(): MemoryInfo {
        activityManager.getMemoryInfo(memoryInfo)
        val runtime = Runtime.getRuntime()
        
        return MemoryInfo(
            totalMemory = memoryInfo.totalMem,
            availableMemory = memoryInfo.availMem,
            usedMemory = memoryInfo.totalMem - memoryInfo.availMem,
            maxHeapSize = runtime.maxMemory(),
            usedHeapSize = runtime.totalMemory() - runtime.freeMemory(),
            freeHeapSize = runtime.freeMemory(),
            isLowMemory = memoryInfo.lowMemory,
            threshold = memoryInfo.threshold,
            memoryUsagePercentage = ((memoryInfo.totalMem - memoryInfo.availMem).toFloat() / memoryInfo.totalMem.toFloat()) * 100f
        )
    }
    
    fun cacheBitmap(key: String, bitmap: Bitmap) {
        bitmapCache.put(key, bitmap)
        trackMemoryAllocation(key, bitmap.byteCount.toLong(), MemoryObjectType.BITMAP)
    }
    
    fun getCachedBitmap(key: String): Bitmap? {
        return bitmapCache.get(key)
    }
    
    fun removeBitmapFromCache(key: String) {
        bitmapCache.remove(key)
        untrackMemoryAllocation(key)
    }
    
    fun cacheMesh(key: String, mesh: Any) {
        meshCache.put(key, WeakReference(mesh))
        // Estimate mesh memory usage (rough calculation)
        val estimatedSize = estimateMeshSize(mesh)
        trackMemoryAllocation(key, estimatedSize, MemoryObjectType.MESH)
    }
    
    fun getCachedMesh(key: String): Any? {
        return meshCache.get(key)?.get()
    }
    
    fun cacheTexture(key: String, texture: Bitmap) {
        textureCache.put(key, WeakReference(texture))
        trackMemoryAllocation(key, texture.byteCount.toLong(), MemoryObjectType.TEXTURE)
    }
    
    fun getCachedTexture(key: String): Bitmap? {
        return textureCache.get(key)?.get()
    }
    
    fun clearAllCaches() {
        bitmapCache.evictAll()
        meshCache.evictAll()
        textureCache.evictAll()
        allocatedObjects.clear()
        totalAllocatedMemory = 0L
        Log.d(TAG, "All caches cleared")
    }
    
    fun clearBitmapCache() {
        bitmapCache.evictAll()
        Log.d(TAG, "Bitmap cache cleared")
    }
    
    fun performGarbageCollection() {
        System.gc()
        Log.d(TAG, "Garbage collection performed")
    }
    
    fun optimizeMemoryUsage() {
        val memInfo = getMemoryInfo()
        
        if (memInfo.memoryUsagePercentage > LOW_MEMORY_THRESHOLD * 100) {
            Log.w(TAG, "High memory usage detected (${memInfo.memoryUsagePercentage}%), optimizing...")
            
            // Clear least recently used items
            bitmapCache.trimToSize(bitmapCache.size() / 2)
            
            // Remove weak references that have been garbage collected
            cleanupWeakReferences()
            
            // Perform garbage collection
            performGarbageCollection()
            
            // Notify callbacks
            memoryWarningCallbacks.forEach { it.invoke() }
        }
        
        if (memInfo.memoryUsagePercentage > CRITICAL_MEMORY_THRESHOLD * 100) {
            Log.e(TAG, "Critical memory usage detected (${memInfo.memoryUsagePercentage}%), emergency cleanup...")
            
            // Emergency cleanup
            clearAllCaches()
            performGarbageCollection()
            
            // Notify low memory callbacks
            lowMemoryCallbacks.forEach { it.invoke() }
        }
    }
    
    fun addMemoryWarningCallback(callback: () -> Unit) {
        memoryWarningCallbacks.add(callback)
    }
    
    fun addLowMemoryCallback(callback: () -> Unit) {
        lowMemoryCallbacks.add(callback)
    }
    
    fun removeMemoryWarningCallback(callback: () -> Unit) {
        memoryWarningCallbacks.remove(callback)
    }
    
    fun removeLowMemoryCallback(callback: () -> Unit) {
        lowMemoryCallbacks.remove(callback)
    }
    
    private fun startMemoryMonitoring() {
        memoryMonitoringScope.launch {
            while (isActive) {
                try {
                    optimizeMemoryUsage()
                    delay(5000) // Check every 5 seconds
                } catch (e: Exception) {
                    Log.e(TAG, "Error in memory monitoring", e)
                }
            }
        }
    }
    
    private fun trackMemoryAllocation(key: String, size: Long, type: MemoryObjectType) {
        allocatedObjects[key] = MemoryObject(key, size, type, System.currentTimeMillis())
        totalAllocatedMemory += size
    }
    
    private fun untrackMemoryAllocation(key: String) {
        allocatedObjects.remove(key)?.let { obj ->
            totalAllocatedMemory -= obj.size
        }
    }
    
    private fun cleanupWeakReferences() {
        val iterator = meshCache.snapshot().iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.get() == null) {
                meshCache.remove(entry.key)
                untrackMemoryAllocation(entry.key)
            }
        }
        
        val textureIterator = textureCache.snapshot().iterator()
        while (textureIterator.hasNext()) {
            val entry = textureIterator.next()
            if (entry.value.get() == null) {
                textureCache.remove(entry.key)
                untrackMemoryAllocation(entry.key)
            }
        }
    }
    
    private fun estimateMeshSize(mesh: Any): Long {
        // Rough estimation based on object type
        return when (mesh) {
            is com.scanner3d.app.data.model.Mesh3D -> {
                val vertexSize = mesh.vertices.size * 4L // 4 bytes per float
                val indexSize = mesh.indices.size * 4L // 4 bytes per int
                val normalSize = (mesh.normals?.size ?: 0) * 4L
                val uvSize = (mesh.textureCoordinates?.size ?: 0) * 4L
                vertexSize + indexSize + normalSize + uvSize
            }
            else -> 1024L // Default 1KB estimate
        }
    }
    
    fun getMemoryUsageReport(): MemoryUsageReport {
        val memInfo = getMemoryInfo()
        val cacheStats = CacheStats(
            bitmapCacheSize = bitmapCache.size(),
            bitmapCacheHitCount = bitmapCache.hitCount(),
            bitmapCacheMissCount = bitmapCache.missCount(),
            meshCacheSize = meshCache.size(),
            textureCacheSize = textureCache.size()
        )
        
        val objectsByType = allocatedObjects.values.groupBy { it.type }
        val typeStats = objectsByType.map { (type, objects) ->
            TypeStats(
                type = type,
                count = objects.size,
                totalSize = objects.sumOf { it.size }
            )
        }
        
        return MemoryUsageReport(
            memoryInfo = memInfo,
            cacheStats = cacheStats,
            totalTrackedMemory = totalAllocatedMemory,
            typeStats = typeStats,
            oldestAllocation = allocatedObjects.values.minByOrNull { it.timestamp }?.timestamp ?: 0L
        )
    }
    
    fun cleanup() {
        memoryMonitoringJob.cancel()
        clearAllCaches()
        memoryWarningCallbacks.clear()
        lowMemoryCallbacks.clear()
    }
    
    // Data classes
    data class MemoryInfo(
        val totalMemory: Long,
        val availableMemory: Long,
        val usedMemory: Long,
        val maxHeapSize: Long,
        val usedHeapSize: Long,
        val freeHeapSize: Long,
        val isLowMemory: Boolean,
        val threshold: Long,
        val memoryUsagePercentage: Float
    )
    
    data class CacheStats(
        val bitmapCacheSize: Int,
        val bitmapCacheHitCount: Long,
        val bitmapCacheMissCount: Long,
        val meshCacheSize: Int,
        val textureCacheSize: Int
    ) {
        val bitmapCacheHitRate: Float
            get() = if (bitmapCacheHitCount + bitmapCacheMissCount > 0) {
                bitmapCacheHitCount.toFloat() / (bitmapCacheHitCount + bitmapCacheMissCount).toFloat()
            } else 0f
    }
    
    data class TypeStats(
        val type: MemoryObjectType,
        val count: Int,
        val totalSize: Long
    )
    
    data class MemoryUsageReport(
        val memoryInfo: MemoryInfo,
        val cacheStats: CacheStats,
        val totalTrackedMemory: Long,
        val typeStats: List<TypeStats>,
        val oldestAllocation: Long
    )
    
    private data class MemoryObject(
        val key: String,
        val size: Long,
        val type: MemoryObjectType,
        val timestamp: Long
    )
    
    enum class MemoryObjectType {
        BITMAP, MESH, TEXTURE, POINT_CLOUD, OTHER
    }
}
