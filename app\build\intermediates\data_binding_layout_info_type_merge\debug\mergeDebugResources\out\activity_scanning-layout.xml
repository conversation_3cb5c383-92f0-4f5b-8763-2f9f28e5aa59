<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_scanning" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\activity_scanning.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_scanning_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="328" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="36"/></Target><Target id="@+id/depth_view" view="com.scanner3d.app.ui.custom.DepthView"><Expressions/><location startLine="21" startOffset="4" endLine="29" endOffset="63"/></Target><Target id="@+id/point_cloud_view" view="com.scanner3d.app.ui.custom.PointCloudView"><Expressions/><location startLine="32" startOffset="4" endLine="40" endOffset="63"/></Target><Target id="@+id/ll_top_bar" view="LinearLayout"><Expressions/><location startLine="43" startOffset="4" endLine="119" endOffset="18"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="56" startOffset="8" endLine="63" endOffset="41"/></Target><Target id="@+id/tv_scan_title" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="80" endOffset="42"/></Target><Target id="@+id/indicator_scanning" view="View"><Expressions/><location startLine="88" startOffset="16" endLine="95" endOffset="47"/></Target><Target id="@+id/tv_scan_status" view="TextView"><Expressions/><location startLine="97" startOffset="16" endLine="103" endOffset="45"/></Target><Target id="@+id/btn_scan_settings" view="ImageButton"><Expressions/><location startLine="110" startOffset="8" endLine="117" endOffset="41"/></Target><Target id="@+id/ll_view_toggle" view="LinearLayout"><Expressions/><location startLine="122" startOffset="4" endLine="154" endOffset="18"/></Target><Target id="@+id/btn_camera_view" view="Button"><Expressions/><location startLine="133" startOffset="8" endLine="142" endOffset="37"/></Target><Target id="@+id/btn_depth_view" view="Button"><Expressions/><location startLine="144" startOffset="8" endLine="152" endOffset="37"/></Target><Target id="@+id/ll_scan_info" view="LinearLayout"><Expressions/><location startLine="157" startOffset="4" endLine="203" endOffset="18"/></Target><Target id="@+id/tv_scan_progress" view="TextView"><Expressions/><location startLine="168" startOffset="8" endLine="175" endOffset="38"/></Target><Target id="@+id/tv_frame_count" view="TextView"><Expressions/><location startLine="177" startOffset="8" endLine="184" endOffset="37"/></Target><Target id="@+id/tv_point_count" view="TextView"><Expressions/><location startLine="186" startOffset="8" endLine="192" endOffset="37"/></Target><Target id="@+id/tv_time_remaining" view="TextView"><Expressions/><location startLine="194" startOffset="8" endLine="201" endOffset="39"/></Target><Target id="@+id/ll_processing_status" view="LinearLayout"><Expressions/><location startLine="206" startOffset="4" endLine="237" endOffset="18"/></Target><Target id="@+id/tv_processing_stage" view="TextView"><Expressions/><location startLine="219" startOffset="8" endLine="226" endOffset="38"/></Target><Target id="@+id/progress_bar_processing" view="ProgressBar"><Expressions/><location startLine="228" startOffset="8" endLine="235" endOffset="56"/></Target><Target id="@+id/ll_scan_controls" view="LinearLayout"><Expressions/><location startLine="240" startOffset="4" endLine="326" endOffset="18"/></Target><Target id="@+id/progress_bar_scan" view="ProgressBar"><Expressions/><location startLine="253" startOffset="8" endLine="261" endOffset="57"/></Target><Target id="@+id/btn_start_scan" view="Button"><Expressions/><location startLine="271" startOffset="12" endLine="281" endOffset="41"/></Target><Target id="@+id/btn_pause_scan" view="Button"><Expressions/><location startLine="284" startOffset="12" endLine="295" endOffset="43"/></Target><Target id="@+id/btn_resume_scan" view="Button"><Expressions/><location startLine="298" startOffset="12" endLine="309" endOffset="43"/></Target><Target id="@+id/btn_stop_scan" view="Button"><Expressions/><location startLine="312" startOffset="12" endLine="322" endOffset="41"/></Target></Targets></Layout>