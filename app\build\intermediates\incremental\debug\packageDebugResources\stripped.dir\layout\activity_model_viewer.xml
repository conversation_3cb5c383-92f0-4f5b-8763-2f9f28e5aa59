<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    tools:context=".ui.model.ModelViewerActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 3D Model Viewer -->
        <com.scanner3d.app.ui.custom.PointCloudView
            android:id="@+id/pointCloudView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/infoPanel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/pointCloudView"
            app:layout_constraintBottom_toBottomOf="@+id/pointCloudView"
            app:layout_constraintStart_toStartOf="@+id/pointCloudView"
            app:layout_constraintEnd_toEndOf="@+id/pointCloudView" />

        <!-- Control Buttons -->
        <LinearLayout
            android:id="@+id/controlButtons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:padding="8dp"
            app:layout_constraintTop_toTopOf="@+id/pointCloudView"
            app:layout_constraintEnd_toEndOf="@+id/pointCloudView">

            <ImageButton
                android:id="@+id/btnResetView"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_refresh"
                android:contentDescription="Reset View"
                android:tint="@color/white" />

            <ImageButton
                android:id="@+id/btnToggleWireframe"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_wireframe"
                android:contentDescription="Toggle Wireframe"
                android:tint="@color/white" />

            <ImageButton
                android:id="@+id/btnToggleTexture"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_texture"
                android:contentDescription="Toggle Texture"
                android:tint="@color/white" />

        </LinearLayout>

        <!-- Info Panel -->
        <LinearLayout
            android:id="@+id/infoPanel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/surface"
            android:padding="16dp"
            android:elevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Scan Name and Date -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/tvScanName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Scan Name"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <View
                    android:id="@+id/qualityIndicator"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/circle_background_white" />

                <ImageView
                    android:id="@+id/iconTexture"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_texture"
                    android:contentDescription="Has Texture"
                    android:tint="@color/success_green"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/iconColors"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="4dp"
                    android:src="@drawable/ic_palette"
                    android:contentDescription="Has Colors"
                    android:tint="@color/success_green"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvScanDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Date"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="12dp" />

            <!-- Stats Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvVertexCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 vertices"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Vertices"
                        android:textSize="10sp"
                        android:textColor="@color/text_hint" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvTriangleCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 triangles"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Triangles"
                        android:textSize="10sp"
                        android:textColor="@color/text_hint" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvFileSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 MB"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Size"
                        android:textSize="10sp"
                        android:textColor="@color/text_hint" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvQuality"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="High"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Quality"
                        android:textSize="10sp"
                        android:textColor="@color/text_hint" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Floating Action Buttons -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/ic_info"
        android:contentDescription="Scan Information"
        app:layout_anchor="@id/infoPanel"
        app:layout_anchorGravity="top|start"
        app:backgroundTint="@color/primary_blue"
        app:tint="@color/white" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabShare"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/ic_share"
        android:contentDescription="Share Scan"
        app:layout_anchor="@id/infoPanel"
        app:layout_anchorGravity="top|end"
        app:backgroundTint="@color/accent_green"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
