{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,896,999,1108,1192,1297,1416,1494,1569,1661,1755,1848,1942,2043,2137,2234,2329,2421,2513,2594,2700,2807,2905,3009,3115,3222,3385,11638", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "891,994,1103,1187,1292,1411,1489,1564,1656,1750,1843,1937,2038,2132,2229,2324,2416,2508,2589,2695,2802,2900,3004,3110,3217,3380,3480,11715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3921,4019,4121,4220,4322,4431,4538,11720", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "4014,4116,4215,4317,4426,4533,4663,11816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,238,289,419,553", "endColumns": "47,50,129,133,89", "endOffsets": "237,288,418,552,642"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "318,370,425,559,697", "endColumns": "51,54,133,137,93", "endOffsets": "365,420,554,692,786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,122", "endOffsets": "159,282"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11326,11435", "endColumns": "108,122", "endOffsets": "11430,11553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2924,3015,3105,3160,3211,3277,3350,3430,3521,3601,3676,3753,3822,3899,4004,4092,4181,4274,4367,4441,4521,4615,4666,4750,4816,4900,4988,5050,5114,5177,5245,5360,5474,5580,5689,5748,5803", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2919,3010,3100,3155,3206,3272,3345,3425,3516,3596,3671,3748,3817,3894,3999,4087,4176,4269,4362,4436,4516,4610,4661,4745,4811,4895,4983,5045,5109,5172,5240,5355,5469,5575,5684,5743,5798,5878"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3485,3569,3650,3727,3826,4668,4767,4907,5196,5262,6710,6795,6857,6945,7007,7076,7139,7212,7275,7329,7450,7507,7569,7623,7700,7837,7922,8004,8139,8220,8301,8447,8538,8628,8683,8734,8800,8873,8953,9044,9124,9199,9276,9345,9422,9527,9615,9704,9797,9890,9964,10044,10138,10189,10273,10339,10423,10511,10573,10637,10700,10768,10883,10997,11103,11212,11271,11558", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "313,3564,3645,3722,3821,3916,4762,4902,4985,5257,5352,6790,6852,6940,7002,7071,7134,7207,7270,7324,7445,7502,7564,7618,7695,7832,7917,7999,8134,8215,8296,8442,8533,8623,8678,8729,8795,8868,8948,9039,9119,9194,9271,9340,9417,9522,9610,9699,9792,9885,9959,10039,10133,10184,10268,10334,10418,10506,10568,10632,10695,10763,10878,10992,11098,11207,11266,11321,11633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,374,509,655,811,941,1099,1201,1338,1489", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "161,256,369,504,650,806,936,1094,1196,1333,1484,1609"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4990,5101,5357,5470,5605,5751,5907,6037,6195,6297,6434,6585", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "5096,5191,5465,5600,5746,5902,6032,6190,6292,6429,6580,6705"}}]}]}