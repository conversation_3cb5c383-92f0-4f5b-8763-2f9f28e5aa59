{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-v28/values-v28.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,397", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,392,584"}, "to": {"startLines": "14,15,16,20", "startColumns": "4,4,4,4", "startOffsets": "1142,1217,1304,1484", "endLines": "14,15,19,23", "endColumns": "74,86,12,12", "endOffsets": "1212,1299,1479,1671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,344,442,530,618,706,794,881,968,1055,1142", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,17", "endColumns": "97,95,94,97,87,87,87,87,86,86,86,86,10", "endOffsets": "148,244,339,437,525,613,701,789,876,963,1050,1137,1424"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,344,442,530,618,706,794,881,968,1055,1676", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,27", "endColumns": "97,95,94,97,87,87,87,87,86,86,86,86,10", "endOffsets": "148,244,339,437,525,613,701,789,876,963,1050,1137,1958"}}]}]}