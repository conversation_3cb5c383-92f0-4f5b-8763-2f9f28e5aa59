package com.scanner3d.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "settings")
@Parcelize
data class AppSettings(
    @PrimaryKey
    val id: Int = 1, // Single row for app settings
    
    // Scanning settings
    val defaultScanQuality: String = "HIGH", // LOW, MEDIUM, HIGH, ULTRA
    val autoSaveScans: Boolean = true,
    val maxScanDuration: Long = 300000L, // 5 minutes in milliseconds
    val frameRate: Int = 30,
    val enableDepthFiltering: Boolean = true,
    val enableMeshSmoothing: Boolean = true,
    
    // Export settings
    val defaultExportFormat: String = "OBJ", // OBJ, STL, PLY, GLTF
    val enableCompression: Boolean = true,
    val compressionLevel: Int = 5, // 1-10
    
    // Cloud settings
    val enableCloudSync: Boolean = false,
    val autoUpload: Boolean = false,
    val wifiOnlyUpload: Boolean = true,
    val cloudStorageLimit: Long = 1073741824L, // 1GB in bytes
    
    // UI settings
    val theme: String = "SYSTEM", // LIGHT, DARK, SYSTEM
    val language: String = "en",
    val showTutorial: Boolean = true,
    val enableHapticFeedback: Boolean = true,
    val enableSoundEffects: Boolean = true,
    
    // Performance settings
    val maxMemoryUsage: Long = 2147483648L, // 2GB in bytes
    val cacheSize: Long = 524288000L, // 500MB in bytes
    val enableGpuAcceleration: Boolean = true,
    val renderQuality: String = "HIGH", // LOW, MEDIUM, HIGH
    
    // Privacy settings
    val enableAnalytics: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val dataRetentionDays: Int = 30,
    
    // Advanced settings
    val debugMode: Boolean = false,
    val enableExperimentalFeatures: Boolean = false,
    val logLevel: String = "INFO", // DEBUG, INFO, WARN, ERROR
    
    val lastModified: Long = System.currentTimeMillis()
) : Parcelable {
    
    val scanQuality: Mesh3D.MeshQuality
        get() = when (defaultScanQuality) {
            "LOW" -> Mesh3D.MeshQuality.LOW
            "MEDIUM" -> Mesh3D.MeshQuality.MEDIUM
            "HIGH" -> Mesh3D.MeshQuality.HIGH
            "ULTRA" -> Mesh3D.MeshQuality.ULTRA
            else -> Mesh3D.MeshQuality.HIGH
        }
    
    val maxMemoryUsageFormatted: String
        get() = "${maxMemoryUsage / (1024 * 1024 * 1024)} GB"
    
    val cacheSizeFormatted: String
        get() = "${cacheSize / (1024 * 1024)} MB"
    
    val cloudStorageLimitFormatted: String
        get() = "${cloudStorageLimit / (1024 * 1024 * 1024)} GB"
}
