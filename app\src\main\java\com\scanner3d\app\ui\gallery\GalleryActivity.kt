package com.scanner3d.app.ui.gallery

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.scanner3d.app.R
import com.scanner3d.app.databinding.ActivityGalleryBinding
import com.scanner3d.app.ui.model.ModelViewerActivity
import com.scanner3d.app.viewmodel.GalleryViewModel

class GalleryActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityGalleryBinding
    private val viewModel: GalleryViewModel by viewModels()
    private lateinit var galleryAdapter: GalleryAdapter
    
    private var isGridView = true
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGalleryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupUI()
        observeViewModel()
        
        viewModel.loadScans()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "Gallery"
        }
    }
    
    private fun setupRecyclerView() {
        galleryAdapter = GalleryAdapter(
            onItemClick = { scan ->
                openModelViewer(scan.id)
            },
            onItemLongClick = { scan ->
                showScanOptions(scan)
            }
        )
        
        binding.recyclerViewScans.apply {
            adapter = galleryAdapter
            layoutManager = GridLayoutManager(this@GalleryActivity, 2)
        }
    }
    
    private fun setupUI() {
        binding.apply {
            fabNewScan.setOnClickListener {
                // Navigate back to main activity to start new scan
                finish()
            }
            
            swipeRefreshLayout.setOnRefreshListener {
                viewModel.refreshScans()
            }
            
            // Search functionality
            searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    query?.let { viewModel.searchScans(it) }
                    return true
                }
                
                override fun onQueryTextChange(newText: String?): Boolean {
                    if (newText.isNullOrEmpty()) {
                        viewModel.loadScans()
                    } else {
                        viewModel.searchScans(newText)
                    }
                    return true
                }
            })
        }
    }
    
    private fun observeViewModel() {
        viewModel.scans.observe(this, Observer { scans ->
            galleryAdapter.submitList(scans)
            binding.swipeRefreshLayout.isRefreshing = false
            
            // Show/hide empty state
            if (scans.isEmpty()) {
                binding.tvEmptyState.visibility = android.view.View.VISIBLE
                binding.recyclerViewScans.visibility = android.view.View.GONE
            } else {
                binding.tvEmptyState.visibility = android.view.View.GONE
                binding.recyclerViewScans.visibility = android.view.View.VISIBLE
            }
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        })
        
        viewModel.errorMessage.observe(this, Observer { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                viewModel.clearError()
            }
        })
        
        viewModel.scanStats.observe(this, Observer { stats ->
            updateStatsDisplay(stats)
        })
    }
    
    private fun updateStatsDisplay(stats: GalleryViewModel.ScanStats) {
        binding.apply {
            tvScanCount.text = "Scans: ${stats.totalScans}"
            tvTotalSize.text = "Size: ${formatFileSize(stats.totalSize)}"
        }
    }
    
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    private fun openModelViewer(scanId: String) {
        val intent = Intent(this, ModelViewerActivity::class.java)
        intent.putExtra("scan_id", scanId)
        startActivity(intent)
    }
    
    private fun showScanOptions(scan: com.scanner3d.app.data.model.ScanEntity) {
        val options = arrayOf("View", "Share", "Export", "Rename", "Delete")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(scan.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> openModelViewer(scan.id)
                    1 -> shareScan(scan)
                    2 -> exportScan(scan)
                    3 -> renameScan(scan)
                    4 -> deleteScan(scan)
                }
            }
            .show()
    }
    
    private fun shareScan(scan: com.scanner3d.app.data.model.ScanEntity) {
        // TODO: Implement sharing functionality
        Toast.makeText(this, "Share functionality coming soon", Toast.LENGTH_SHORT).show()
    }
    
    private fun exportScan(scan: com.scanner3d.app.data.model.ScanEntity) {
        // TODO: Implement export functionality
        Toast.makeText(this, "Export functionality coming soon", Toast.LENGTH_SHORT).show()
    }
    
    private fun renameScan(scan: com.scanner3d.app.data.model.ScanEntity) {
        val editText = android.widget.EditText(this)
        editText.setText(scan.name)
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Rename Scan")
            .setView(editText)
            .setPositiveButton("Rename") { _, _ ->
                val newName = editText.text.toString().trim()
                if (newName.isNotEmpty()) {
                    viewModel.renameScan(scan.id, newName)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun deleteScan(scan: com.scanner3d.app.data.model.ScanEntity) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Delete Scan")
            .setMessage("Are you sure you want to delete '${scan.name}'? This action cannot be undone.")
            .setPositiveButton("Delete") { _, _ ->
                viewModel.deleteScan(scan.id)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.gallery_menu, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_view_toggle -> {
                toggleViewMode()
                true
            }
            R.id.action_sort -> {
                showSortOptions()
                true
            }
            R.id.action_filter -> {
                showFilterOptions()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun toggleViewMode() {
        isGridView = !isGridView
        binding.recyclerViewScans.layoutManager = if (isGridView) {
            GridLayoutManager(this, 2)
        } else {
            LinearLayoutManager(this)
        }
        galleryAdapter.setViewMode(if (isGridView) GalleryAdapter.ViewMode.GRID else GalleryAdapter.ViewMode.LIST)
    }
    
    private fun showSortOptions() {
        val sortOptions = arrayOf("Date Created", "Name", "Size", "Quality")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Sort by")
            .setItems(sortOptions) { _, which ->
                val sortBy = when (which) {
                    0 -> GalleryViewModel.SortBy.DATE_CREATED
                    1 -> GalleryViewModel.SortBy.NAME
                    2 -> GalleryViewModel.SortBy.SIZE
                    3 -> GalleryViewModel.SortBy.QUALITY
                    else -> GalleryViewModel.SortBy.DATE_CREATED
                }
                viewModel.setSortBy(sortBy)
            }
            .show()
    }
    
    private fun showFilterOptions() {
        val filterOptions = arrayOf("All", "High Quality", "With Texture", "Recent")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Filter")
            .setItems(filterOptions) { _, which ->
                val filter = when (which) {
                    0 -> GalleryViewModel.FilterBy.ALL
                    1 -> GalleryViewModel.FilterBy.HIGH_QUALITY
                    2 -> GalleryViewModel.FilterBy.WITH_TEXTURE
                    3 -> GalleryViewModel.FilterBy.RECENT
                    else -> GalleryViewModel.FilterBy.ALL
                }
                viewModel.setFilterBy(filter)
            }
            .show()
    }
}
