{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4000,4099,4201,4303,4406,4507,4609,11583", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "4094,4196,4298,4401,4502,4604,4724,11679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,261,382,521,655,784,908,1055,1158,1295,1440", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "167,256,377,516,650,779,903,1050,1153,1290,1435,1569"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5031,5148,5394,5515,5654,5788,5917,6041,6188,6291,6428,6573", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "5143,5232,5510,5649,5783,5912,6036,6183,6286,6423,6568,6702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,241,293,429,562", "endColumns": "50,51,135,132,89", "endOffsets": "240,292,428,561,651"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "321,376,432,572,709", "endColumns": "54,55,139,136,93", "endOffsets": "371,427,567,704,798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\857ae526a5ee6c76b63616ddc978cbae\\transformed\\navigation-ui-2.7.5\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11187,11299", "endColumns": "111,116", "endOffsets": "11294,11411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1106,1197,1263,1324,1414,1478,1545,1606,1675,1737,1791,1898,1957,2018,2072,2146,2266,2351,2435,2570,2641,2711,2843,2930,3013,3071,3127,3193,3266,3346,3441,3523,3592,3668,3748,3817,3926,4021,4104,4194,4289,4363,4437,4530,4584,4669,4736,4822,4907,4969,5033,5096,5162,5264,5363,5456,5555,5617,5677", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "266,364,457,540,641,733,837,954,1035,1101,1192,1258,1319,1409,1473,1540,1601,1670,1732,1786,1893,1952,2013,2067,2141,2261,2346,2430,2565,2636,2706,2838,2925,3008,3066,3122,3188,3261,3341,3436,3518,3587,3663,3743,3812,3921,4016,4099,4189,4284,4358,4432,4525,4579,4664,4731,4817,4902,4964,5028,5091,5157,5259,5358,5451,5550,5612,5672,5752"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3533,3631,3724,3807,3908,4729,4833,4950,5237,5303,6707,6773,6834,6924,6988,7055,7116,7185,7247,7301,7408,7467,7528,7582,7656,7776,7861,7945,8080,8151,8221,8353,8440,8523,8581,8637,8703,8776,8856,8951,9033,9102,9178,9258,9327,9436,9531,9614,9704,9799,9873,9947,10040,10094,10179,10246,10332,10417,10479,10543,10606,10672,10774,10873,10966,11065,11127,11416", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "316,3626,3719,3802,3903,3995,4828,4945,5026,5298,5389,6768,6829,6919,6983,7050,7111,7180,7242,7296,7403,7462,7523,7577,7651,7771,7856,7940,8075,8146,8216,8348,8435,8518,8576,8632,8698,8771,8851,8946,9028,9097,9173,9253,9322,9431,9526,9609,9699,9794,9868,9942,10035,10089,10174,10241,10327,10412,10474,10538,10601,10667,10769,10868,10961,11060,11122,11182,11491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "803,911,1017,1123,1212,1317,1438,1521,1603,1694,1787,1881,1975,2075,2168,2263,2357,2448,2539,2625,2735,2839,2942,3050,3158,3263,3428,11496", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "906,1012,1118,1207,1312,1433,1516,1598,1689,1782,1876,1970,2070,2163,2258,2352,2443,2534,2620,2730,2834,2937,3045,3153,3258,3423,3528,11578"}}]}]}