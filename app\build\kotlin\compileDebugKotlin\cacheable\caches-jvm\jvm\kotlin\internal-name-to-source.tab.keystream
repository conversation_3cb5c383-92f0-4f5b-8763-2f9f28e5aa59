com/scanner3d/app/MainActivityDcom/scanner3d/app/MainActivity$special$$inlined$viewModels$default$1Dcom/scanner3d/app/MainActivity$special$$inlined$viewModels$default$2Dcom/scanner3d/app/MainActivity$special$$inlined$viewModels$default$3%com/scanner3d/app/core/ScanningEngine6com/scanner3d/app/core/ScanningEngine$processCapture$1/com/scanner3d/app/core/ScanningEngine$Companion3com/scanner3d/app/core/ScanningEngine$CapturedFrame'com/scanner3d/app/data/database/ScanDao4com/scanner3d/app/data/database/ScanDao$DefaultImpls1com/scanner3d/app/data/database/Scanner3DDatabaseKcom/scanner3d/app/data/database/Scanner3DDatabase$Companion$MIGRATION_1_2$1;com/scanner3d/app/data/database/Scanner3DDatabase$CompanionLcom/scanner3d/app/data/database/Scanner3DDatabase$Companion$DatabaseCallback+com/scanner3d/app/data/database/SettingsDao(com/scanner3d/app/data/model/AppSettings0com/scanner3d/app/data/model/AppSettings$Creator#com/scanner3d/app/data/model/Mesh3D+com/scanner3d/app/data/model/Mesh3D$Creator/com/scanner3d/app/data/model/Mesh3D$BoundingBox7com/scanner3d/app/data/model/Mesh3D$BoundingBox$Creator0com/scanner3d/app/data/model/Mesh3D$MeshMetadata8com/scanner3d/app/data/model/Mesh3D$MeshMetadata$Creator/com/scanner3d/app/data/model/Mesh3D$MeshQuality+com/scanner3d/app/data/model/PointCloudData3com/scanner3d/app/data/model/PointCloudData$Creator'com/scanner3d/app/data/model/ScanEntity/com/scanner3d/app/data/model/ScanEntity$Creator)com/scanner3d/app/data/model/ScanProgress1com/scanner3d/app/data/model/ScanProgress$Creator+com/scanner3d/app/repository/AuthRepositoryYcom/scanner3d/app/repository/AuthRepository$authenticateWithBiometric$2$biometricPrompt$1Icom/scanner3d/app/repository/AuthRepository$authenticateWithBiometric$2$15com/scanner3d/app/repository/AuthRepository$Companion3com/scanner3d/app/repository/CloudStorageRepository@com/scanner3d/app/repository/CloudStorageRepository$uploadScan$2@com/scanner3d/app/repository/CloudStorageRepository$uploadScan$1Bcom/scanner3d/app/repository/CloudStorageRepository$downloadScan$2Bcom/scanner3d/app/repository/CloudStorageRepository$downloadScan$1?com/scanner3d/app/repository/CloudStorageRepository$syncScans$2?com/scanner3d/app/repository/CloudStorageRepository$syncScans$1Icom/scanner3d/app/repository/CloudStorageRepository$deleteScanFromCloud$2Icom/scanner3d/app/repository/CloudStorageRepository$deleteScanFromCloud$1Gcom/scanner3d/app/repository/CloudStorageRepository$signInAnonymously$2Gcom/scanner3d/app/repository/CloudStorageRepository$signInAnonymously$1Jcom/scanner3d/app/repository/CloudStorageRepository$getCloudStorageUsage$2Jcom/scanner3d/app/repository/CloudStorageRepository$getCloudStorageUsage$1=com/scanner3d/app/repository/CloudStorageRepository$CompanionEcom/scanner3d/app/repository/CloudStorageRepository$CloudStorageUsage+com/scanner3d/app/repository/ScanRepository6com/scanner3d/app/repository/ScanRepository$saveScan$26com/scanner3d/app/repository/ScanRepository$saveScan$18com/scanner3d/app/repository/ScanRepository$deleteScan$28com/scanner3d/app/repository/ScanRepository$deleteScan$1@com/scanner3d/app/repository/ScanRepository$updateScanMetadata$2@com/scanner3d/app/repository/ScanRepository$updateScanMetadata$18com/scanner3d/app/repository/ScanRepository$exportScan$28com/scanner3d/app/repository/ScanRepository$exportScan$1:com/scanner3d/app/repository/ScanRepository$getScanStats$2=com/scanner3d/app/repository/ScanRepository$cleanupOldScans$2=com/scanner3d/app/repository/ScanRepository$cleanupOldScans$15com/scanner3d/app/repository/ScanRepository$Companion5com/scanner3d/app/repository/ScanRepository$ScanStats0com/scanner3d/app/ui/auth/AuthenticationActivityVcom/scanner3d/app/ui/auth/AuthenticationActivity$special$$inlined$viewModels$default$1Vcom/scanner3d/app/ui/auth/AuthenticationActivity$special$$inlined$viewModels$default$2Vcom/scanner3d/app/ui/auth/AuthenticationActivity$special$$inlined$viewModels$default$3=com/scanner3d/app/ui/auth/AuthenticationActivity$WhenMappings%com/scanner3d/app/ui/custom/DepthView*com/scanner3d/app/ui/custom/PointCloudView=com/scanner3d/app/ui/custom/PointCloudView$PointCloudRenderer,com/scanner3d/app/ui/gallery/GalleryActivityRcom/scanner3d/app/ui/gallery/GalleryActivity$special$$inlined$viewModels$default$1Rcom/scanner3d/app/ui/gallery/GalleryActivity$special$$inlined$viewModels$default$2Rcom/scanner3d/app/ui/gallery/GalleryActivity$special$$inlined$viewModels$default$3@com/scanner3d/app/ui/gallery/GalleryActivity$setupRecyclerView$1@com/scanner3d/app/ui/gallery/GalleryActivity$setupRecyclerView$28com/scanner3d/app/ui/gallery/GalleryActivity$setupUI$1$3+com/scanner3d/app/ui/gallery/GalleryAdapter4com/scanner3d/app/ui/gallery/GalleryAdapter$ViewMode5com/scanner3d/app/ui/gallery/GalleryAdapter$Companion:com/scanner3d/app/ui/gallery/GalleryAdapter$GridViewHolder:com/scanner3d/app/ui/gallery/GalleryAdapter$ListViewHolder<com/scanner3d/app/ui/gallery/GalleryAdapter$ScanDiffCallback8com/scanner3d/app/ui/gallery/GalleryAdapter$WhenMappings.com/scanner3d/app/ui/scanning/ScanningActivityTcom/scanner3d/app/ui/scanning/ScanningActivity$special$$inlined$viewModels$default$1Tcom/scanner3d/app/ui/scanning/ScanningActivity$special$$inlined$viewModels$default$2Tcom/scanner3d/app/ui/scanning/ScanningActivity$special$$inlined$viewModels$default$38com/scanner3d/app/ui/scanning/ScanningActivity$Companion)com/scanner3d/app/utils/EncryptionManager3com/scanner3d/app/utils/EncryptionManager$Companion7com/scanner3d/app/utils/EncryptionManager$EncryptedData5com/scanner3d/app/utils/EncryptionManager$masterKey$24com/scanner3d/app/utils/EncryptionManager$keyStore$2#com/scanner3d/app/utils/FileManager-com/scanner3d/app/utils/FileManager$Companion/com/scanner3d/app/utils/FileManager$StorageInfo.com/scanner3d/app/utils/FileManager$scansDir$23com/scanner3d/app/utils/FileManager$thumbnailsDir$20com/scanner3d/app/utils/FileManager$exportsDir$2.com/scanner3d/app/utils/FileManager$cacheDir$2%com/scanner3d/app/utils/MemoryManager=com/scanner3d/app/utils/MemoryManager$startMemoryMonitoring$1/com/scanner3d/app/utils/MemoryManager$Companion0com/scanner3d/app/utils/MemoryManager$MemoryInfo0com/scanner3d/app/utils/MemoryManager$CacheStats/com/scanner3d/app/utils/MemoryManager$TypeStats7com/scanner3d/app/utils/MemoryManager$MemoryUsageReport2com/scanner3d/app/utils/MemoryManager$MemoryObject6com/scanner3d/app/utils/MemoryManager$MemoryObjectType'com/scanner3d/app/utils/MemoryManager$1$com/scanner3d/app/utils/MeshExporter.com/scanner3d/app/utils/MeshExporter$Companioncom/scanner3d/app/utils/Point%com/scanner3d/app/utils/MeshGenerator4com/scanner3d/app/utils/MeshGenerator$generateMesh$2/com/scanner3d/app/utils/MeshGenerator$Companion3com/scanner3d/app/utils/MeshGenerator$Triangulation&com/scanner3d/app/utils/MeshSimplifier5com/scanner3d/app/utils/MeshSimplifier$simplifyMesh$2Scom/scanner3d/app/utils/MeshSimplifier$quadricErrorSimplification$$inlined$sortBy$10com/scanner3d/app/utils/MeshSimplifier$Companion+com/scanner3d/app/utils/MeshSimplifier$Edge6com/scanner3d/app/utils/MeshSimplifier$VertexAdjacency4com/scanner3d/app/utils/MeshSimplifier$QuadricMatrix>com/scanner3d/app/utils/MeshSimplifier$QuadricMatrix$Companion)com/scanner3d/app/utils/TextureCompressor;com/scanner3d/app/utils/TextureCompressor$compressTexture$2Hcom/scanner3d/app/utils/TextureCompressor$compressTexture$2$WhenMappings;com/scanner3d/app/utils/TextureCompressor$generateMipmaps$2@com/scanner3d/app/utils/TextureCompressor$optimizeTextureAtlas$2lcom/scanner3d/app/utils/TextureCompressor$optimizeTextureAtlas$2$invokeSuspend$$inlined$sortedByDescending$13com/scanner3d/app/utils/TextureCompressor$Companion;com/scanner3d/app/utils/TextureCompressor$CompressionFormat<com/scanner3d/app/utils/TextureCompressor$CompressionQuality;com/scanner3d/app/utils/TextureCompressor$CompressedTexture6com/scanner3d/app/utils/TextureCompressor$TextureAtlas7com/scanner3d/app/utils/TextureCompressor$PackedTexture8com/scanner3d/app/utils/TextureCompressor$IndexedTexture3com/scanner3d/app/utils/TextureCompressor$Rectangle%com/scanner3d/app/utils/TextureMapper5com/scanner3d/app/utils/TextureMapper$applyTextures$2/com/scanner3d/app/utils/TextureMapper$Companion*com/scanner3d/app/utils/ThumbnailGeneratorLcom/scanner3d/app/utils/ThumbnailGenerator$renderTriangles$$inlined$sortBy$14com/scanner3d/app/utils/ThumbnailGenerator$Companion)com/scanner3d/app/viewmodel/AuthViewModelEcom/scanner3d/app/viewmodel/AuthViewModel$authenticateWithBiometric$1?com/scanner3d/app/viewmodel/AuthViewModel$authenticateWithPin$14com/scanner3d/app/viewmodel/AuthViewModel$setupPin$14com/scanner3d/app/viewmodel/AuthViewModel$AuthResult,com/scanner3d/app/viewmodel/GalleryViewModel8com/scanner3d/app/viewmodel/GalleryViewModel$loadScans$1:com/scanner3d/app/viewmodel/GalleryViewModel$loadScans$1$1:com/scanner3d/app/viewmodel/GalleryViewModel$searchScans$1<com/scanner3d/app/viewmodel/GalleryViewModel$searchScans$1$19com/scanner3d/app/viewmodel/GalleryViewModel$deleteScan$19com/scanner3d/app/viewmodel/GalleryViewModel$renameScan$1]com/scanner3d/app/viewmodel/GalleryViewModel$applyFilterAndSort$$inlined$sortedByDescending$1Scom/scanner3d/app/viewmodel/GalleryViewModel$applyFilterAndSort$$inlined$sortedBy$1]com/scanner3d/app/viewmodel/GalleryViewModel$applyFilterAndSort$$inlined$sortedByDescending$2Scom/scanner3d/app/viewmodel/GalleryViewModel$applyFilterAndSort$$inlined$sortedBy$26com/scanner3d/app/viewmodel/GalleryViewModel$ScanStats3com/scanner3d/app/viewmodel/GalleryViewModel$SortBy5com/scanner3d/app/viewmodel/GalleryViewModel$FilterBy9com/scanner3d/app/viewmodel/GalleryViewModel$WhenMappings)com/scanner3d/app/viewmodel/MainViewModel?com/scanner3d/app/viewmodel/MainViewModel$checkAuthentication$12com/scanner3d/app/viewmodel/MainViewModel$logout$1-com/scanner3d/app/viewmodel/ScanningViewModelBcom/scanner3d/app/viewmodel/ScanningViewModel$initializeScanning$1=com/scanner3d/app/viewmodel/ScanningViewModel$startScanning$1<com/scanner3d/app/viewmodel/ScanningViewModel$stopScanning$1=com/scanner3d/app/viewmodel/ScanningViewModel$pauseScanning$1>com/scanner3d/app/viewmodel/ScanningViewModel$resumeScanning$1/com/scanner3d/app/viewmodel/ScanningViewModel$11com/scanner3d/app/viewmodel/ScanningViewModel$1$1/com/scanner3d/app/viewmodel/ScanningViewModel$21com/scanner3d/app/viewmodel/ScanningViewModel$2$1/com/scanner3d/app/viewmodel/ScanningViewModel$31com/scanner3d/app/viewmodel/ScanningViewModel$3$1.com/scanner3d/app/ui/model/ModelViewerActivityTcom/scanner3d/app/ui/model/ModelViewerActivity$special$$inlined$viewModels$default$1Tcom/scanner3d/app/ui/model/ModelViewerActivity$special$$inlined$viewModels$default$2Tcom/scanner3d/app/ui/model/ModelViewerActivity$special$$inlined$viewModels$default$30com/scanner3d/app/viewmodel/ModelViewerViewModel;com/scanner3d/app/viewmodel/ModelViewerViewModel$loadScan$1Ecom/scanner3d/app/viewmodel/ModelViewerViewModel$loadPointCloudData$1=com/scanner3d/app/viewmodel/ModelViewerViewModel$exportScan$1=com/scanner3d/app/viewmodel/ModelViewerViewModel$deleteScan$1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          