// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAuthenticationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnSubmitPin;

  @NonNull
  public final Button btnUseBiometric;

  @NonNull
  public final Button btnUsePin;

  @NonNull
  public final TextInputEditText etPin;

  @NonNull
  public final ImageView ivAuthLogo;

  @NonNull
  public final LinearLayout llAuthButtons;

  @NonNull
  public final LinearLayout llPinInput;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvAuthSubtitle;

  @NonNull
  public final TextView tvAuthTitle;

  @NonNull
  public final TextView tvPinLabel;

  private ActivityAuthenticationBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnSubmitPin, @NonNull Button btnUseBiometric, @NonNull Button btnUsePin,
      @NonNull TextInputEditText etPin, @NonNull ImageView ivAuthLogo,
      @NonNull LinearLayout llAuthButtons, @NonNull LinearLayout llPinInput,
      @NonNull ProgressBar progressBar, @NonNull TextView tvAuthSubtitle,
      @NonNull TextView tvAuthTitle, @NonNull TextView tvPinLabel) {
    this.rootView = rootView;
    this.btnSubmitPin = btnSubmitPin;
    this.btnUseBiometric = btnUseBiometric;
    this.btnUsePin = btnUsePin;
    this.etPin = etPin;
    this.ivAuthLogo = ivAuthLogo;
    this.llAuthButtons = llAuthButtons;
    this.llPinInput = llPinInput;
    this.progressBar = progressBar;
    this.tvAuthSubtitle = tvAuthSubtitle;
    this.tvAuthTitle = tvAuthTitle;
    this.tvPinLabel = tvPinLabel;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAuthenticationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAuthenticationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_authentication, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAuthenticationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_submit_pin;
      Button btnSubmitPin = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmitPin == null) {
        break missingId;
      }

      id = R.id.btn_use_biometric;
      Button btnUseBiometric = ViewBindings.findChildViewById(rootView, id);
      if (btnUseBiometric == null) {
        break missingId;
      }

      id = R.id.btn_use_pin;
      Button btnUsePin = ViewBindings.findChildViewById(rootView, id);
      if (btnUsePin == null) {
        break missingId;
      }

      id = R.id.et_pin;
      TextInputEditText etPin = ViewBindings.findChildViewById(rootView, id);
      if (etPin == null) {
        break missingId;
      }

      id = R.id.iv_auth_logo;
      ImageView ivAuthLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivAuthLogo == null) {
        break missingId;
      }

      id = R.id.ll_auth_buttons;
      LinearLayout llAuthButtons = ViewBindings.findChildViewById(rootView, id);
      if (llAuthButtons == null) {
        break missingId;
      }

      id = R.id.ll_pin_input;
      LinearLayout llPinInput = ViewBindings.findChildViewById(rootView, id);
      if (llPinInput == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_auth_subtitle;
      TextView tvAuthSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAuthSubtitle == null) {
        break missingId;
      }

      id = R.id.tv_auth_title;
      TextView tvAuthTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAuthTitle == null) {
        break missingId;
      }

      id = R.id.tv_pin_label;
      TextView tvPinLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvPinLabel == null) {
        break missingId;
      }

      return new ActivityAuthenticationBinding((ConstraintLayout) rootView, btnSubmitPin,
          btnUseBiometric, btnUsePin, etPin, ivAuthLogo, llAuthButtons, llPinInput, progressBar,
          tvAuthSubtitle, tvAuthTitle, tvPinLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
