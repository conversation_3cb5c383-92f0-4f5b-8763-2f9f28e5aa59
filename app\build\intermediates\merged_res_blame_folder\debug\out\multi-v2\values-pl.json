{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-60:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "55,56,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5013,5117,5352,5477,5615,5768,5895,6023,6170,6270,6404,6543", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "5112,5202,5472,5610,5763,5890,6018,6165,6265,6399,6538,6662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,967,1069,1177,1263,1370,1489,1568,1644,1735,1828,1923,2017,2118,2211,2306,2401,2492,2583,2665,2774,2874,2973,3082,3194,3305,3468,11537", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "962,1064,1172,1258,1365,1484,1563,1639,1730,1823,1918,2012,2113,2206,2301,2396,2487,2578,2660,2769,2869,2968,3077,3189,3300,3463,3559,11615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,116", "endOffsets": "158,275"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11236,11344", "endColumns": "107,116", "endOffsets": "11339,11456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1162,1243,1307,1368,1479,1543,1611,1665,1734,1796,1850,1961,2022,2084,2138,2210,2339,2428,2510,2659,2741,2824,2961,3048,3125,3179,3230,3296,3367,3443,3532,3615,3692,3770,3848,3924,4032,4122,4195,4290,4387,4459,4533,4633,4685,4770,4836,4924,5014,5076,5140,5203,5274,5381,5493,5592,5699,5757,5812", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1157,1238,1302,1363,1474,1538,1606,1660,1729,1791,1845,1956,2017,2079,2133,2205,2334,2423,2505,2654,2736,2819,2956,3043,3120,3174,3225,3291,3362,3438,3527,3610,3687,3765,3843,3919,4027,4117,4190,4285,4382,4454,4528,4628,4680,4765,4831,4919,5009,5071,5135,5198,5269,5376,5488,5587,5694,5752,5807,5883"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3564,3639,3714,3793,3897,4729,4814,4931,5207,5271,6667,6731,6792,6903,6967,7035,7089,7158,7220,7274,7385,7446,7508,7562,7634,7763,7852,7934,8083,8165,8248,8385,8472,8549,8603,8654,8720,8791,8867,8956,9039,9116,9194,9272,9348,9456,9546,9619,9714,9811,9883,9957,10057,10109,10194,10260,10348,10438,10500,10564,10627,10698,10805,10917,11016,11123,11181,11461", "endLines": "7,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "431,3634,3709,3788,3892,3987,4809,4926,5008,5266,5347,6726,6787,6898,6962,7030,7084,7153,7215,7269,7380,7441,7503,7557,7629,7758,7847,7929,8078,8160,8243,8380,8467,8544,8598,8649,8715,8786,8862,8951,9034,9111,9189,9267,9343,9451,9541,9614,9709,9806,9878,9952,10052,10104,10189,10255,10343,10433,10495,10559,10622,10693,10800,10912,11011,11118,11176,11231,11532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,285,394,505", "endColumns": "46,47,108,110,80", "endOffsets": "236,284,393,504,585"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "436,487,539,652,767", "endColumns": "50,51,112,114,84", "endOffsets": "482,534,647,762,847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "45,46,47,48,49,50,51,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3992,4089,4191,4289,4388,4502,4607,11620", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "4084,4186,4284,4383,4497,4602,4724,11716"}}]}]}