{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "11191,11294", "endColumns": "102,124", "endOffsets": "11289,11414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1133,1225,1302,1365,1473,1533,1599,1655,1726,1786,1840,1959,2016,2078,2132,2207,2331,2419,2502,2647,2732,2818,2951,3039,3117,3171,3225,3291,3365,3443,3530,3612,3684,3761,3834,3904,4013,4106,4178,4270,4366,4440,4516,4612,4665,4747,4814,4901,4988,5050,5114,5177,5246,5354,5459,5560,5663,5721,5779", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "322,401,481,563,665,759,855,981,1062,1128,1220,1297,1360,1468,1528,1594,1650,1721,1781,1835,1954,2011,2073,2127,2202,2326,2414,2497,2642,2727,2813,2946,3034,3112,3166,3220,3286,3360,3438,3525,3607,3679,3756,3829,3899,4008,4101,4173,4265,4361,4435,4511,4607,4660,4742,4809,4896,4983,5045,5109,5172,5241,5349,5454,5555,5658,5716,5774,5854"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3542,3621,3701,3783,3885,4704,4800,4926,5209,5275,6637,6714,6777,6885,6945,7011,7067,7138,7198,7252,7371,7428,7490,7544,7619,7743,7831,7914,8059,8144,8230,8363,8451,8529,8583,8637,8703,8777,8855,8942,9024,9096,9173,9246,9316,9425,9518,9590,9682,9778,9852,9928,10024,10077,10159,10226,10313,10400,10462,10526,10589,10658,10766,10871,10972,11075,11133,11419", "endLines": "6,39,40,41,42,43,51,52,53,56,57,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "372,3616,3696,3778,3880,3974,4795,4921,5002,5270,5362,6709,6772,6880,6940,7006,7062,7133,7193,7247,7366,7423,7485,7539,7614,7738,7826,7909,8054,8139,8225,8358,8446,8524,8578,8632,8698,8772,8850,8937,9019,9091,9168,9241,9311,9420,9513,9585,9677,9773,9847,9923,10019,10072,10154,10221,10308,10395,10457,10521,10584,10653,10761,10866,10967,11070,11128,11186,11494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "44,45,46,47,48,49,50,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3979,4077,4179,4277,4381,4485,4587,11586", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "4072,4174,4272,4376,4480,4582,4699,11682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "817,938,1035,1142,1228,1332,1454,1539,1621,1712,1805,1900,1994,2094,2187,2282,2377,2468,2559,2647,2750,2854,2955,3060,3174,3277,3446,11499", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "933,1030,1137,1223,1327,1449,1534,1616,1707,1800,1895,1989,2089,2182,2277,2372,2463,2554,2642,2745,2849,2950,3055,3169,3272,3441,3537,11581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,380,512,643,771,901,1035,1136,1271,1404", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "159,252,375,507,638,766,896,1030,1131,1266,1399,1522"}, "to": {"startLines": "54,55,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5007,5116,5367,5490,5622,5753,5881,6011,6145,6246,6381,6514", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "5111,5204,5485,5617,5748,5876,6006,6140,6241,6376,6509,6632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,287,406,527", "endColumns": "46,49,118,120,82", "endOffsets": "236,286,405,526,609"}, "to": {"startLines": "7,8,9,10,11", "startColumns": "4,4,4,4,4", "startOffsets": "377,428,482,605,730", "endColumns": "50,53,122,124,86", "endOffsets": "423,477,600,725,812"}}]}]}