<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Required permissions for 3D scanning -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- ARCore requirements -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.ar"
        android:required="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Scanner3D"
        tools:targetApi="31">

        <!-- ARCore metadata -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="required" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Scanner3D">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activity -->
        <activity
            android:name=".ui.auth.AuthenticationActivity"
            android:exported="false"
            android:theme="@style/Theme.Scanner3D" />

        <!-- Scanning Activity -->
        <activity
            android:name=".ui.scanning.ScanningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Gallery Activity -->
        <activity
            android:name=".ui.gallery.GalleryActivity"
            android:exported="false"
            android:theme="@style/Theme.Scanner3D" />

        <!-- Model Viewer Activity -->
        <activity
            android:name=".ui.model.ModelViewerActivity"
            android:exported="false"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.Scanner3D" />

    </application>

</manifest>
