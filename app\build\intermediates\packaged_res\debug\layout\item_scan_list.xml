<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/stroke_light"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Thumbnail -->
        <ImageView
            android:id="@+id/iv_thumbnail"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@color/surface_light"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_3d_model_placeholder" />

        <!-- Scan Name -->
        <TextView
            android:id="@+id/tv_scan_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary_light"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tv_quality"
            app:layout_constraintStart_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Living Room Scan" />

        <!-- Quality Badge -->
        <TextView
            android:id="@+id/tv_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_quality_high"
            android:paddingStart="8dp"
            android:paddingTop="4dp"
            android:paddingEnd="8dp"
            android:paddingBottom="4dp"
            android:text="HIGH"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="HIGH" />

        <!-- Description -->
        <TextView
            android:id="@+id/tv_scan_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_secondary_light"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toBottomOf="@+id/tv_scan_name"
            tools:text="3D scan of the living room with furniture" />

        <!-- Stats Row -->
        <LinearLayout
            android:id="@+id/ll_stats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toBottomOf="@+id/tv_scan_description">

            <TextView
                android:id="@+id/tv_vertex_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="12sp"
                tools:text="15.2K vertices" />

            <TextView
                android:id="@+id/tv_triangle_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="12sp"
                tools:text="30.4K triangles" />

        </LinearLayout>

        <!-- Info Row -->
        <LinearLayout
            android:id="@+id/ll_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toBottomOf="@+id/ll_stats">

            <TextView
                android:id="@+id/tv_scan_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="12sp"
                tools:text="Mar 15, 2024" />

            <TextView
                android:id="@+id/tv_scan_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/text_secondary_light"
                android:textSize="12sp"
                tools:text="2m 30s" />

            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary_light"
                android:textSize="12sp"
                tools:text="2.5 MB" />

        </LinearLayout>

        <!-- Feature Chips -->
        <com.google.android.material.chip.ChipGroup
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            app:chipSpacingHorizontal="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_thumbnail"
            app:layout_constraintTop_toBottomOf="@+id/ll_info">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_texture"
                style="@style/Widget.MaterialComponents.Chip.Entry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texture"
                android:textSize="10sp"
                android:visibility="gone"
                app:chipBackgroundColor="@color/success_green"
                app:chipMinHeight="24dp"
                tools:visibility="visible" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_colors"
                style="@style/Widget.MaterialComponents.Chip.Entry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Colors"
                android:textSize="10sp"
                android:visibility="gone"
                app:chipBackgroundColor="@color/primary_blue"
                app:chipMinHeight="24dp"
                tools:visibility="visible" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_uploaded"
                style="@style/Widget.MaterialComponents.Chip.Entry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cloud"
                android:textSize="10sp"
                android:visibility="gone"
                app:chipBackgroundColor="@color/warning_orange"
                app:chipMinHeight="24dp"
                tools:visibility="visible" />

        </com.google.android.material.chip.ChipGroup>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
