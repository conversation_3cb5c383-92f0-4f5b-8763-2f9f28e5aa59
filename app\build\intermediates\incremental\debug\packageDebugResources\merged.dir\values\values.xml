<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_green">#4CAF50</color>
    <color name="accent_orange">#FF9800</color>
    <color name="accent_orange_dark">#F57C00</color>
    <color name="accent_orange_light">#FFE0B2</color>
    <color name="background_dark">#121212</color>
    <color name="background_light">#FAFAFA</color>
    <color name="black">#000000</color>
    <color name="camera_overlay">#80000000</color>
    <color name="error_red">#F44336</color>
    <color name="gray_dark">#424242</color>
    <color name="gray_light">#F5F5F5</color>
    <color name="gray_medium">#9E9E9E</color>
    <color name="info_blue">#2196F3</color>
    <color name="primary_blue">#2196F3</color>
    <color name="primary_blue_dark">#1976D2</color>
    <color name="primary_blue_light">#BBDEFB</color>
    <color name="quality_high">#4CAF50</color>
    <color name="quality_low">#F44336</color>
    <color name="quality_medium">#FF9800</color>
    <color name="quality_ultra">#9C27B0</color>
    <color name="scan_progress">#4CAF50</color>
    <color name="secondary_teal">#009688</color>
    <color name="secondary_teal_dark">#00695C</color>
    <color name="secondary_teal_light">#B2DFDB</color>
    <color name="stroke_dark">#333333</color>
    <color name="stroke_light">#E0E0E0</color>
    <color name="success_green">#4CAF50</color>
    <color name="surface_dark">#1E1E1E</color>
    <color name="surface_light">#FFFFFF</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_primary_light">#212121</color>
    <color name="text_secondary_dark">#AAAAAA</color>
    <color name="text_secondary_light">#757575</color>
    <color name="warning_orange">#FF9800</color>
    <color name="white">#FFFFFF</color>
    <string name="app_name">3D Scanner</string>
    <string name="auth_subtitle">Please authenticate to access your 3D scans</string>
    <string name="auth_title">Secure Access</string>
    <string name="back">Back</string>
    <string name="camera_view">Camera</string>
    <string name="cancel">Cancel</string>
    <string name="delete">Delete</string>
    <string name="depth_view">Depth</string>
    <string name="enter_pin">Enter PIN</string>
    <string name="error_camera_permission">Camera permission is required for scanning</string>
    <string name="error_load_failed">Failed to load scan</string>
    <string name="error_save_failed">Failed to save scan</string>
    <string name="error_scanning_failed">Scanning failed. Please try again.</string>
    <string name="export">Export</string>
    <string name="main_title">3D Scanner</string>
    <string name="new_scan">New Scan</string>
    <string name="ok">OK</string>
    <string name="pause_scan">Pause</string>
    <string name="rename">Rename</string>
    <string name="resume_scan">Resume</string>
    <string name="scan_deleted">Scan deleted</string>
    <string name="scan_exported">Scan exported successfully</string>
    <string name="scan_progress">Progress: %d%%</string>
    <string name="scan_saved">Scan saved successfully</string>
    <string name="scanning_title">3D Scanning</string>
    <string name="settings">Settings</string>
    <string name="share">Share</string>
    <string name="start_scan">Start</string>
    <string name="start_scanning">Start Scanning</string>
    <string name="stop_scan">Stop</string>
    <string name="use_biometric">Use Fingerprint/Face ID</string>
    <string name="use_pin">Use PIN</string>
    <string name="version">Version %s</string>
    <string name="view_gallery">View Gallery</string>
    <style name="Scanner3D.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    <style name="Scanner3D.Button.Danger">
        <item name="backgroundTint">@color/error_red</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="Scanner3D.Button.Primary">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="Scanner3D.Button.Secondary">
        <item name="backgroundTint">@color/gray_light</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style>
    <style name="Scanner3D.Card" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="Scanner3D.Text" parent="android:Widget.TextView">
        <item name="android:textColor">@color/text_primary_light</item>
    </style>
    <style name="Scanner3D.Text.Body">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style>
    <style name="Scanner3D.Text.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style>
    <style name="Scanner3D.Text.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style>
    <style name="Scanner3D.Text.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style>
    <style name="Scanner3D.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
    </style>
    <style name="Theme.Scanner3D" parent="Theme.MaterialComponents.DayNight">
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary_teal</item>
        <item name="colorSecondaryVariant">@color/secondary_teal_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorError">@color/error_red</item>
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:navigationBarColor">@color/primary_blue_dark</item>
    </style>
    <style name="Theme.Scanner3D.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>
</resources>