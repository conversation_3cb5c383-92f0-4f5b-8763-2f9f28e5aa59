<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.settings.SettingsActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.MaterialComponents.Light" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:indeterminate="true"
            android:visibility="gone" />

        <!-- Camera Quality Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/Scanner3D.Text.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Camera Settings"
                android:textStyle="bold" />

            <Switch
                android:id="@+id/switchHighQuality"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="High Quality Mode"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switchAutoFocus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Auto Focus"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switchFlashlight"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Enable Flashlight"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Scanning Settings Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/Scanner3D.Text.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Scanning Settings"
                android:textStyle="bold" />

            <!-- Resolution Setting -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Resolution"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <SeekBar
                    android:id="@+id/seekBarResolution"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="2"
                    android:progress="1" />

                <TextView
                    android:id="@+id/tvResolutionValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:minWidth="60dp"
                    android:text="1080p"
                    android:textAlignment="center"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Quality Setting -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Scan Quality"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <SeekBar
                    android:id="@+id/seekBarQuality"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="3"
                    android:progress="2" />

                <TextView
                    android:id="@+id/tvQualityValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:minWidth="60dp"
                    android:text="High"
                    android:textAlignment="center"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Security Settings Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/Scanner3D.Text.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Security Settings"
                android:textStyle="bold" />

            <Switch
                android:id="@+id/switchBiometric"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Biometric Authentication"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switchAutoLock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Auto Lock"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Storage Settings Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/Scanner3D.Text.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Storage"
                android:textStyle="bold" />

            <!-- Storage Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCacheSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Cache: 0 MB"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvDataSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Data: 0 MB"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvTotalSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Total: 0 MB"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!-- Storage Actions -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnClearCache"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="Clear Cache"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnExportData"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:text="Export"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnImportData"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="Import"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Advanced Settings Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                style="@style/Scanner3D.Text.Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Advanced"
                android:textStyle="bold" />

            <Switch
                android:id="@+id/switchDeveloperMode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Developer Mode"
                android:textSize="16sp" />

            <!-- Developer Options (hidden by default) -->
            <LinearLayout
                android:id="@+id/llDeveloperOptions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <Switch
                    android:id="@+id/switchDebugLogging"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Debug Logging"
                    android:textSize="16sp" />

            </LinearLayout>

            <!-- Reset Settings -->
            <Button
                android:id="@+id/btnResetSettings"
                style="@style/Scanner3D.Button.Danger"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Reset to Defaults" />

        </LinearLayout>

        <!-- Bottom Spacing -->
        <View
            android:layout_width="match_parent"
            android:layout_height="32dp" />

    </LinearLayout>

</ScrollView> 