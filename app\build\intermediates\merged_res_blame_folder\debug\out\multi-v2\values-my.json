{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-60:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11323,11436", "endColumns": "112,125", "endOffsets": "11431,11557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,953,1060,1176,1263,1372,1495,1574,1652,1743,1836,1931,2025,2125,2218,2313,2407,2498,2589,2674,2789,2898,2997,3123,3230,3338,3498,11644", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "948,1055,1171,1258,1367,1490,1569,1647,1738,1831,1926,2020,2120,2213,2308,2402,2493,2584,2669,2784,2893,2992,3118,3225,3333,3493,3596,11725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4052,4155,4259,4362,4464,4569,4675,11730", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "4150,4254,4357,4459,4564,4670,4789,11826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2910,3002,3079,3137,3188,3254,3326,3408,3490,3568,3643,3717,3789,3868,3976,4073,4154,4240,4332,4406,4485,4571,4625,4701,4769,4852,4933,4995,5059,5122,5190,5302,5413,5517,5630,5691,5746", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2905,2997,3074,3132,3183,3249,3321,3403,3485,3563,3638,3712,3784,3863,3971,4068,4149,4235,4327,4401,4480,4566,4620,4696,4764,4847,4928,4990,5054,5117,5185,5297,5408,5512,5625,5686,5741,5823"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3601,3702,3801,3877,3968,4794,4900,5029,5319,5384,6788,6863,6922,7013,7076,7141,7200,7271,7333,7390,7509,7567,7628,7683,7756,7888,7979,8068,8209,8287,8364,8487,8579,8656,8714,8765,8831,8903,8985,9067,9145,9220,9294,9366,9445,9553,9650,9731,9817,9909,9983,10062,10148,10202,10278,10346,10429,10510,10572,10636,10699,10767,10879,10990,11094,11207,11268,11562", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "330,3697,3796,3872,3963,4047,4895,5024,5109,5379,5469,6858,6917,7008,7071,7136,7195,7266,7328,7385,7504,7562,7623,7678,7751,7883,7974,8063,8204,8282,8359,8482,8574,8651,8709,8760,8826,8898,8980,9062,9140,9215,9289,9361,9440,9548,9645,9726,9812,9904,9978,10057,10143,10197,10273,10341,10424,10505,10567,10631,10694,10762,10874,10985,11089,11202,11263,11318,11639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,260,380,501,635,772,902,1055,1144,1294,1437", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "159,255,375,496,630,767,897,1050,1139,1289,1432,1569"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5114,5223,5474,5594,5715,5849,5986,6116,6269,6358,6508,6651", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "5218,5314,5589,5710,5844,5981,6111,6264,6353,6503,6646,6783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,293,430,570", "endColumns": "49,52,136,139,104", "endOffsets": "239,292,429,569,674"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "335,389,446,587,731", "endColumns": "53,56,140,143,108", "endOffsets": "384,441,582,726,835"}}]}]}