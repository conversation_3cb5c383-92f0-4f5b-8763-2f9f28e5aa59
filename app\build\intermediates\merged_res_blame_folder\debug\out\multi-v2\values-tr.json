{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-60:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3230fd2a7a34684e89ced05b023027\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3909,4006,4108,4206,4303,4405,4511,11378", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4001,4103,4201,4298,4400,4506,4617,11474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fbb2a044127634c9d3a6fc3f78750e9\\transformed\\material-1.11.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3492,3567,3642,3719,3818,4622,4718,4830,5110,5174,6540,6617,6678,6769,6832,6895,6954,7023,7086,7140,7248,7306,7368,7422,7495,7616,7700,7791,7931,8008,8084,8215,8302,8378,8431,8485,8551,8621,8698,8781,8861,8932,9007,9085,9156,9257,9342,9431,9526,9619,9691,9763,9859,9911,9997,10064,10148,10238,10300,10364,10427,10497,10591,10693,10782,10882,10939,11219", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "310,3562,3637,3714,3813,3904,4713,4825,4907,5169,5260,6612,6673,6764,6827,6890,6949,7018,7081,7135,7243,7301,7363,7417,7490,7611,7695,7786,7926,8003,8079,8210,8297,8373,8426,8480,8546,8616,8693,8776,8856,8927,9002,9080,9151,9252,9337,9426,9521,9614,9686,9758,9854,9906,9992,10059,10143,10233,10295,10359,10422,10492,10586,10688,10777,10877,10934,10992,11293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\955c5ec1b827236d7c0018924d2d14b2\\transformed\\core-1.41.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,284,425,568", "endColumns": "45,47,140,142,86", "endOffsets": "235,283,424,567,654"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "315,365,417,562,709", "endColumns": "49,51,144,146,90", "endOffsets": "360,412,557,704,795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\429a30bcc688c4b342e3c444713d0398\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "800,914,1013,1125,1210,1316,1436,1516,1591,1682,1775,1867,1961,2061,2154,2256,2351,2442,2533,2612,2719,2823,2919,3026,3129,3238,3394,11298", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "909,1008,1120,1205,1311,1431,1511,1586,1677,1770,1862,1956,2056,2149,2251,2346,2437,2528,2607,2714,2818,2914,3021,3124,3233,3389,3487,11373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4054160278556e062d4ea0b7d8eca303\\transformed\\navigation-ui-2.7.5\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10997,11102", "endColumns": "104,116", "endOffsets": "11097,11214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ddabe16ea9ddf7c286dc8364cabf1745\\transformed\\biometric-1.1.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,253,372,501,626,770,887,1030,1125,1249,1390", "endColumns": "105,91,118,128,124,143,116,142,94,123,140,137", "endOffsets": "156,248,367,496,621,765,882,1025,1120,1244,1385,1523"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4912,5018,5265,5384,5513,5638,5782,5899,6042,6137,6261,6402", "endColumns": "105,91,118,128,124,143,116,142,94,123,140,137", "endOffsets": "5013,5105,5379,5508,5633,5777,5894,6037,6132,6256,6397,6535"}}]}]}