package com.scanner3d.app.ui.settings

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.scanner3d.app.databinding.ActivitySettingsBinding
import com.scanner3d.app.viewmodel.SettingsViewModel

class SettingsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySettingsBinding
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupUI()
        observeViewModel()
        loadSettings()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "Settings"
        }
    }
    
    private fun setupUI() {
        binding.apply {
            // Camera Quality Settings
            switchHighQuality.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setHighQualityMode(isChecked)
            }
            
            switchAutoFocus.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setAutoFocus(isChecked)
            }
            
            switchFlashlight.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setFlashlightEnabled(isChecked)
            }
            
            // Scanning Settings
            seekBarResolution.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        val resolution = when (progress) {
                            0 -> "720p"
                            1 -> "1080p"
                            2 -> "4K"
                            else -> "1080p"
                        }
                        tvResolutionValue.text = resolution
                        viewModel.setResolution(resolution)
                    }
                }
                override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
            })
            
            seekBarQuality.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        val qualityDisplay = when (progress) {
                            0 -> "Low"
                            1 -> "Medium" 
                            2 -> "High"
                            3 -> "Ultra"
                            else -> "High"
                        }
                        val qualityValue = when (progress) {
                            0 -> "LOW"
                            1 -> "MEDIUM" 
                            2 -> "HIGH"
                            3 -> "ULTRA"
                            else -> "HIGH"
                        }
                        tvQualityValue.text = qualityDisplay
                        viewModel.setScanQuality(qualityValue)
                    }
                }
                override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
            })
            
            // Security Settings
            switchBiometric.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setBiometricEnabled(isChecked)
            }
            
            switchAutoLock.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setAutoLockEnabled(isChecked)
            }
            
            // Storage Settings
            btnClearCache.setOnClickListener {
                showClearCacheDialog()
            }
            
            btnExportData.setOnClickListener {
                viewModel.exportAppData()
            }
            
            btnImportData.setOnClickListener {
                viewModel.importAppData()
            }
            
            // Advanced Settings
            switchDeveloperMode.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setDeveloperMode(isChecked)
                updateDeveloperOptions(isChecked)
            }
            
            switchDebugLogging.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setDebugLogging(isChecked)
            }
            
            // Reset Settings
            btnResetSettings.setOnClickListener {
                showResetDialog()
            }
        }
    }
    
    private fun observeViewModel() {
        viewModel.settings.observe(this, Observer { settings ->
            updateUI(settings)
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        })
        
        viewModel.message.observe(this, Observer { message ->
            message?.let {
                Toast.makeText(this, it, Toast.LENGTH_SHORT).show()
                viewModel.clearMessage()
            }
        })
        
        viewModel.storageInfo.observe(this, Observer { info ->
            binding.apply {
                tvCacheSize.text = "Cache: ${formatFileSize(info.cacheSize)}"
                tvDataSize.text = "Data: ${formatFileSize(info.dataSize)}"
                tvTotalSize.text = "Total: ${formatFileSize(info.totalSize)}"
            }
        })
    }
    
    private fun updateUI(settings: com.scanner3d.app.data.model.AppSettings) {
        binding.apply {
            switchHighQuality.isChecked = settings.highQualityMode
            switchAutoFocus.isChecked = settings.autoFocus
            switchFlashlight.isChecked = settings.flashlightEnabled
            
            val resolutionIndex = when (settings.resolution) {
                "720p" -> 0
                "1080p" -> 1
                "4K" -> 2
                else -> 1
            }
            seekBarResolution.progress = resolutionIndex
            tvResolutionValue.text = settings.resolution
            
            val qualityIndex = when (settings.defaultScanQuality) {
                "LOW" -> 0
                "MEDIUM" -> 1
                "HIGH" -> 2
                "ULTRA" -> 3
                else -> 2
            }
            seekBarQuality.progress = qualityIndex
            tvQualityValue.text = when (settings.defaultScanQuality) {
                "LOW" -> "Low"
                "MEDIUM" -> "Medium" 
                "HIGH" -> "High"
                "ULTRA" -> "Ultra"
                else -> "High"
            }
            
            switchBiometric.isChecked = settings.biometricEnabled
            switchAutoLock.isChecked = settings.autoLockEnabled
            switchDeveloperMode.isChecked = settings.developerMode
            switchDebugLogging.isChecked = settings.debugLogging
            
            updateDeveloperOptions(settings.developerMode)
        }
    }
    
    private fun updateDeveloperOptions(enabled: Boolean) {
        binding.llDeveloperOptions.visibility = if (enabled) android.view.View.VISIBLE else android.view.View.GONE
    }
    
    private fun loadSettings() {
        viewModel.loadSettings()
        viewModel.loadStorageInfo()
    }
    
    private fun showClearCacheDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Clear Cache")
            .setMessage("This will delete all cached data including thumbnails and temporary files. Are you sure?")
            .setPositiveButton("Clear") { _, _ ->
                viewModel.clearCache()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun showResetDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Reset Settings")
            .setMessage("This will reset all settings to their default values. Are you sure?")
            .setPositiveButton("Reset") { _, _ ->
                viewModel.resetSettings()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
} 