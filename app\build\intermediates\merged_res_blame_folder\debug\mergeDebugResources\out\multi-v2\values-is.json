{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa51a49a0fdb8d78736b292657adf1a3\\transformed\\core-1.41.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,288,405,530", "endColumns": "49,47,116,124,85", "endOffsets": "239,287,404,529,615"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "320,374,426,547,676", "endColumns": "53,51,120,128,89", "endOffsets": "369,421,542,671,761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08372b0b4714dee0e9823f116e744e60\\transformed\\navigation-ui-2.7.5\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,113", "endOffsets": "153,267"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10911,11014", "endColumns": "102,113", "endOffsets": "11009,11123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0060a6d1e9ec36933e7259d41494599\\transformed\\material-1.11.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2774,2861,2940,2997,3052,3118,3194,3274,3363,3439,3506,3580,3650,3716,3818,3904,3974,4065,4155,4229,4302,4391,4442,4523,4595,4676,4762,4824,4888,4951,5020,5134,5240,5348,5450,5511,5570", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2769,2856,2935,2992,3047,3113,3189,3269,3358,3434,3501,3575,3645,3711,3813,3899,3969,4060,4150,4224,4297,4386,4437,4518,4590,4671,4757,4819,4883,4946,5015,5129,5235,5343,5445,5506,5565,5645"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3440,3514,3586,3665,3747,4544,4641,4756,5036,5101,6445,6509,6570,6660,6724,6787,6849,6917,6981,7037,7160,7225,7287,7343,7414,7541,7625,7709,7845,7922,7999,8115,8202,8281,8338,8393,8459,8535,8615,8704,8780,8847,8921,8991,9057,9159,9245,9315,9406,9496,9570,9643,9732,9783,9864,9936,10017,10103,10165,10229,10292,10361,10475,10581,10689,10791,10852,11128", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "315,3509,3581,3660,3742,3822,4636,4751,4833,5096,5184,6504,6565,6655,6719,6782,6844,6912,6976,7032,7155,7220,7282,7338,7409,7536,7620,7704,7840,7917,7994,8110,8197,8276,8333,8388,8454,8530,8610,8699,8775,8842,8916,8986,9052,9154,9240,9310,9401,9491,9565,9638,9727,9778,9859,9931,10012,10098,10160,10224,10287,10356,10470,10576,10684,10786,10847,10906,11203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e90c754ff7f976aa957bdef8385fc4b1\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,866,963,1075,1160,1261,1375,1456,1535,1626,1719,1812,1906,2012,2105,2200,2295,2386,2480,2561,2671,2778,2875,2984,3084,3187,3342,11208", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "861,958,1070,1155,1256,1370,1451,1530,1621,1714,1807,1901,2007,2100,2195,2290,2381,2475,2556,2666,2773,2870,2979,3079,3182,3337,3435,11284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b986efbab3f35a8495809ca7b1ee567d\\transformed\\core-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3827,3922,4029,4126,4226,4329,4433,11289", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3917,4024,4121,4221,4324,4428,4539,11385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad912a0b6595c0fd27017314ccf5845d\\transformed\\biometric-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,253,369,500,631,756,882,1009,1108,1250,1392", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "159,248,364,495,626,751,877,1004,1103,1245,1387,1504"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4838,4947,5189,5305,5436,5567,5692,5818,5945,6044,6186,6328", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "4942,5031,5300,5431,5562,5687,5813,5940,6039,6181,6323,6440"}}]}]}