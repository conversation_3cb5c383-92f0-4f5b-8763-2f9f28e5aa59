package com.scanner3d.app.utils

import android.util.Log
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.*

class MeshSimplifier {
    
    companion object {
        private const val TAG = "MeshSimplifier"
    }
    
    suspend fun simplifyMesh(
        mesh: Mesh3D,
        targetReduction: Float = 0.5f, // Reduce to 50% of original triangles
        preserveTexture: Boolean = true
    ): Mesh3D = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Starting mesh simplification. Original triangles: ${mesh.triangleCount}")
        
        try {
            val targetTriangleCount = (mesh.triangleCount * (1f - targetReduction)).toInt()
            
            // Use quadric error metrics for edge collapse simplification
            val simplifiedMesh = quadricErrorSimplification(mesh, targetTriangleCount, preserveTexture)
            
            Log.d(TAG, "Mesh simplification completed. New triangles: ${simplifiedMesh.triangleCount}")
            simplifiedMesh
            
        } catch (e: Exception) {
            Log.e(TAG, "Mesh simplification failed", e)
            mesh // Return original mesh if simplification fails
        }
    }
    
    private fun quadricErrorSimplification(
        mesh: Mesh3D,
        targetTriangleCount: Int,
        preserveTexture: Boolean
    ): Mesh3D {
        
        // Build adjacency information
        val adjacency = buildAdjacencyInfo(mesh)
        
        // Calculate quadric error matrices for each vertex
        val quadrics = calculateQuadricMatrices(mesh, adjacency)
        
        // Create edge list with error costs
        val edges = createEdgeList(mesh, adjacency, quadrics)
        
        // Sort edges by error cost
        edges.sortBy { it.cost }
        
        // Perform edge collapses until target triangle count is reached
        val simplifiedMesh = performEdgeCollapses(mesh, edges, targetTriangleCount, quadrics, preserveTexture)
        
        return simplifiedMesh
    }
    
    private fun buildAdjacencyInfo(mesh: Mesh3D): VertexAdjacency {
        val adjacency = VertexAdjacency(mesh.vertexCount)
        
        // Build vertex-to-triangle and vertex-to-vertex adjacency
        for (i in mesh.indices.indices step 3) {
            val v1 = mesh.indices[i]
            val v2 = mesh.indices[i + 1]
            val v3 = mesh.indices[i + 2]
            val triangleIndex = i / 3
            
            // Add triangle to each vertex
            adjacency.addTriangleToVertex(v1, triangleIndex)
            adjacency.addTriangleToVertex(v2, triangleIndex)
            adjacency.addTriangleToVertex(v3, triangleIndex)
            
            // Add vertex neighbors
            adjacency.addVertexNeighbor(v1, v2)
            adjacency.addVertexNeighbor(v1, v3)
            adjacency.addVertexNeighbor(v2, v1)
            adjacency.addVertexNeighbor(v2, v3)
            adjacency.addVertexNeighbor(v3, v1)
            adjacency.addVertexNeighbor(v3, v2)
        }
        
        return adjacency
    }
    
    private fun calculateQuadricMatrices(mesh: Mesh3D, adjacency: VertexAdjacency): Array<QuadricMatrix> {
        val quadrics = Array(mesh.vertexCount) { QuadricMatrix() }
        
        // Calculate quadric for each triangle and add to vertices
        for (i in mesh.indices.indices step 3) {
            val v1Index = mesh.indices[i]
            val v2Index = mesh.indices[i + 1]
            val v3Index = mesh.indices[i + 2]
            
            // Get triangle vertices
            val v1 = getVertex(mesh, v1Index)
            val v2 = getVertex(mesh, v2Index)
            val v3 = getVertex(mesh, v3Index)
            
            // Calculate plane equation (ax + by + cz + d = 0)
            val plane = calculatePlaneEquation(v1, v2, v3)
            
            // Create quadric matrix for this plane
            val quadric = QuadricMatrix.fromPlane(plane)
            
            // Add to vertex quadrics
            quadrics[v1Index].add(quadric)
            quadrics[v2Index].add(quadric)
            quadrics[v3Index].add(quadric)
        }
        
        return quadrics
    }
    
    private fun createEdgeList(
        mesh: Mesh3D,
        adjacency: VertexAdjacency,
        quadrics: Array<QuadricMatrix>
    ): MutableList<Edge> {
        val edges = mutableListOf<Edge>()
        val processedEdges = mutableSetOf<Pair<Int, Int>>()
        
        for (vertexIndex in 0 until mesh.vertexCount) {
            val neighbors = adjacency.getVertexNeighbors(vertexIndex)
            
            for (neighbor in neighbors) {
                val edgePair = if (vertexIndex < neighbor) {
                    Pair(vertexIndex, neighbor)
                } else {
                    Pair(neighbor, vertexIndex)
                }
                
                if (!processedEdges.contains(edgePair)) {
                    processedEdges.add(edgePair)
                    
                    // Calculate edge collapse cost
                    val cost = calculateEdgeCollapseCost(
                        mesh, edgePair.first, edgePair.second, quadrics
                    )
                    
                    edges.add(Edge(edgePair.first, edgePair.second, cost))
                }
            }
        }
        
        return edges
    }
    
    private fun calculateEdgeCollapseCost(
        mesh: Mesh3D,
        v1Index: Int,
        v2Index: Int,
        quadrics: Array<QuadricMatrix>
    ): Float {
        val v1 = getVertex(mesh, v1Index)
        val v2 = getVertex(mesh, v2Index)
        
        // Calculate optimal collapse position
        val combinedQuadric = QuadricMatrix().apply {
            add(quadrics[v1Index])
            add(quadrics[v2Index])
        }
        
        // Try to find optimal position by solving quadric equation
        val optimalPos = combinedQuadric.findOptimalPosition()
            ?: floatArrayOf((v1[0] + v2[0]) / 2f, (v1[1] + v2[1]) / 2f, (v1[2] + v2[2]) / 2f)
        
        // Calculate error at optimal position
        return combinedQuadric.evaluateError(optimalPos)
    }
    
    private fun performEdgeCollapses(
        mesh: Mesh3D,
        edges: MutableList<Edge>,
        targetTriangleCount: Int,
        quadrics: Array<QuadricMatrix>,
        preserveTexture: Boolean
    ): Mesh3D {
        
        // Create mutable copies of mesh data
        val vertices = mesh.vertices.toMutableList()
        val indices = mesh.indices.toMutableList()
        val normals = mesh.normals?.toMutableList()
        val textureCoords = mesh.textureCoordinates?.toMutableList()
        
        val removedVertices = mutableSetOf<Int>()
        val removedTriangles = mutableSetOf<Int>()
        
        var currentTriangleCount = mesh.triangleCount
        var edgeIndex = 0
        
        while (currentTriangleCount > targetTriangleCount && edgeIndex < edges.size) {
            val edge = edges[edgeIndex]
            edgeIndex++
            
            // Skip if vertices already removed
            if (removedVertices.contains(edge.v1) || removedVertices.contains(edge.v2)) {
                continue
            }
            
            // Perform edge collapse
            val collapseResult = collapseEdge(
                edge, vertices, indices, normals, textureCoords,
                removedVertices, removedTriangles, preserveTexture
            )
            
            if (collapseResult > 0) {
                currentTriangleCount -= collapseResult
            }
        }
        
        // Rebuild mesh with remaining vertices and triangles
        return rebuildMesh(mesh, vertices, indices, normals, textureCoords, removedVertices, removedTriangles)
    }
    
    private fun collapseEdge(
        edge: Edge,
        vertices: MutableList<Float>,
        indices: MutableList<Int>,
        normals: MutableList<Float>?,
        textureCoords: MutableList<Float>?,
        removedVertices: MutableSet<Int>,
        removedTriangles: MutableSet<Int>,
        preserveTexture: Boolean
    ): Int {
        
        val v1Index = edge.v1
        val v2Index = edge.v2
        
        // Calculate collapse position (midpoint for simplicity)
        val newX = (vertices[v1Index * 3] + vertices[v2Index * 3]) / 2f
        val newY = (vertices[v1Index * 3 + 1] + vertices[v2Index * 3 + 1]) / 2f
        val newZ = (vertices[v1Index * 3 + 2] + vertices[v2Index * 3 + 2]) / 2f
        
        // Update v1 position to collapse position
        vertices[v1Index * 3] = newX
        vertices[v1Index * 3 + 1] = newY
        vertices[v1Index * 3 + 2] = newZ
        
        // Update normals if available
        normals?.let { norms ->
            val newNx = (norms[v1Index * 3] + norms[v2Index * 3]) / 2f
            val newNy = (norms[v1Index * 3 + 1] + norms[v2Index * 3 + 1]) / 2f
            val newNz = (norms[v1Index * 3 + 2] + norms[v2Index * 3 + 2]) / 2f
            
            // Normalize
            val length = sqrt(newNx * newNx + newNy * newNy + newNz * newNz)
            if (length > 0) {
                norms[v1Index * 3] = newNx / length
                norms[v1Index * 3 + 1] = newNy / length
                norms[v1Index * 3 + 2] = newNz / length
            }
        }
        
        // Update texture coordinates if available and preserving texture
        if (preserveTexture) {
            textureCoords?.let { uvs ->
                val newU = (uvs[v1Index * 2] + uvs[v2Index * 2]) / 2f
                val newV = (uvs[v1Index * 2 + 1] + uvs[v2Index * 2 + 1]) / 2f
                uvs[v1Index * 2] = newU
                uvs[v1Index * 2 + 1] = newV
            }
        }
        
        // Replace all occurrences of v2 with v1 in indices
        var removedTriangleCount = 0
        for (i in indices.indices) {
            if (indices[i] == v2Index) {
                indices[i] = v1Index
            }
        }
        
        // Mark degenerate triangles for removal
        for (i in indices.indices step 3) {
            val t1 = indices[i]
            val t2 = indices[i + 1]
            val t3 = indices[i + 2]
            
            if (t1 == t2 || t2 == t3 || t1 == t3) {
                val triangleIndex = i / 3
                if (!removedTriangles.contains(triangleIndex)) {
                    removedTriangles.add(triangleIndex)
                    removedTriangleCount++
                }
            }
        }
        
        // Mark v2 as removed
        removedVertices.add(v2Index)
        
        return removedTriangleCount
    }
    
    private fun rebuildMesh(
        originalMesh: Mesh3D,
        vertices: List<Float>,
        indices: List<Int>,
        normals: List<Float>?,
        textureCoords: List<Float>?,
        removedVertices: Set<Int>,
        removedTriangles: Set<Int>
    ): Mesh3D {
        
        // Create vertex mapping (old index -> new index)
        val vertexMapping = mutableMapOf<Int, Int>()
        val newVertices = mutableListOf<Float>()
        val newNormals = mutableListOf<Float>()
        val newTextureCoords = mutableListOf<Float>()
        
        var newVertexIndex = 0
        for (i in 0 until originalMesh.vertexCount) {
            if (!removedVertices.contains(i)) {
                vertexMapping[i] = newVertexIndex
                
                newVertices.add(vertices[i * 3])
                newVertices.add(vertices[i * 3 + 1])
                newVertices.add(vertices[i * 3 + 2])
                
                normals?.let { norms ->
                    newNormals.add(norms[i * 3])
                    newNormals.add(norms[i * 3 + 1])
                    newNormals.add(norms[i * 3 + 2])
                }
                
                textureCoords?.let { uvs ->
                    newTextureCoords.add(uvs[i * 2])
                    newTextureCoords.add(uvs[i * 2 + 1])
                }
                
                newVertexIndex++
            }
        }
        
        // Rebuild indices
        val newIndices = mutableListOf<Int>()
        for (i in indices.indices step 3) {
            val triangleIndex = i / 3
            if (!removedTriangles.contains(triangleIndex)) {
                val v1 = vertexMapping[indices[i]]
                val v2 = vertexMapping[indices[i + 1]]
                val v3 = vertexMapping[indices[i + 2]]
                
                if (v1 != null && v2 != null && v3 != null) {
                    newIndices.add(v1)
                    newIndices.add(v2)
                    newIndices.add(v3)
                }
            }
        }
        
        // Calculate new bounding box
        val newBoundingBox = calculateBoundingBox(newVertices.toFloatArray())
        
        // Update metadata
        val newMetadata = originalMesh.metadata.copy(
            quality = determineQuality(newVertices.size / 3, newIndices.size / 3)
        )
        
        return originalMesh.copy(
            vertices = newVertices.toFloatArray(),
            indices = newIndices.toIntArray(),
            normals = if (newNormals.isNotEmpty()) newNormals.toFloatArray() else null,
            textureCoordinates = if (newTextureCoords.isNotEmpty()) newTextureCoords.toFloatArray() else null,
            vertexCount = newVertices.size / 3,
            triangleCount = newIndices.size / 3,
            boundingBox = newBoundingBox,
            metadata = newMetadata
        )
    }
    
    private fun getVertex(mesh: Mesh3D, index: Int): FloatArray {
        return floatArrayOf(
            mesh.vertices[index * 3],
            mesh.vertices[index * 3 + 1],
            mesh.vertices[index * 3 + 2]
        )
    }
    
    private fun calculatePlaneEquation(v1: FloatArray, v2: FloatArray, v3: FloatArray): FloatArray {
        val edge1 = floatArrayOf(v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2])
        val edge2 = floatArrayOf(v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2])
        
        // Calculate normal (cross product)
        val normal = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        // Normalize
        val length = sqrt(normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2])
        if (length > 0) {
            normal[0] /= length
            normal[1] /= length
            normal[2] /= length
        }
        
        // Calculate d = -(ax + by + cz)
        val d = -(normal[0] * v1[0] + normal[1] * v1[1] + normal[2] * v1[2])
        
        return floatArrayOf(normal[0], normal[1], normal[2], d)
    }
    
    private fun calculateBoundingBox(vertices: FloatArray): Mesh3D.BoundingBox {
        if (vertices.isEmpty()) {
            return Mesh3D.BoundingBox(0f, 0f, 0f, 0f, 0f, 0f)
        }
        
        var minX = Float.MAX_VALUE
        var minY = Float.MAX_VALUE
        var minZ = Float.MAX_VALUE
        var maxX = Float.MIN_VALUE
        var maxY = Float.MIN_VALUE
        var maxZ = Float.MIN_VALUE
        
        for (i in vertices.indices step 3) {
            val x = vertices[i]
            val y = vertices[i + 1]
            val z = vertices[i + 2]
            
            if (x < minX) minX = x
            if (x > maxX) maxX = x
            if (y < minY) minY = y
            if (y > maxY) maxY = y
            if (z < minZ) minZ = z
            if (z > maxZ) maxZ = z
        }
        
        return Mesh3D.BoundingBox(minX, minY, minZ, maxX, maxY, maxZ)
    }
    
    private fun determineQuality(vertexCount: Int, triangleCount: Int): Mesh3D.MeshQuality {
        return when {
            triangleCount < 1000 -> Mesh3D.MeshQuality.LOW
            triangleCount < 10000 -> Mesh3D.MeshQuality.MEDIUM
            triangleCount < 50000 -> Mesh3D.MeshQuality.HIGH
            else -> Mesh3D.MeshQuality.ULTRA
        }
    }
    
    // Helper classes
    private data class Edge(val v1: Int, val v2: Int, val cost: Float)
    
    private class VertexAdjacency(vertexCount: Int) {
        private val triangles = Array(vertexCount) { mutableSetOf<Int>() }
        private val neighbors = Array(vertexCount) { mutableSetOf<Int>() }
        
        fun addTriangleToVertex(vertex: Int, triangle: Int) {
            triangles[vertex].add(triangle)
        }
        
        fun addVertexNeighbor(vertex: Int, neighbor: Int) {
            neighbors[vertex].add(neighbor)
        }
        
        fun getVertexNeighbors(vertex: Int): Set<Int> = neighbors[vertex]
        fun getVertexTriangles(vertex: Int): Set<Int> = triangles[vertex]
    }
    
    private class QuadricMatrix {
        private val matrix = FloatArray(16) // 4x4 matrix stored as array
        
        fun add(other: QuadricMatrix) {
            for (i in matrix.indices) {
                matrix[i] += other.matrix[i]
            }
        }
        
        fun evaluateError(vertex: FloatArray): Float {
            val x = vertex[0]
            val y = vertex[1]
            val z = vertex[2]
            val w = 1f
            
            return (matrix[0] * x * x + matrix[5] * y * y + matrix[10] * z * z + matrix[15] * w * w +
                    2 * (matrix[1] * x * y + matrix[2] * x * z + matrix[3] * x * w +
                            matrix[6] * y * z + matrix[7] * y * w + matrix[11] * z * w))
        }
        
        fun findOptimalPosition(): FloatArray? {
            // Simplified - return null to use midpoint
            return null
        }
        
        companion object {
            fun fromPlane(plane: FloatArray): QuadricMatrix {
                val quadric = QuadricMatrix()
                val a = plane[0]
                val b = plane[1]
                val c = plane[2]
                val d = plane[3]
                
                quadric.matrix[0] = a * a
                quadric.matrix[1] = a * b
                quadric.matrix[2] = a * c
                quadric.matrix[3] = a * d
                quadric.matrix[5] = b * b
                quadric.matrix[6] = b * c
                quadric.matrix[7] = b * d
                quadric.matrix[10] = c * c
                quadric.matrix[11] = c * d
                quadric.matrix[15] = d * d
                
                return quadric
            }
        }
    }
}
