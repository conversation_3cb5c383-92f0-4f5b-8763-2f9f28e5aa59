{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "43,46,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4211,4527,5123,5240,5379,5526,5657,5787,5931,6032,6166,6310", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "4310,4616,5235,5374,5521,5652,5782,5926,6027,6161,6305,6428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-pt\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,291,409,535", "endColumns": "48,51,117,125,86", "endOffsets": "238,290,408,534,621"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,158,214,336,466", "endColumns": "52,55,121,129,90", "endOffsets": "153,209,331,461,552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "34,35,36,37,38,39,40,74", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3295,3392,3494,3593,3693,3800,3910,7501", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3387,3489,3588,3688,3795,3905,4025,7597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "64,65", "startColumns": "4,4", "startOffsets": "6613,6725", "endColumns": "111,119", "endOffsets": "6720,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "557,677,783,890,979,1080,1199,1284,1364,1455,1548,1643,1737,1837,1930,2025,2120,2211,2302,2387,2494,2605,2707,2815,2923,3033,3195,7339", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "672,778,885,974,1075,1194,1279,1359,1450,1543,1638,1732,1832,1925,2020,2115,2206,2297,2382,2489,2600,2702,2810,2918,3028,3190,3290,7420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,150,236,333,432,518,601,698,789,876,948,1017,1102,1192,1268", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,66", "endOffsets": "145,231,328,427,513,596,693,784,871,943,1012,1097,1187,1263,1330"}, "to": {"startLines": "41,42,45,47,48,62,63,66,67,68,69,70,71,73,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4030,4125,4430,4621,4720,6433,6516,6845,6936,7023,7095,7164,7249,7425,7602", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,66", "endOffsets": "4120,4206,4522,4715,4801,6511,6608,6931,7018,7090,7159,7244,7334,7496,7664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "44,49,50,51", "startColumns": "4,4,4,4", "startOffsets": "4315,4806,4905,5017", "endColumns": "114,98,111,105", "endOffsets": "4425,4900,5012,5118"}}]}]}