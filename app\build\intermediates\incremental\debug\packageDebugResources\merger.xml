<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res"><file name="bg_quality_high" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\bg_quality_high.xml" qualifiers="" type="drawable"/><file name="bg_quality_low" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\bg_quality_low.xml" qualifiers="" type="drawable"/><file name="bg_quality_medium" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\bg_quality_medium.xml" qualifiers="" type="drawable"/><file name="bg_quality_ultra" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\bg_quality_ultra.xml" qualifiers="" type="drawable"/><file name="circle_background_white" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\circle_background_white.xml" qualifiers="" type="drawable"/><file name="circle_indicator" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\circle_indicator.xml" qualifiers="" type="drawable"/><file name="ic_3d_model_placeholder" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_3d_model_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_3d_scanner" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_3d_scanner.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_empty_gallery" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_empty_gallery.xml" qualifiers="" type="drawable"/><file name="ic_export" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_export.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_fingerprint" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_fingerprint.xml" qualifiers="" type="drawable"/><file name="ic_gallery" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_gallery.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_pin" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_pin.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_quality_high" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_quality_high.xml" qualifiers="" type="drawable"/><file name="ic_quality_low" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_quality_low.xml" qualifiers="" type="drawable"/><file name="ic_quality_medium" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_quality_medium.xml" qualifiers="" type="drawable"/><file name="ic_quality_ultra" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_quality_ultra.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_sort" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_sort.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ic_texture" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_texture.xml" qualifiers="" type="drawable"/><file name="ic_view_toggle" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_view_toggle.xml" qualifiers="" type="drawable"/><file name="ic_wireframe" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\ic_wireframe.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="activity_authentication" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\activity_authentication.xml" qualifiers="" type="layout"/><file name="activity_gallery" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\activity_gallery.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_model_viewer" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\activity_model_viewer.xml" qualifiers="" type="layout"/><file name="activity_scanning" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\activity_scanning.xml" qualifiers="" type="layout"/><file name="item_scan_grid" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\item_scan_grid.xml" qualifiers="" type="layout"/><file name="item_scan_list" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\layout\item_scan_list.xml" qualifiers="" type="layout"/><file name="gallery_menu" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\menu\gallery_menu.xml" qualifiers="" type="menu"/><file name="model_viewer_menu" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\menu\model_viewer_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_blue">#2196F3</color><color name="primary_blue_dark">#1976D2</color><color name="primary_blue_light">#BBDEFB</color><color name="secondary_teal">#009688</color><color name="secondary_teal_dark">#00695C</color><color name="secondary_teal_light">#B2DFDB</color><color name="accent_orange">#FF9800</color><color name="accent_orange_dark">#F57C00</color><color name="accent_orange_light">#FFE0B2</color><color name="accent_green">#4CAF50</color><color name="success_green">#4CAF50</color><color name="warning_orange">#FF9800</color><color name="error_red">#F44336</color><color name="info_blue">#2196F3</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="gray_light">#F5F5F5</color><color name="gray_medium">#9E9E9E</color><color name="gray_dark">#424242</color><color name="surface">#FFFFFF</color><color name="background_light">#FAFAFA</color><color name="background_dark">#121212</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="text_disabled">#E0E0E0</color><color name="surface_light">#FFFFFF</color><color name="surface_dark">#1E1E1E</color><color name="text_primary_light">#212121</color><color name="text_primary_dark">#FFFFFF</color><color name="text_secondary_light">#757575</color><color name="text_secondary_dark">#AAAAAA</color><color name="stroke_light">#E0E0E0</color><color name="stroke_dark">#333333</color><color name="camera_overlay">#80000000</color><color name="scan_progress">#4CAF50</color><color name="quality_low">#F44336</color><color name="quality_medium">#FF9800</color><color name="quality_high">#4CAF50</color><color name="quality_ultra">#9C27B0</color></file><file path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">3D Scanner</string><string name="main_title">3D Scanner</string><string name="start_scanning">Start Scanning</string><string name="view_gallery">View Gallery</string><string name="settings">Settings</string><string name="version">Version %s</string><string name="auth_title">Secure Access</string><string name="auth_subtitle">Please authenticate to access your 3D scans</string><string name="use_biometric">Use Fingerprint/Face ID</string><string name="use_pin">Use PIN</string><string name="enter_pin">Enter PIN</string><string name="ok">OK</string><string name="scanning_title">3D Scanning</string><string name="camera_view">Camera</string><string name="depth_view">Depth</string><string name="scan_progress">Progress: %d%%</string><string name="start_scan">Start</string><string name="pause_scan">Pause</string><string name="resume_scan">Resume</string><string name="stop_scan">Stop</string><string name="back">Back</string><string name="new_scan">New Scan</string><string name="cancel">Cancel</string><string name="delete">Delete</string><string name="share">Share</string><string name="export">Export</string><string name="rename">Rename</string><string name="error_camera_permission">Camera permission is required for scanning</string><string name="error_scanning_failed">Scanning failed. Please try again.</string><string name="error_save_failed">Failed to save scan</string><string name="error_load_failed">Failed to load scan</string><string name="scan_saved">Scan saved successfully</string><string name="scan_deleted">Scan deleted</string><string name="scan_exported">Scan exported successfully</string></file><file path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\values\styles.xml" qualifiers=""><style name="Theme.Scanner3D" parent="Theme.MaterialComponents.DayNight">
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary_teal</item>
        <item name="colorSecondaryVariant">@color/secondary_teal_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorError">@color/error_red</item>
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:navigationBarColor">@color/primary_blue_dark</item>
    </style><style name="Theme.Scanner3D.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style><style name="Scanner3D.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style><style name="Scanner3D.Button.Primary">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Scanner3D.Button.Secondary">
        <item name="backgroundTint">@color/gray_light</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="Scanner3D.Button.Danger">
        <item name="backgroundTint">@color/error_red</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Scanner3D.Text" parent="android:Widget.TextView">
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="Scanner3D.Text.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="Scanner3D.Text.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="Scanner3D.Text.Body">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="Scanner3D.Text.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style><style name="Scanner3D.Card" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="Scanner3D.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Desktop\3dscanner\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\3dscanner\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>