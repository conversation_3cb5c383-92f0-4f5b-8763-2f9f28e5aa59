{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,243,295,438,590", "endColumns": "48,51,142,151,107", "endOffsets": "242,294,437,589,697"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "323,376,432,579,735", "endColumns": "52,55,146,155,111", "endOffsets": "371,427,574,730,842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11373,11485", "endColumns": "111,119", "endOffsets": "11480,11600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2917,3004,3084,3140,3191,3257,3332,3412,3499,3578,3651,3728,3801,3875,3982,4075,4152,4245,4343,4417,4498,4597,4650,4734,4800,4889,4977,5039,5103,5166,5234,5350,5458,5565,5667,5727,5782", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2912,2999,3079,3135,3186,3252,3327,3407,3494,3573,3646,3723,3796,3870,3977,4070,4147,4240,4338,4412,4493,4592,4645,4729,4795,4884,4972,5034,5098,5161,5229,5345,5453,5560,5662,5722,5777,5863"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3578,3659,3739,3821,3920,4748,4851,4971,5263,5327,6762,6841,6906,6996,7060,7128,7190,7263,7327,7381,7507,7565,7627,7681,7757,7900,7987,8069,8208,8290,8372,8508,8595,8675,8731,8782,8848,8923,9003,9090,9169,9242,9319,9392,9466,9573,9666,9743,9836,9934,10008,10089,10188,10241,10325,10391,10480,10568,10630,10694,10757,10825,10941,11049,11156,11258,11318,11605", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "318,3654,3734,3816,3915,4011,4846,4966,5047,5322,5414,6836,6901,6991,7055,7123,7185,7258,7322,7376,7502,7560,7622,7676,7752,7895,7982,8064,8203,8285,8367,8503,8590,8670,8726,8777,8843,8918,8998,9085,9164,9237,9314,9387,9461,9568,9661,9738,9831,9929,10003,10084,10183,10236,10320,10386,10475,10563,10625,10689,10752,10820,10936,11044,11151,11253,11313,11368,11686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "847,955,1061,1168,1257,1358,1476,1561,1641,1733,1827,1924,2018,2117,2211,2307,2402,2494,2586,2671,2778,2889,2991,3099,3207,3314,3479,11691", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "950,1056,1163,1252,1353,1471,1556,1636,1728,1822,1919,2013,2112,2206,2302,2397,2489,2581,2666,2773,2884,2986,3094,3202,3309,3474,3573,11772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4016,4113,4215,4314,4414,4521,4627,11777", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "4108,4210,4309,4409,4516,4622,4743,11873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,266,395,534,681,816,945,1092,1194,1334,1483", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "166,261,390,529,676,811,940,1087,1189,1329,1478,1604"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5052,5168,5419,5548,5687,5834,5969,6098,6245,6347,6487,6636", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "5163,5258,5543,5682,5829,5964,6093,6240,6342,6482,6631,6757"}}]}]}