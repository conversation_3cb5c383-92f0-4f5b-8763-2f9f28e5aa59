R_DEF: Internal format may change without notice
local
color accent_green
color accent_orange
color accent_orange_dark
color accent_orange_light
color background_dark
color background_light
color black
color camera_overlay
color error_red
color gray_dark
color gray_light
color gray_medium
color info_blue
color primary_blue
color primary_blue_dark
color primary_blue_light
color quality_high
color quality_low
color quality_medium
color quality_ultra
color scan_progress
color secondary_teal
color secondary_teal_dark
color secondary_teal_light
color stroke_dark
color stroke_light
color success_green
color surface_dark
color surface_light
color text_primary_dark
color text_primary_light
color text_secondary_dark
color text_secondary_light
color warning_orange
color white
drawable bg_quality_high
drawable bg_quality_low
drawable bg_quality_medium
drawable bg_quality_ultra
drawable circle_background_white
drawable circle_indicator
drawable ic_3d_model_placeholder
drawable ic_3d_scanner
drawable ic_add
drawable ic_arrow_back
drawable ic_camera
drawable ic_delete
drawable ic_edit
drawable ic_empty_gallery
drawable ic_export
drawable ic_filter
drawable ic_fingerprint
drawable ic_gallery
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_palette
drawable ic_pause
drawable ic_pin
drawable ic_play_arrow
drawable ic_quality_high
drawable ic_quality_low
drawable ic_quality_medium
drawable ic_quality_ultra
drawable ic_refresh
drawable ic_search
drawable ic_settings
drawable ic_share
drawable ic_sort
drawable ic_stop
drawable ic_texture
drawable ic_view_toggle
drawable ic_wireframe
drawable rounded_background
drawable search_background
id action_delete
id action_edit
id action_export
id action_filter
id action_search
id action_settings
id action_sort
id action_view_toggle
id btnResetView
id btnToggleTexture
id btnToggleWireframe
id btn_back
id btn_camera_view
id btn_depth_view
id btn_pause_scan
id btn_resume_scan
id btn_scan_settings
id btn_settings
id btn_start_scan
id btn_start_scanning
id btn_stop_scan
id btn_submit_pin
id btn_use_biometric
id btn_use_pin
id btn_view_gallery
id chip_colors
id chip_texture
id chip_uploaded
id controlButtons
id depth_view
id et_pin
id fabInfo
id fabShare
id fab_new_scan
id iconColors
id iconTexture
id indicator_scanning
id infoPanel
id iv_app_logo
id iv_auth_logo
id iv_quality_indicator
id iv_texture_indicator
id iv_thumbnail
id ll_auth_buttons
id ll_info
id ll_main_buttons
id ll_pin_input
id ll_processing_status
id ll_scan_controls
id ll_scan_info
id ll_stats
id ll_top_bar
id ll_view_toggle
id pointCloudView
id point_cloud_view
id preview_view
id progressBar
id progress_bar
id progress_bar_processing
id progress_bar_scan
id qualityIndicator
id recycler_view_scans
id search_view
id swipe_refresh_layout
id toolbar
id tvFileSize
id tvQuality
id tvScanDate
id tvScanName
id tvTriangleCount
id tvVertexCount
id tv_app_title
id tv_auth_subtitle
id tv_auth_title
id tv_empty_state
id tv_file_size
id tv_frame_count
id tv_pin_label
id tv_point_count
id tv_processing_stage
id tv_quality
id tv_scan_count
id tv_scan_date
id tv_scan_description
id tv_scan_duration
id tv_scan_name
id tv_scan_progress
id tv_scan_status
id tv_scan_title
id tv_time_remaining
id tv_total_size
id tv_triangle_count
id tv_version
id tv_vertex_count
layout activity_authentication
layout activity_gallery
layout activity_main
layout activity_model_viewer
layout activity_scanning
layout item_scan_grid
layout item_scan_list
menu gallery_menu
menu model_viewer_menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string auth_subtitle
string auth_title
string back
string camera_view
string cancel
string delete
string depth_view
string enter_pin
string error_camera_permission
string error_load_failed
string error_save_failed
string error_scanning_failed
string export
string main_title
string new_scan
string ok
string pause_scan
string rename
string resume_scan
string scan_deleted
string scan_exported
string scan_progress
string scan_saved
string scanning_title
string settings
string share
string start_scan
string start_scanning
string stop_scan
string use_biometric
string use_pin
string version
string view_gallery
style Scanner3D.Button
style Scanner3D.Button.Danger
style Scanner3D.Button.Primary
style Scanner3D.Button.Secondary
style Scanner3D.Card
style Scanner3D.Text
style Scanner3D.Text.Body
style Scanner3D.Text.Caption
style Scanner3D.Text.Headline
style Scanner3D.Text.Title
style Scanner3D.Toolbar
style Theme.Scanner3D
style Theme.Scanner3D.NoActionBar
xml backup_rules
xml data_extraction_rules
xml file_paths
