package com.scanner3d.app.ui.model

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.scanner3d.app.R
import com.scanner3d.app.databinding.ActivityModelViewerBinding
import com.scanner3d.app.viewmodel.ModelViewerViewModel
import com.scanner3d.app.ui.custom.PointCloudView
import com.scanner3d.app.data.model.ScanEntity

class ModelViewerActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityModelViewerBinding
    private val viewModel: ModelViewerViewModel by viewModels()
    
    private var scanId: String? = null
    private var currentScan: ScanEntity? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityModelViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupObservers()
        setupUI()
        
        // Get scan ID from intent
        scanId = intent.getStringExtra("scan_id")
        scanId?.let { id ->
            viewModel.loadScan(id)
        } ?: run {
            Toast.makeText(this, "Invalid scan ID", Toast.LENGTH_SHORT).show()
            finish()
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "3D Model Viewer"
        }
    }
    
    private fun setupObservers() {
        viewModel.scanData.observe(this, Observer { scan ->
            scan?.let {
                currentScan = it
                updateUI(it)
                loadModel(it)
            }
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
        })
        
        viewModel.errorMessage.observe(this, Observer { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        })
        
        viewModel.pointCloudData.observe(this, Observer { pointCloud ->
            pointCloud?.let {
                binding.pointCloudView.updatePointCloud(it)
            }
        })
    }
    
    private fun setupUI() {
        // Setup point cloud view
        binding.pointCloudView.setOnTouchListener { _, event ->
            // Handle touch events for rotation/zoom
            true
        }
        
        // Setup control buttons
        binding.btnResetView.setOnClickListener {
            binding.pointCloudView.resetView()
        }
        
        binding.btnToggleWireframe.setOnClickListener {
            binding.pointCloudView.toggleWireframe()
        }
        
        binding.btnToggleTexture.setOnClickListener {
            binding.pointCloudView.toggleTexture()
        }
        
        // Setup info panel
        binding.fabInfo.setOnClickListener {
            showScanInfo()
        }
        
        // Setup sharing
        binding.fabShare.setOnClickListener {
            shareScan()
        }
    }
    
    private fun updateUI(scan: ScanEntity) {
        binding.apply {
            // Update scan info
            tvScanName.text = scan.name
            tvScanDate.text = android.text.format.DateFormat.format("MMM dd, yyyy", scan.createdAt)
            tvVertexCount.text = "${scan.vertexCount} vertices"
            tvTriangleCount.text = "${scan.triangleCount} triangles"
            tvFileSize.text = formatFileSize(scan.fileSize)
            tvQuality.text = scan.quality
            
            // Update quality indicator
            val qualityColor = when (scan.quality.lowercase()) {
                "low" -> R.color.quality_low
                "medium" -> R.color.quality_medium
                "high" -> R.color.quality_high
                "ultra" -> R.color.quality_ultra
                else -> R.color.quality_medium
            }
            qualityIndicator.setBackgroundColor(getColor(qualityColor))
            
            // Update texture/color indicators
            iconTexture.visibility = if (scan.hasTexture) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
            
            iconColors.visibility = if (scan.hasColors) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
        }
    }
    
    private fun loadModel(scan: ScanEntity) {
        viewModel.loadPointCloudData(scan.filePath)
        
        // Load thumbnail if available
        scan.thumbnailPath?.let { thumbnailPath ->
            // Load thumbnail into ImageView
            // Implementation depends on image loading library (Glide, etc.)
        }
    }
    
    private fun showScanInfo() {
        currentScan?.let { scan ->
            val info = buildString {
                appendLine("Scan Information")
                appendLine("================")
                appendLine("Name: ${scan.name}")
                appendLine("Created: ${android.text.format.DateFormat.format("MMM dd, yyyy HH:mm", scan.createdAt)}")
                appendLine("Duration: ${formatDuration(scan.scanDuration)}")
                appendLine("Format: ${scan.format}")
                appendLine("Quality: ${scan.quality}")
                appendLine("File Size: ${formatFileSize(scan.fileSize)}")
                appendLine("Vertices: ${scan.vertexCount}")
                appendLine("Triangles: ${scan.triangleCount}")
                appendLine("Has Texture: ${if (scan.hasTexture) "Yes" else "No"}")
                appendLine("Has Colors: ${if (scan.hasColors) "Yes" else "No"}")
                appendLine()
                appendLine("Bounding Box:")
                appendLine("X: ${scan.boundingBoxMinX} to ${scan.boundingBoxMaxX}")
                appendLine("Y: ${scan.boundingBoxMinY} to ${scan.boundingBoxMaxY}")
                appendLine("Z: ${scan.boundingBoxMinZ} to ${scan.boundingBoxMaxZ}")
            }
            
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Scan Information")
                .setMessage(info)
                .setPositiveButton("OK", null)
                .show()
        }
    }
    
    private fun shareScan() {
        currentScan?.let { scan ->
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "3D Scan: ${scan.name}")
                putExtra(Intent.EXTRA_TEXT, "Check out this 3D scan I created with Scanner3D!\n\n" +
                        "Name: ${scan.name}\n" +
                        "Quality: ${scan.quality}\n" +
                        "Vertices: ${scan.vertexCount}\n" +
                        "Triangles: ${scan.triangleCount}")
            }
            startActivity(Intent.createChooser(shareIntent, "Share 3D Scan"))
        }
    }
    
    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1.0 -> String.format("%.1f GB", gb)
            mb >= 1.0 -> String.format("%.1f MB", mb)
            kb >= 1.0 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
    
    private fun formatDuration(milliseconds: Long): String {
        val seconds = milliseconds / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            minutes > 0 -> String.format("%d:%02d", minutes, seconds % 60)
            else -> "${seconds}s"
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.model_viewer_menu, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_export -> {
                exportModel()
                true
            }
            R.id.action_edit -> {
                editScan()
                true
            }
            R.id.action_delete -> {
                deleteScan()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun exportModel() {
        currentScan?.let { scan ->
            viewModel.exportScan(scan)
            Toast.makeText(this, "Exporting ${scan.name}...", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun editScan() {
        // TODO: Implement scan editing
        Toast.makeText(this, "Edit functionality coming soon", Toast.LENGTH_SHORT).show()
    }
    
    private fun deleteScan() {
        currentScan?.let { scan ->
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Delete Scan")
                .setMessage("Are you sure you want to delete '${scan.name}'? This action cannot be undone.")
                .setPositiveButton("Delete") { _, _ ->
                    viewModel.deleteScan(scan.id)
                    finish()
                }
                .setNegativeButton("Cancel", null)
                .show()
        }
    }
}
