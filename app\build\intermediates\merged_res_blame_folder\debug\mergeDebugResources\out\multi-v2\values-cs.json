{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "848,955,1057,1167,1253,1358,1475,1553,1629,1720,1813,1908,2002,2096,2189,2284,2381,2472,2563,2647,2751,2863,2962,3068,3179,3281,3444,11447", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "950,1052,1162,1248,1353,1470,1548,1624,1715,1808,1903,1997,2091,2184,2279,2376,2467,2558,2642,2746,2858,2957,3063,3174,3276,3439,3537,11525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "45,46,47,48,49,50,51,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3970,4068,4170,4271,4370,4475,4582,11530", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4063,4165,4266,4365,4470,4577,4696,11626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,290,402,516", "endColumns": "46,52,111,113,83", "endOffsets": "236,289,401,515,599"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "418,469,526,642,760", "endColumns": "50,56,115,117,87", "endOffsets": "464,521,637,755,843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\857ae526a5ee6c76b63616ddc978cbae\\transformed\\navigation-ui-2.7.5\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11133,11241", "endColumns": "107,126", "endOffsets": "11236,11363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,374,518,648,772,906,1037,1135,1270,1407", "endColumns": "105,89,122,143,129,123,133,130,97,134,136,118", "endOffsets": "156,246,369,513,643,767,901,1032,1130,1265,1402,1521"}, "to": {"startLines": "55,56,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5000,5106,5360,5483,5627,5757,5881,6015,6146,6244,6379,6516", "endColumns": "105,89,122,143,129,123,133,130,97,134,136,118", "endOffsets": "5101,5191,5478,5622,5752,5876,6010,6141,6239,6374,6511,6630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2939,3027,3107,3161,3212,3278,3350,3427,3511,3592,3664,3741,3815,3886,3991,4079,4150,4243,4338,4412,4486,4582,4634,4717,4784,4870,4958,5020,5084,5147,5215,5325,5431,5530,5644,5702,5757", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2934,3022,3102,3156,3207,3273,3345,3422,3506,3587,3659,3736,3810,3881,3986,4074,4145,4238,4333,4407,4481,4577,4629,4712,4779,4865,4953,5015,5079,5142,5210,5320,5426,5525,5639,5697,5752,5831"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3542,3620,3698,3775,3878,4701,4793,4919,5196,5261,6635,6711,6772,6861,6925,6992,7046,7114,7174,7228,7345,7405,7467,7521,7593,7715,7799,7891,8028,8106,8188,8315,8403,8483,8537,8588,8654,8726,8803,8887,8968,9040,9117,9191,9262,9367,9455,9526,9619,9714,9788,9862,9958,10010,10093,10160,10246,10334,10396,10460,10523,10591,10701,10807,10906,11020,11078,11368", "endLines": "7,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "413,3615,3693,3770,3873,3965,4788,4914,4995,5256,5355,6706,6767,6856,6920,6987,7041,7109,7169,7223,7340,7400,7462,7516,7588,7710,7794,7886,8023,8101,8183,8310,8398,8478,8532,8583,8649,8721,8798,8882,8963,9035,9112,9186,9257,9362,9450,9521,9614,9709,9783,9857,9953,10005,10088,10155,10241,10329,10391,10455,10518,10586,10696,10802,10901,11015,11073,11128,11442"}}]}]}