package com.scanner3d.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import android.util.Log
import com.scanner3d.app.data.model.ScanEntity
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class FileManager(private val context: Context) {
    
    companion object {
        private const val TAG = "FileManager"
        private const val SCANS_DIRECTORY = "Scanner3D/Scans"
        private const val THUMBNAILS_DIRECTORY = "Scanner3D/Thumbnails"
        private const val EXPORTS_DIRECTORY = "Scanner3D/Exports"
        private const val CACHE_DIRECTORY = "Scanner3D/Cache"
    }
    
    private val scansDir: File by lazy {
        File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), SCANS_DIRECTORY).apply {
            mkdirs()
        }
    }
    
    private val thumbnailsDir: File by lazy {
        File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), THUMBNAILS_DIRECTORY).apply {
            mkdirs()
        }
    }
    
    private val exportsDir: File by lazy {
        File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), EXPORTS_DIRECTORY).apply {
            mkdirs()
        }
    }
    
    private val cacheDir: File by lazy {
        File(context.cacheDir, CACHE_DIRECTORY).apply {
            mkdirs()
        }
    }
    
    fun createScanFile(scanId: String, format: String): File {
        val extension = when (format.uppercase()) {
            "OBJ" -> "obj"
            "STL" -> "stl"
            "PLY" -> "ply"
            "GLTF" -> "gltf"
            else -> "obj"
        }
        return File(scansDir, "$scanId.$extension")
    }
    
    fun createThumbnailFile(scanId: String): File {
        return File(thumbnailsDir, "$scanId.jpg")
    }
    
    fun createExportFile(scanId: String, format: String): File {
        val extension = when (format.uppercase()) {
            "OBJ" -> "obj"
            "STL" -> "stl"
            "PLY" -> "ply"
            "GLTF" -> "gltf"
            else -> "obj"
        }
        return File(exportsDir, "${scanId}_export.$extension")
    }
    
    fun createCacheFile(filename: String): File {
        return File(cacheDir, filename)
    }
    
    fun saveBitmap(bitmap: Bitmap, file: File): Boolean {
        return try {
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 85, out)
            }
            Log.d(TAG, "Bitmap saved to: ${file.absolutePath}")
            true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to save bitmap", e)
            false
        }
    }
    
    fun deleteScanFiles(scan: ScanEntity) {
        try {
            // Delete main scan file
            File(scan.filePath).delete()
            
            // Delete thumbnail
            scan.thumbnailPath?.let { thumbnailPath ->
                File(thumbnailPath).delete()
            }
            
            // Delete any associated texture files
            val scanFile = File(scan.filePath)
            val parentDir = scanFile.parentFile
            val baseName = scanFile.nameWithoutExtension
            
            // Look for texture files with the same base name
            parentDir?.listFiles { _, name ->
                name.startsWith(baseName) && (name.endsWith(".jpg") || name.endsWith(".png") || name.endsWith(".mtl"))
            }?.forEach { it.delete() }
            
            Log.d(TAG, "Deleted files for scan: ${scan.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete scan files", e)
        }
    }
    
    fun getStorageInfo(): StorageInfo {
        val totalSpace = scansDir.totalSpace
        val freeSpace = scansDir.freeSpace
        val usedSpace = totalSpace - freeSpace
        
        val scansDirSize = calculateDirectorySize(scansDir)
        val thumbnailsDirSize = calculateDirectorySize(thumbnailsDir)
        val cacheDirSize = calculateDirectorySize(cacheDir)
        
        return StorageInfo(
            totalSpace = totalSpace,
            freeSpace = freeSpace,
            usedSpace = usedSpace,
            scansDirSize = scansDirSize,
            thumbnailsDirSize = thumbnailsDirSize,
            cacheDirSize = cacheDirSize
        )
    }
    
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        try {
            directory.walkTopDown().forEach { file ->
                if (file.isFile) {
                    size += file.length()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to calculate directory size", e)
        }
        return size
    }
    
    fun clearCache(): Boolean {
        return try {
            cacheDir.deleteRecursively()
            cacheDir.mkdirs()
            Log.d(TAG, "Cache cleared successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear cache", e)
            false
        }
    }
    
    fun ensureDirectoriesExist() {
        listOf(scansDir, thumbnailsDir, exportsDir, cacheDir).forEach { dir ->
            if (!dir.exists()) {
                dir.mkdirs()
            }
        }
    }
    
    fun isExternalStorageWritable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }
    
    fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return state == Environment.MEDIA_MOUNTED || state == Environment.MEDIA_MOUNTED_READ_ONLY
    }
    
    fun getFileExtension(filename: String): String {
        return filename.substringAfterLast('.', "")
    }
    
    fun getFileNameWithoutExtension(filename: String): String {
        return filename.substringBeforeLast('.')
    }
    
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    data class StorageInfo(
        val totalSpace: Long,
        val freeSpace: Long,
        val usedSpace: Long,
        val scansDirSize: Long,
        val thumbnailsDirSize: Long,
        val cacheDirSize: Long
    ) {
        val usagePercentage: Float
            get() = if (totalSpace > 0) (usedSpace.toFloat() / totalSpace.toFloat()) * 100f else 0f
        
        val appUsagePercentage: Float
            get() = if (totalSpace > 0) ((scansDirSize + thumbnailsDirSize + cacheDirSize).toFloat() / totalSpace.toFloat()) * 100f else 0f
    }
}
