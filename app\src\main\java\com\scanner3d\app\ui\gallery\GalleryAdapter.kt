package com.scanner3d.app.ui.gallery

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.scanner3d.app.R
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.databinding.ItemScanGridBinding
import com.scanner3d.app.databinding.ItemScanListBinding
import java.text.SimpleDateFormat
import java.util.*

class GalleryAdapter(
    private val onItemClick: (ScanEntity) -> Unit,
    private val onItemLongClick: (ScanEntity) -> Unit
) : ListAdapter<ScanEntity, RecyclerView.ViewHolder>(ScanDiffCallback()) {
    
    enum class ViewMode { GRID, LIST }
    
    private var viewMode = ViewMode.GRID
    
    companion object {
        private const val VIEW_TYPE_GRID = 0
        private const val VIEW_TYPE_LIST = 1
    }
    
    fun setViewMode(mode: ViewMode) {
        viewMode = mode
        notifyDataSetChanged()
    }
    
    override fun getItemViewType(position: Int): Int {
        return when (viewMode) {
            ViewMode.GRID -> VIEW_TYPE_GRID
            ViewMode.LIST -> VIEW_TYPE_LIST
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_GRID -> {
                val binding = ItemScanGridBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                GridViewHolder(binding)
            }
            VIEW_TYPE_LIST -> {
                val binding = ItemScanListBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ListViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val scan = getItem(position)
        when (holder) {
            is GridViewHolder -> holder.bind(scan)
            is ListViewHolder -> holder.bind(scan)
        }
    }
    
    inner class GridViewHolder(private val binding: ItemScanGridBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(scan: ScanEntity) {
            binding.apply {
                tvScanName.text = scan.name
                tvScanDate.text = formatDate(scan.createdAt)
                tvFileSize.text = scan.fileSizeFormatted
                
                // Load thumbnail if available
                if (scan.thumbnailPath != null) {
                    Glide.with(ivThumbnail.context)
                        .load(scan.thumbnailPath)
                        .placeholder(R.drawable.ic_3d_model_placeholder)
                        .error(R.drawable.ic_3d_model_placeholder)
                        .into(ivThumbnail)
                } else {
                    ivThumbnail.setImageResource(R.drawable.ic_3d_model_placeholder)
                }
                
                // Quality indicator
                ivQualityIndicator.setImageResource(getQualityIcon(scan.quality))
                
                // Texture indicator
                ivTextureIndicator.visibility = if (scan.hasTexture) View.VISIBLE else View.GONE
                
                // Click listeners
                root.setOnClickListener { onItemClick(scan) }
                root.setOnLongClickListener { 
                    onItemLongClick(scan)
                    true
                }
            }
        }
    }
    
    inner class ListViewHolder(private val binding: ItemScanListBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(scan: ScanEntity) {
            binding.apply {
                tvScanName.text = scan.name
                tvScanDescription.text = scan.description ?: "No description"
                tvScanDate.text = formatDate(scan.createdAt)
                tvFileSize.text = scan.fileSizeFormatted
                tvScanDuration.text = scan.scanDurationFormatted
                tvVertexCount.text = "${scan.vertexCount} vertices"
                tvTriangleCount.text = "${scan.triangleCount} triangles"
                
                // Load thumbnail if available
                if (scan.thumbnailPath != null) {
                    Glide.with(ivThumbnail.context)
                        .load(scan.thumbnailPath)
                        .placeholder(R.drawable.ic_3d_model_placeholder)
                        .error(R.drawable.ic_3d_model_placeholder)
                        .into(ivThumbnail)
                } else {
                    ivThumbnail.setImageResource(R.drawable.ic_3d_model_placeholder)
                }
                
                // Quality badge
                tvQuality.text = scan.quality
                tvQuality.setBackgroundResource(getQualityBackground(scan.quality))
                
                // Feature indicators
                chipTexture.visibility = if (scan.hasTexture) View.VISIBLE else View.GONE
                chipColors.visibility = if (scan.hasColors) View.VISIBLE else View.GONE
                chipUploaded.visibility = if (scan.isUploaded) View.VISIBLE else View.GONE
                
                // Click listeners
                root.setOnClickListener { onItemClick(scan) }
                root.setOnLongClickListener { 
                    onItemLongClick(scan)
                    true
                }
            }
        }
    }
    
    private fun formatDate(timestamp: Long): String {
        val sdf = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    private fun getQualityIcon(quality: String): Int {
        return when (quality) {
            "LOW" -> R.drawable.ic_quality_low
            "MEDIUM" -> R.drawable.ic_quality_medium
            "HIGH" -> R.drawable.ic_quality_high
            "ULTRA" -> R.drawable.ic_quality_ultra
            else -> R.drawable.ic_quality_medium
        }
    }
    
    private fun getQualityBackground(quality: String): Int {
        return when (quality) {
            "LOW" -> R.drawable.bg_quality_low
            "MEDIUM" -> R.drawable.bg_quality_medium
            "HIGH" -> R.drawable.bg_quality_high
            "ULTRA" -> R.drawable.bg_quality_ultra
            else -> R.drawable.bg_quality_medium
        }
    }
    
    class ScanDiffCallback : DiffUtil.ItemCallback<ScanEntity>() {
        override fun areItemsTheSame(oldItem: ScanEntity, newItem: ScanEntity): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: ScanEntity, newItem: ScanEntity): Boolean {
            return oldItem == newItem
        }
    }
}
