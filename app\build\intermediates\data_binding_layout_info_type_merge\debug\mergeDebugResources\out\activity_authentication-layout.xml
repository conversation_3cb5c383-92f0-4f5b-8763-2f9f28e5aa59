<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_authentication" modulePackage="com.scanner3d.app" filePath="app\src\main\res\layout\activity_authentication.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_authentication_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="159" endOffset="51"/></Target><Target id="@+id/iv_auth_logo" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/tv_auth_title" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="32" endOffset="65"/></Target><Target id="@+id/tv_auth_subtitle" view="TextView"><Expressions/><location startLine="35" startOffset="4" endLine="47" endOffset="66"/></Target><Target id="@+id/ll_auth_buttons" view="LinearLayout"><Expressions/><location startLine="50" startOffset="4" endLine="90" endOffset="18"/></Target><Target id="@+id/btn_use_biometric" view="Button"><Expressions/><location startLine="64" startOffset="8" endLine="75" endOffset="41"/></Target><Target id="@+id/btn_use_pin" view="Button"><Expressions/><location startLine="78" startOffset="8" endLine="88" endOffset="41"/></Target><Target id="@+id/ll_pin_input" view="LinearLayout"><Expressions/><location startLine="93" startOffset="4" endLine="146" endOffset="18"/></Target><Target id="@+id/tv_pin_label" view="TextView"><Expressions/><location startLine="108" startOffset="8" endLine="115" endOffset="46"/></Target><Target id="@+id/et_pin" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="125" startOffset="12" endLine="133" endOffset="41"/></Target><Target id="@+id/btn_submit_pin" view="Button"><Expressions/><location startLine="138" startOffset="8" endLine="144" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="149" startOffset="4" endLine="157" endOffset="51"/></Target></Targets></Layout>