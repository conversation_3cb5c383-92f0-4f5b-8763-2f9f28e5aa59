package com.scanner3d.app.ui.custom

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.view.MotionEvent
import com.scanner3d.app.data.model.PointCloudData
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10
import kotlin.math.*

class PointCloudView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : GLSurfaceView(context, attrs, defStyleAttr) {
    
    private val renderer = PointCloudRenderer()
    private var currentPointCloud: PointCloudData? = null
    
    // Touch handling for rotation
    private var previousX = 0f
    private var previousY = 0f
    private var rotationX = 0f
    private var rotationY = 0f
    private var scale = 1f
    
    init {
        setEGLContextClientVersion(2)
        setRenderer(renderer)
        renderMode = RENDERMODE_WHEN_DIRTY
    }
    
    fun updatePointCloud(pointCloudData: PointCloudData) {
        currentPointCloud = pointCloudData
        renderer.updatePointCloud(pointCloudData)
        requestRender()
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                previousX = x
                previousY = y
            }
            MotionEvent.ACTION_MOVE -> {
                val deltaX = x - previousX
                val deltaY = y - previousY
                
                rotationY += deltaX * 0.5f
                rotationX += deltaY * 0.5f
                
                // Clamp rotation
                rotationX = rotationX.coerceIn(-90f, 90f)
                
                renderer.setRotation(rotationX, rotationY)
                requestRender()
                
                previousX = x
                previousY = y
            }
        }
        return true
    }
    
    private inner class PointCloudRenderer : GLSurfaceView.Renderer {
        
        private var pointCloudData: PointCloudData? = null
        private var rotationX = 0f
        private var rotationY = 0f
        
        override fun onSurfaceCreated(gl: GL10, config: EGLConfig) {
            gl.glClearColor(0.1f, 0.1f, 0.1f, 1.0f)
            gl.glEnable(GL10.GL_DEPTH_TEST)
            gl.glEnable(GL10.GL_POINT_SMOOTH)
            gl.glHint(GL10.GL_POINT_SMOOTH_HINT, GL10.GL_NICEST)
        }
        
        override fun onSurfaceChanged(gl: GL10, width: Int, height: Int) {
            gl.glViewport(0, 0, width, height)
            
            // Set up projection matrix
            gl.glMatrixMode(GL10.GL_PROJECTION)
            gl.glLoadIdentity()
            
            val ratio = width.toFloat() / height.toFloat()
            gl.glFrustumf(-ratio, ratio, -1f, 1f, 1f, 10f)
            
            gl.glMatrixMode(GL10.GL_MODELVIEW)
            gl.glLoadIdentity()
        }
        
        override fun onDrawFrame(gl: GL10) {
            gl.glClear(GL10.GL_COLOR_BUFFER_BIT or GL10.GL_DEPTH_BUFFER_BIT)
            gl.glLoadIdentity()
            
            // Move camera back
            gl.glTranslatef(0f, 0f, -3f)
            
            // Apply rotations
            gl.glRotatef(rotationX, 1f, 0f, 0f)
            gl.glRotatef(rotationY, 0f, 1f, 0f)
            
            // Draw point cloud
            pointCloudData?.let { data ->
                drawPointCloud(gl, data)
            }
            
            // Draw coordinate axes for reference
            drawCoordinateAxes(gl)
        }
        
        private fun drawPointCloud(gl: GL10, data: PointCloudData) {
            gl.glPointSize(3f)
            gl.glEnableClientState(GL10.GL_VERTEX_ARRAY)
            
            // Convert point cloud data to vertex array
            val vertices = FloatArray(data.pointCount * 3)
            for (i in 0 until data.pointCount) {
                vertices[i * 3] = data.points[i * 4]     // x
                vertices[i * 3 + 1] = data.points[i * 4 + 1] // y
                vertices[i * 3 + 2] = data.points[i * 4 + 2] // z
            }
            
            // Set vertex array
            val vertexBuffer = java.nio.ByteBuffer.allocateDirect(vertices.size * 4)
                .order(java.nio.ByteOrder.nativeOrder())
                .asFloatBuffer()
            vertexBuffer.put(vertices)
            vertexBuffer.position(0)
            
            gl.glVertexPointer(3, GL10.GL_FLOAT, 0, vertexBuffer)
            
            // Set point color based on confidence if available
            if (data.confidence != null) {
                drawColoredPoints(gl, data)
            } else {
                gl.glColor4f(0.8f, 0.8f, 1.0f, 1.0f) // Light blue
                gl.glDrawArrays(GL10.GL_POINTS, 0, data.pointCount)
            }
            
            gl.glDisableClientState(GL10.GL_VERTEX_ARRAY)
        }
        
        private fun drawColoredPoints(gl: GL10, data: PointCloudData) {
            // Draw points with colors based on confidence
            gl.glBegin(GL10.GL_POINTS)
            
            for (i in 0 until data.pointCount) {
                val confidence = data.confidence?.get(i) ?: 1f
                
                // Color from red (low confidence) to green (high confidence)
                val red = 1f - confidence
                val green = confidence
                val blue = 0.2f
                
                gl.glColor4f(red, green, blue, 1f)
                gl.glVertex3f(
                    data.points[i * 4],
                    data.points[i * 4 + 1],
                    data.points[i * 4 + 2]
                )
            }
            
            gl.glEnd()
        }
        
        private fun drawCoordinateAxes(gl: GL10) {
            gl.glLineWidth(2f)
            gl.glBegin(GL10.GL_LINES)
            
            // X axis - Red
            gl.glColor4f(1f, 0f, 0f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(0.5f, 0f, 0f)
            
            // Y axis - Green
            gl.glColor4f(0f, 1f, 0f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(0f, 0.5f, 0f)
            
            // Z axis - Blue
            gl.glColor4f(0f, 0f, 1f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(0f, 0f, 0.5f)
            
            gl.glEnd()
        }
        
        fun updatePointCloud(data: PointCloudData) {
            pointCloudData = data
        }
        
        fun setRotation(rotX: Float, rotY: Float) {
            rotationX = rotX
            rotationY = rotY
        }
    }
}
