// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.scanner3d.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemScanListBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Chip chipColors;

  @NonNull
  public final Chip chipTexture;

  @NonNull
  public final Chip chipUploaded;

  @NonNull
  public final ImageView ivThumbnail;

  @NonNull
  public final LinearLayout llInfo;

  @NonNull
  public final LinearLayout llStats;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvQuality;

  @NonNull
  public final TextView tvScanDate;

  @NonNull
  public final TextView tvScanDescription;

  @NonNull
  public final TextView tvScanDuration;

  @NonNull
  public final TextView tvScanName;

  @NonNull
  public final TextView tvTriangleCount;

  @NonNull
  public final TextView tvVertexCount;

  private ItemScanListBinding(@NonNull MaterialCardView rootView, @NonNull Chip chipColors,
      @NonNull Chip chipTexture, @NonNull Chip chipUploaded, @NonNull ImageView ivThumbnail,
      @NonNull LinearLayout llInfo, @NonNull LinearLayout llStats, @NonNull TextView tvFileSize,
      @NonNull TextView tvQuality, @NonNull TextView tvScanDate,
      @NonNull TextView tvScanDescription, @NonNull TextView tvScanDuration,
      @NonNull TextView tvScanName, @NonNull TextView tvTriangleCount,
      @NonNull TextView tvVertexCount) {
    this.rootView = rootView;
    this.chipColors = chipColors;
    this.chipTexture = chipTexture;
    this.chipUploaded = chipUploaded;
    this.ivThumbnail = ivThumbnail;
    this.llInfo = llInfo;
    this.llStats = llStats;
    this.tvFileSize = tvFileSize;
    this.tvQuality = tvQuality;
    this.tvScanDate = tvScanDate;
    this.tvScanDescription = tvScanDescription;
    this.tvScanDuration = tvScanDuration;
    this.tvScanName = tvScanName;
    this.tvTriangleCount = tvTriangleCount;
    this.tvVertexCount = tvVertexCount;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemScanListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemScanListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_scan_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemScanListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chip_colors;
      Chip chipColors = ViewBindings.findChildViewById(rootView, id);
      if (chipColors == null) {
        break missingId;
      }

      id = R.id.chip_texture;
      Chip chipTexture = ViewBindings.findChildViewById(rootView, id);
      if (chipTexture == null) {
        break missingId;
      }

      id = R.id.chip_uploaded;
      Chip chipUploaded = ViewBindings.findChildViewById(rootView, id);
      if (chipUploaded == null) {
        break missingId;
      }

      id = R.id.iv_thumbnail;
      ImageView ivThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (ivThumbnail == null) {
        break missingId;
      }

      id = R.id.ll_info;
      LinearLayout llInfo = ViewBindings.findChildViewById(rootView, id);
      if (llInfo == null) {
        break missingId;
      }

      id = R.id.ll_stats;
      LinearLayout llStats = ViewBindings.findChildViewById(rootView, id);
      if (llStats == null) {
        break missingId;
      }

      id = R.id.tv_file_size;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tv_quality;
      TextView tvQuality = ViewBindings.findChildViewById(rootView, id);
      if (tvQuality == null) {
        break missingId;
      }

      id = R.id.tv_scan_date;
      TextView tvScanDate = ViewBindings.findChildViewById(rootView, id);
      if (tvScanDate == null) {
        break missingId;
      }

      id = R.id.tv_scan_description;
      TextView tvScanDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvScanDescription == null) {
        break missingId;
      }

      id = R.id.tv_scan_duration;
      TextView tvScanDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvScanDuration == null) {
        break missingId;
      }

      id = R.id.tv_scan_name;
      TextView tvScanName = ViewBindings.findChildViewById(rootView, id);
      if (tvScanName == null) {
        break missingId;
      }

      id = R.id.tv_triangle_count;
      TextView tvTriangleCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTriangleCount == null) {
        break missingId;
      }

      id = R.id.tv_vertex_count;
      TextView tvVertexCount = ViewBindings.findChildViewById(rootView, id);
      if (tvVertexCount == null) {
        break missingId;
      }

      return new ItemScanListBinding((MaterialCardView) rootView, chipColors, chipTexture,
          chipUploaded, ivThumbnail, llInfo, llStats, tvFileSize, tvQuality, tvScanDate,
          tvScanDescription, tvScanDuration, tvScanName, tvTriangleCount, tvVertexCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
