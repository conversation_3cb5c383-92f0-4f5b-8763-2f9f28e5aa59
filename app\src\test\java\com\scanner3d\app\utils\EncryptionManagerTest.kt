package com.scanner3d.app.utils

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.io.File

@RunWith(RobolectricTestRunner::class)
class EncryptionManagerTest {
    
    private lateinit var encryptionManager: EncryptionManager
    private lateinit var context: Context
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        encryptionManager = EncryptionManager.getInstance(context)
    }
    
    @Test
    fun `encryptData and decryptData should work correctly`() {
        // Arrange
        val originalData = "This is test data for encryption".toByteArray()
        
        // Act
        val encryptedData = encryptionManager.encryptData(originalData)
        val decryptedData = encryptionManager.decryptData(encryptedData)
        
        // Assert
        assertNotNull("Encrypted data should not be null", encryptedData)
        assertNotNull("Decrypted data should not be null", decryptedData)
        assertArrayEquals("Decrypted data should match original", originalData, decryptedData)
        assertFalse("Encrypted data should be different from original", 
            encryptedData.encryptedBytes.contentEquals(originalData))
    }
    
    @Test
    fun `encryptString and decryptString should work correctly`() {
        // Arrange
        val originalString = "This is a test string for encryption"
        
        // Act
        val encryptedString = encryptionManager.encryptString(originalString)
        val decryptedString = encryptionManager.decryptString(encryptedString)
        
        // Assert
        assertNotNull("Encrypted string should not be null", encryptedString)
        assertNotNull("Decrypted string should not be null", decryptedString)
        assertEquals("Decrypted string should match original", originalString, decryptedString)
        assertNotEquals("Encrypted string should be different from original", 
            originalString, encryptedString)
    }
    
    @Test
    fun `generateSecureHash should create consistent hashes`() {
        // Arrange
        val testData = "Test data for hashing"
        
        // Act
        val hash1 = encryptionManager.generateSecureHash(testData)
        val hash2 = encryptionManager.generateSecureHash(testData)
        
        // Assert
        assertNotNull("Hash should not be null", hash1)
        assertNotNull("Hash should not be null", hash2)
        assertEquals("Same input should produce same hash", hash1, hash2)
        assertNotEquals("Hash should be different from input", testData, hash1)
    }
    
    @Test
    fun `verifyHash should correctly verify data integrity`() {
        // Arrange
        val testData = "Test data for verification".toByteArray()
        val correctHash = encryptionManager.generateSecureHash(testData)
        val incorrectHash = "incorrect_hash"
        
        // Act & Assert
        assertTrue("Should verify correct hash", 
            encryptionManager.verifyHash(testData, correctHash))
        assertFalse("Should not verify incorrect hash", 
            encryptionManager.verifyHash(testData, incorrectHash))
    }
    
    @Test
    fun `encryptScanFile and decryptScanFile should work with files`() {
        // Arrange
        val testContent = "This is test scan file content"
        val inputFile = File.createTempFile("test_input", ".obj")
        val encryptedFile = File.createTempFile("test_encrypted", ".enc")
        val decryptedFile = File.createTempFile("test_decrypted", ".obj")
        
        inputFile.writeText(testContent)
        
        try {
            // Act
            val encryptResult = encryptionManager.encryptScanFile(inputFile, encryptedFile)
            val decryptResult = encryptionManager.decryptScanFile(encryptedFile, decryptedFile)
            
            // Assert
            assertTrue("Encryption should succeed", encryptResult)
            assertTrue("Decryption should succeed", decryptResult)
            assertTrue("Encrypted file should exist", encryptedFile.exists())
            assertTrue("Decrypted file should exist", decryptedFile.exists())
            
            val decryptedContent = decryptedFile.readText()
            assertEquals("Decrypted content should match original", testContent, decryptedContent)
            
            // Encrypted file should be different from original
            val encryptedContent = encryptedFile.readBytes()
            val originalContent = inputFile.readBytes()
            assertFalse("Encrypted content should be different from original",
                encryptedContent.contentEquals(originalContent))
            
        } finally {
            // Cleanup
            inputFile.delete()
            encryptedFile.delete()
            decryptedFile.delete()
        }
    }
    
    @Test
    fun `generateRandomKey should create valid keys`() {
        // Act
        val key1 = encryptionManager.generateRandomKey()
        val key2 = encryptionManager.generateRandomKey()
        
        // Assert
        assertNotNull("Key should not be null", key1)
        assertNotNull("Key should not be null", key2)
        assertEquals("Key algorithm should be AES", "AES", key1.algorithm)
        assertEquals("Key algorithm should be AES", "AES", key2.algorithm)
        assertFalse("Different keys should be generated", 
            key1.encoded.contentEquals(key2.encoded))
    }
    
    @Test
    fun `encryptWithCustomKey and decryptWithCustomKey should work`() {
        // Arrange
        val testData = "Test data for custom key encryption".toByteArray()
        val customKey = encryptionManager.generateRandomKey()
        
        // Act
        val encryptedData = encryptionManager.encryptWithCustomKey(testData, customKey)
        val decryptedData = encryptionManager.decryptWithCustomKey(encryptedData, customKey)
        
        // Assert
        assertNotNull("Encrypted data should not be null", encryptedData)
        assertNotNull("Decrypted data should not be null", decryptedData)
        assertArrayEquals("Decrypted data should match original", testData, decryptedData)
    }
    
    @Test
    fun `keyToString and stringToKey should work correctly`() {
        // Arrange
        val originalKey = encryptionManager.generateRandomKey()
        
        // Act
        val keyString = encryptionManager.keyToString(originalKey)
        val reconstructedKey = encryptionManager.stringToKey(keyString)
        
        // Assert
        assertNotNull("Key string should not be null", keyString)
        assertNotNull("Reconstructed key should not be null", reconstructedKey)
        assertEquals("Key algorithm should match", originalKey.algorithm, reconstructedKey.algorithm)
        assertArrayEquals("Key bytes should match", originalKey.encoded, reconstructedKey.encoded)
    }
    
    @Test
    fun `isKeyStoreAvailable should return true after initialization`() {
        // Act
        val isAvailable = encryptionManager.isKeyStoreAvailable()
        
        // Assert
        assertTrue("KeyStore should be available after initialization", isAvailable)
    }
    
    @Test
    fun `createEncryptedSharedPreferences should create valid preferences`() {
        // Act
        val encryptedPrefs = encryptionManager.createEncryptedSharedPreferences("test_prefs")
        
        // Assert
        assertNotNull("Encrypted preferences should not be null", encryptedPrefs)
        
        // Test basic functionality
        encryptedPrefs.edit().putString("test_key", "test_value").apply()
        val retrievedValue = encryptedPrefs.getString("test_key", null)
        assertEquals("Should retrieve correct value", "test_value", retrievedValue)
    }
    
    @Test
    fun `encryption should be deterministic with same IV`() {
        // Arrange
        val testData = "Test data for deterministic encryption".toByteArray()
        
        // Act
        val encrypted1 = encryptionManager.encryptData(testData)
        val encrypted2 = encryptionManager.encryptData(testData)
        
        // Assert
        // Different encryptions should have different IVs and thus different encrypted data
        assertFalse("Different encryptions should produce different results",
            encrypted1.encryptedBytes.contentEquals(encrypted2.encryptedBytes))
        assertFalse("Different encryptions should have different IVs",
            encrypted1.iv.contentEquals(encrypted2.iv))
    }
    
    @Test
    fun `decryption with wrong key should fail`() {
        // Arrange
        val testData = "Test data for wrong key test".toByteArray()
        val correctKey = encryptionManager.generateRandomKey()
        val wrongKey = encryptionManager.generateRandomKey()
        
        val encryptedData = encryptionManager.encryptWithCustomKey(testData, correctKey)
        
        // Act & Assert
        try {
            encryptionManager.decryptWithCustomKey(encryptedData, wrongKey)
            fail("Decryption with wrong key should fail")
        } catch (e: Exception) {
            // Expected - decryption should fail with wrong key
            assertTrue("Should throw exception for wrong key", true)
        }
    }
}
