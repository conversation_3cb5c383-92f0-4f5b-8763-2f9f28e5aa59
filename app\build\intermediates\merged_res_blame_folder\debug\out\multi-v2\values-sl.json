{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "45,46,47,48,49,50,51,166", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4078,4175,4277,4375,4479,4582,4684,15504", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4170,4272,4370,4474,4577,4679,4796,15600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "76,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "7624,8260,8364,8476", "endColumns": "105,103,111,101", "endOffsets": "7725,8359,8471,8573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6295", "endColumns": "139", "endOffsets": "6430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "883,995,1097,1205,1292,1395,1514,1595,1673,1765,1859,1954,2048,2143,2237,2333,2433,2525,2617,2701,2809,2917,3017,3130,3238,3343,3523,15348", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "990,1092,1200,1287,1390,1509,1590,1668,1760,1854,1949,2043,2138,2232,2328,2428,2520,2612,2696,2804,2912,3012,3125,3233,3338,3518,3618,15427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1353", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1348,1469"}, "to": {"startLines": "55,56,77,79,80,97,98,157,158,159,160,162,163,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5117,5212,7730,7917,8015,9943,10020,14784,14876,14958,15031,15180,15262,15432,15605,15675", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,69,120", "endOffsets": "5207,5288,7821,8010,8095,10015,10102,14871,14953,15026,15098,15257,15343,15499,15670,15791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,253,374,512,642,768,893,1033,1132,1275,1409", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "157,248,369,507,637,763,888,1028,1127,1270,1404,1537"}, "to": {"startLines": "75,78,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7517,7826,8578,8699,8837,8967,9093,9218,9358,9457,9600,9734", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "7619,7912,8694,8832,8962,9088,9213,9353,9452,9595,9729,9862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,120", "endOffsets": "158,279"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "14555,14663", "endColumns": "107,120", "endOffsets": "14658,14779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1204,1298,1374,1437,1549,1609,1674,1728,1798,1858,1914,2026,2083,2145,2201,2274,2408,2493,2578,2721,2805,2888,3022,3111,3188,3244,3299,3365,3438,3515,3599,3678,3752,3828,3903,3976,4081,4169,4242,4332,4423,4495,4569,4660,4712,4794,4861,4945,5032,5094,5158,5221,5290,5393,5501,5599,5703,5763,5822", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1199,1293,1369,1432,1544,1604,1669,1723,1793,1853,1909,2021,2078,2140,2196,2269,2403,2488,2573,2716,2800,2883,3017,3106,3183,3239,3294,3360,3433,3510,3594,3673,3747,3823,3898,3971,4076,4164,4237,4327,4418,4490,4564,4655,4707,4789,4856,4940,5027,5089,5153,5216,5285,5388,5496,5594,5698,5758,5817,5894"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3623,3712,3801,3889,3987,4801,4907,5033,8100,8166,9867,10107,10170,10282,10342,10407,10461,10531,10591,10647,10759,10816,10878,10934,11007,11141,11226,11311,11454,11538,11621,11755,11844,11921,11977,12032,12098,12171,12248,12332,12411,12485,12561,12636,12709,12814,12902,12975,13065,13156,13228,13302,13393,13445,13527,13594,13678,13765,13827,13891,13954,14023,14126,14234,14332,14436,14496,15103", "endLines": "7,40,41,42,43,44,52,53,54,81,82,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "412,3707,3796,3884,3982,4073,4902,5028,5112,8161,8255,9938,10165,10277,10337,10402,10456,10526,10586,10642,10754,10811,10873,10929,11002,11136,11221,11306,11449,11533,11616,11750,11839,11916,11972,12027,12093,12166,12243,12327,12406,12480,12556,12631,12704,12809,12897,12970,13060,13151,13223,13297,13388,13440,13522,13589,13673,13760,13822,13886,13949,14018,14121,14229,14327,14431,14491,14550,15175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,288,419,552", "endColumns": "48,48,130,132,83", "endOffsets": "238,287,418,551,635"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "417,470,523,658,795", "endColumns": "52,52,134,136,87", "endOffsets": "465,518,653,790,878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5293,5399,5564,5693,5800,5947,6077,6191,6435,6593,6700,6867,6997,7149,7301,7371,7433", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "5394,5559,5688,5795,5942,6072,6186,6290,6588,6695,6862,6992,7144,7296,7366,7428,7512"}}]}]}