package com.scanner3d.app

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Observer
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import com.scanner3d.app.core.ScanningEngine
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.PointCloudData
import com.scanner3d.app.data.model.ScanProgress
import com.scanner3d.app.repository.ScanRepository
import com.scanner3d.app.utils.MemoryManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations

@RunWith(AndroidJUnit4::class)
class ScanningIntegrationTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    @get:Rule
    val permissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.CAMERA,
        android.Manifest.permission.RECORD_AUDIO,
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    
    private lateinit var context: Context
    private lateinit var scanningEngine: ScanningEngine
    private lateinit var scanRepository: ScanRepository
    private lateinit var database: Scanner3DDatabase
    private lateinit var memoryManager: MemoryManager
    
    @Mock
    private lateinit var progressObserver: Observer<ScanProgress>
    
    @Mock
    private lateinit var pointCloudObserver: Observer<PointCloudData?>
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        context = ApplicationProvider.getApplicationContext()
        scanningEngine = ScanningEngine(context)
        scanRepository = ScanRepository(context)
        database = Scanner3DDatabase.getDatabase(context)
        memoryManager = MemoryManager.getInstance(context)
    }
    
    @After
    fun tearDown() {
        scanningEngine.cleanup()
        database.close()
        memoryManager.cleanup()
    }
    
    @Test
    fun testFullScanningWorkflow() = runBlocking {
        // This test simulates a complete scanning workflow
        
        // 1. Initialize scanning engine
        val initResult = scanningEngine.initialize(mockLifecycleOwner())
        assertTrue("Scanning engine should initialize successfully", initResult)
        
        // 2. Observe scanning progress
        scanningEngine.scanProgress.observeForever(progressObserver)
        scanningEngine.pointCloudData.observeForever(pointCloudObserver)
        
        try {
            // 3. Start scanning
            scanningEngine.startScanning()
            
            // 4. Wait for scanning to begin
            withTimeout(5000) {
                val isScanning = scanningEngine.isScanning.first { it }
                assertTrue("Scanning should start", isScanning)
            }
            
            // 5. Simulate some scanning time
            kotlinx.coroutines.delay(2000)
            
            // 6. Stop scanning
            scanningEngine.stopScanning()
            
            // 7. Wait for processing to complete
            withTimeout(10000) {
                val progress = scanningEngine.scanProgress.first { it.isComplete }
                assertTrue("Scanning should complete", progress.isComplete)
            }
            
            // 8. Verify scan results
            val finalProgress = scanningEngine.scanProgress.value
            assertNotNull("Final progress should not be null", finalProgress)
            assertTrue("Scan should be complete", finalProgress?.isComplete == true)
            assertFalse("Should not have errors", finalProgress?.hasError == true)
            
        } finally {
            scanningEngine.scanProgress.removeObserver(progressObserver)
            scanningEngine.pointCloudData.removeObserver(pointCloudObserver)
        }
    }
    
    @Test
    fun testScanPauseAndResume() = runBlocking {
        // Test pausing and resuming a scan
        
        val initResult = scanningEngine.initialize(mockLifecycleOwner())
        assertTrue("Scanning engine should initialize", initResult)
        
        try {
            // Start scanning
            scanningEngine.startScanning()
            
            // Wait for scanning to start
            withTimeout(3000) {
                scanningEngine.isScanning.first { it }
            }
            
            // Pause scanning
            scanningEngine.pauseScanning()
            
            // Verify pause state
            withTimeout(2000) {
                val progress = scanningEngine.scanProgress.first { it.isPaused }
                assertTrue("Scan should be paused", progress.isPaused)
            }
            
            // Resume scanning
            scanningEngine.resumeScanning()
            
            // Verify resume state
            withTimeout(2000) {
                val progress = scanningEngine.scanProgress.first { !it.isPaused && it.isActive }
                assertTrue("Scan should be resumed", !progress.isPaused)
                assertTrue("Scan should be active", progress.isActive)
            }
            
            // Stop scanning
            scanningEngine.stopScanning()
            
        } catch (e: Exception) {
            fail("Pause/resume test failed: ${e.message}")
        }
    }
    
    @Test
    fun testMemoryManagementDuringScanning() = runBlocking {
        // Test memory management during scanning process
        
        val initialMemoryInfo = memoryManager.getMemoryInfo()
        assertNotNull("Initial memory info should be available", initialMemoryInfo)
        
        val initResult = scanningEngine.initialize(mockLifecycleOwner())
        assertTrue("Scanning engine should initialize", initResult)
        
        try {
            // Start scanning
            scanningEngine.startScanning()
            
            // Monitor memory usage during scanning
            var memoryWarningTriggered = false
            memoryManager.addMemoryWarningCallback {
                memoryWarningTriggered = true
            }
            
            // Simulate scanning for a period
            kotlinx.coroutines.delay(3000)
            
            // Check memory usage
            val scanningMemoryInfo = memoryManager.getMemoryInfo()
            assertNotNull("Memory info should be available during scanning", scanningMemoryInfo)
            
            // Memory usage should be reasonable
            assertTrue("Memory usage should be within reasonable limits",
                scanningMemoryInfo.memoryUsagePercentage < 90f)
            
            // Stop scanning
            scanningEngine.stopScanning()
            
            // Wait for cleanup
            kotlinx.coroutines.delay(1000)
            
            // Check memory after cleanup
            val finalMemoryInfo = memoryManager.getMemoryInfo()
            assertNotNull("Final memory info should be available", finalMemoryInfo)
            
        } catch (e: Exception) {
            fail("Memory management test failed: ${e.message}")
        }
    }
    
    @Test
    fun testDatabaseIntegration() = runBlocking {
        // Test database operations during scanning workflow
        
        val scanDao = database.scanDao()
        
        // Verify database is empty initially
        val initialScans = scanDao.getAllScans().first()
        assertTrue("Database should be empty initially", initialScans.isEmpty())
        
        // Create a test mesh and save it
        val testMesh = createTestMesh()
        val saveResult = scanRepository.saveScan(testMesh, "Integration Test Scan", "Test scan from integration test")
        
        assertTrue("Scan should be saved successfully", saveResult.isSuccess)
        val scanId = saveResult.getOrNull()
        assertNotNull("Scan ID should be returned", scanId)
        
        // Verify scan was saved to database
        val savedScans = scanDao.getAllScans().first()
        assertEquals("Should have one scan in database", 1, savedScans.size)
        
        val savedScan = savedScans[0]
        assertEquals("Scan ID should match", scanId, savedScan.id)
        assertEquals("Scan name should match", "Integration Test Scan", savedScan.name)
        
        // Test scan retrieval
        val retrievedScan = scanDao.getScanById(scanId!!)
        assertNotNull("Should be able to retrieve scan by ID", retrievedScan)
        assertEquals("Retrieved scan should match saved scan", savedScan, retrievedScan)
        
        // Test scan deletion
        val deleteResult = scanRepository.deleteScan(scanId)
        assertTrue("Scan should be deleted successfully", deleteResult.isSuccess)
        
        // Verify scan was deleted
        val finalScans = scanDao.getAllScans().first()
        assertTrue("Database should be empty after deletion", finalScans.isEmpty())
    }
    
    @Test
    fun testErrorHandling() = runBlocking {
        // Test error handling in various scenarios
        
        try {
            // Test initialization without proper permissions (should be handled gracefully)
            val scanningEngineNoPerms = ScanningEngine(context)
            
            // Test starting scan without initialization
            scanningEngineNoPerms.startScanning()
            
            // Should handle error gracefully
            withTimeout(3000) {
                val progress = scanningEngineNoPerms.scanProgress.first { it.hasError }
                assertTrue("Should report error for uninitialized scanning", progress.hasError)
                assertNotNull("Error message should be provided", progress.errorMessage)
            }
            
            scanningEngineNoPerms.cleanup()
            
        } catch (e: Exception) {
            // Expected - some operations may throw exceptions
            assertTrue("Error handling should work", true)
        }
    }
    
    @Test
    fun testConcurrentOperations() = runBlocking {
        // Test concurrent scanning operations
        
        val initResult = scanningEngine.initialize(mockLifecycleOwner())
        assertTrue("Scanning engine should initialize", initResult)
        
        try {
            // Start scanning
            scanningEngine.startScanning()
            
            // Try to start another scan (should be ignored or handled gracefully)
            scanningEngine.startScanning()
            
            // Verify only one scan is active
            val isScanning = scanningEngine.isScanning.value
            assertTrue("Should have one active scan", isScanning == true)
            
            // Try multiple pause/resume operations
            scanningEngine.pauseScanning()
            scanningEngine.resumeScanning()
            scanningEngine.pauseScanning()
            scanningEngine.resumeScanning()
            
            // Should handle multiple operations gracefully
            val progress = scanningEngine.scanProgress.value
            assertNotNull("Progress should be available", progress)
            assertFalse("Should not have errors from concurrent operations", progress?.hasError == true)
            
            // Stop scanning
            scanningEngine.stopScanning()
            
        } catch (e: Exception) {
            fail("Concurrent operations test failed: ${e.message}")
        }
    }
    
    private fun mockLifecycleOwner(): androidx.lifecycle.LifecycleOwner {
        return object : androidx.lifecycle.LifecycleOwner {
            override val lifecycle: androidx.lifecycle.Lifecycle
                get() = androidx.lifecycle.testing.TestLifecycleOwner().lifecycle
        }
    }
    
    private fun createTestMesh(): com.scanner3d.app.data.model.Mesh3D {
        val vertices = floatArrayOf(
            0f, 0f, 0f,
            1f, 0f, 0f,
            0f, 1f, 0f,
            1f, 1f, 0f
        )
        val indices = intArrayOf(0, 1, 2, 1, 3, 2)
        val boundingBox = com.scanner3d.app.data.model.Mesh3D.BoundingBox(0f, 0f, 0f, 1f, 1f, 0f)
        val metadata = com.scanner3d.app.data.model.Mesh3D.MeshMetadata(
            createdAt = System.currentTimeMillis(),
            scanDuration = 30000L,
            quality = com.scanner3d.app.data.model.Mesh3D.MeshQuality.MEDIUM,
            hasTexture = false,
            hasColors = false,
            estimatedFileSize = 1024L,
            scannerVersion = "1.0"
        )
        
        return com.scanner3d.app.data.model.Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 4,
            triangleCount = 2,
            boundingBox = boundingBox,
            metadata = metadata
        )
    }
}
