{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "34,35,36,37,38,39,40,54", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3208,3304,3406,3505,3604,3708,3810,5452", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "3299,3401,3500,3599,3703,3805,3921,5548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3926,4032,4122,4241,4370,4508,4635,4753,4884,4984,5110,5249", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4027,4117,4236,4365,4503,4630,4748,4879,4979,5105,5244,5364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-en-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,241,292,414,536", "endColumns": "46,50,121,121,83", "endOffsets": "240,291,413,535,619"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,211,337,463", "endColumns": "50,54,125,125,87", "endOffsets": "151,206,332,458,546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "551,655,755,863,947,1047,1162,1240,1315,1406,1499,1594,1688,1788,1881,1976,2070,2161,2252,2334,2437,2540,2639,2744,2848,2952,3108,5369", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "650,750,858,942,1042,1157,1235,1310,1401,1494,1589,1683,1783,1876,1971,2065,2156,2247,2329,2432,2535,2634,2739,2843,2947,3103,3203,5447"}}]}]}