// Generated by view binder compiler. Do not edit!
package com.scanner3d.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.scanner3d.app.R;
import com.scanner3d.app.ui.custom.DepthView;
import com.scanner3d.app.ui.custom.PointCloudView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityScanningBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnCameraView;

  @NonNull
  public final Button btnDepthView;

  @NonNull
  public final Button btnPauseScan;

  @NonNull
  public final Button btnResumeScan;

  @NonNull
  public final ImageButton btnScanSettings;

  @NonNull
  public final Button btnStartScan;

  @NonNull
  public final Button btnStopScan;

  @NonNull
  public final DepthView depthView;

  @NonNull
  public final View indicatorScanning;

  @NonNull
  public final LinearLayout llProcessingStatus;

  @NonNull
  public final LinearLayout llScanControls;

  @NonNull
  public final LinearLayout llScanInfo;

  @NonNull
  public final LinearLayout llTopBar;

  @NonNull
  public final LinearLayout llViewToggle;

  @NonNull
  public final PointCloudView pointCloudView;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final ProgressBar progressBarProcessing;

  @NonNull
  public final ProgressBar progressBarScan;

  @NonNull
  public final TextView tvFrameCount;

  @NonNull
  public final TextView tvPointCount;

  @NonNull
  public final TextView tvProcessingStage;

  @NonNull
  public final TextView tvScanProgress;

  @NonNull
  public final TextView tvScanStatus;

  @NonNull
  public final TextView tvScanTitle;

  @NonNull
  public final TextView tvTimeRemaining;

  private ActivityScanningBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton btnBack,
      @NonNull Button btnCameraView, @NonNull Button btnDepthView, @NonNull Button btnPauseScan,
      @NonNull Button btnResumeScan, @NonNull ImageButton btnScanSettings,
      @NonNull Button btnStartScan, @NonNull Button btnStopScan, @NonNull DepthView depthView,
      @NonNull View indicatorScanning, @NonNull LinearLayout llProcessingStatus,
      @NonNull LinearLayout llScanControls, @NonNull LinearLayout llScanInfo,
      @NonNull LinearLayout llTopBar, @NonNull LinearLayout llViewToggle,
      @NonNull PointCloudView pointCloudView, @NonNull PreviewView previewView,
      @NonNull ProgressBar progressBarProcessing, @NonNull ProgressBar progressBarScan,
      @NonNull TextView tvFrameCount, @NonNull TextView tvPointCount,
      @NonNull TextView tvProcessingStage, @NonNull TextView tvScanProgress,
      @NonNull TextView tvScanStatus, @NonNull TextView tvScanTitle,
      @NonNull TextView tvTimeRemaining) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCameraView = btnCameraView;
    this.btnDepthView = btnDepthView;
    this.btnPauseScan = btnPauseScan;
    this.btnResumeScan = btnResumeScan;
    this.btnScanSettings = btnScanSettings;
    this.btnStartScan = btnStartScan;
    this.btnStopScan = btnStopScan;
    this.depthView = depthView;
    this.indicatorScanning = indicatorScanning;
    this.llProcessingStatus = llProcessingStatus;
    this.llScanControls = llScanControls;
    this.llScanInfo = llScanInfo;
    this.llTopBar = llTopBar;
    this.llViewToggle = llViewToggle;
    this.pointCloudView = pointCloudView;
    this.previewView = previewView;
    this.progressBarProcessing = progressBarProcessing;
    this.progressBarScan = progressBarScan;
    this.tvFrameCount = tvFrameCount;
    this.tvPointCount = tvPointCount;
    this.tvProcessingStage = tvProcessingStage;
    this.tvScanProgress = tvScanProgress;
    this.tvScanStatus = tvScanStatus;
    this.tvScanTitle = tvScanTitle;
    this.tvTimeRemaining = tvTimeRemaining;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityScanningBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityScanningBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_scanning, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityScanningBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_camera_view;
      Button btnCameraView = ViewBindings.findChildViewById(rootView, id);
      if (btnCameraView == null) {
        break missingId;
      }

      id = R.id.btn_depth_view;
      Button btnDepthView = ViewBindings.findChildViewById(rootView, id);
      if (btnDepthView == null) {
        break missingId;
      }

      id = R.id.btn_pause_scan;
      Button btnPauseScan = ViewBindings.findChildViewById(rootView, id);
      if (btnPauseScan == null) {
        break missingId;
      }

      id = R.id.btn_resume_scan;
      Button btnResumeScan = ViewBindings.findChildViewById(rootView, id);
      if (btnResumeScan == null) {
        break missingId;
      }

      id = R.id.btn_scan_settings;
      ImageButton btnScanSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnScanSettings == null) {
        break missingId;
      }

      id = R.id.btn_start_scan;
      Button btnStartScan = ViewBindings.findChildViewById(rootView, id);
      if (btnStartScan == null) {
        break missingId;
      }

      id = R.id.btn_stop_scan;
      Button btnStopScan = ViewBindings.findChildViewById(rootView, id);
      if (btnStopScan == null) {
        break missingId;
      }

      id = R.id.depth_view;
      DepthView depthView = ViewBindings.findChildViewById(rootView, id);
      if (depthView == null) {
        break missingId;
      }

      id = R.id.indicator_scanning;
      View indicatorScanning = ViewBindings.findChildViewById(rootView, id);
      if (indicatorScanning == null) {
        break missingId;
      }

      id = R.id.ll_processing_status;
      LinearLayout llProcessingStatus = ViewBindings.findChildViewById(rootView, id);
      if (llProcessingStatus == null) {
        break missingId;
      }

      id = R.id.ll_scan_controls;
      LinearLayout llScanControls = ViewBindings.findChildViewById(rootView, id);
      if (llScanControls == null) {
        break missingId;
      }

      id = R.id.ll_scan_info;
      LinearLayout llScanInfo = ViewBindings.findChildViewById(rootView, id);
      if (llScanInfo == null) {
        break missingId;
      }

      id = R.id.ll_top_bar;
      LinearLayout llTopBar = ViewBindings.findChildViewById(rootView, id);
      if (llTopBar == null) {
        break missingId;
      }

      id = R.id.ll_view_toggle;
      LinearLayout llViewToggle = ViewBindings.findChildViewById(rootView, id);
      if (llViewToggle == null) {
        break missingId;
      }

      id = R.id.point_cloud_view;
      PointCloudView pointCloudView = ViewBindings.findChildViewById(rootView, id);
      if (pointCloudView == null) {
        break missingId;
      }

      id = R.id.preview_view;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.progress_bar_processing;
      ProgressBar progressBarProcessing = ViewBindings.findChildViewById(rootView, id);
      if (progressBarProcessing == null) {
        break missingId;
      }

      id = R.id.progress_bar_scan;
      ProgressBar progressBarScan = ViewBindings.findChildViewById(rootView, id);
      if (progressBarScan == null) {
        break missingId;
      }

      id = R.id.tv_frame_count;
      TextView tvFrameCount = ViewBindings.findChildViewById(rootView, id);
      if (tvFrameCount == null) {
        break missingId;
      }

      id = R.id.tv_point_count;
      TextView tvPointCount = ViewBindings.findChildViewById(rootView, id);
      if (tvPointCount == null) {
        break missingId;
      }

      id = R.id.tv_processing_stage;
      TextView tvProcessingStage = ViewBindings.findChildViewById(rootView, id);
      if (tvProcessingStage == null) {
        break missingId;
      }

      id = R.id.tv_scan_progress;
      TextView tvScanProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvScanProgress == null) {
        break missingId;
      }

      id = R.id.tv_scan_status;
      TextView tvScanStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvScanStatus == null) {
        break missingId;
      }

      id = R.id.tv_scan_title;
      TextView tvScanTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvScanTitle == null) {
        break missingId;
      }

      id = R.id.tv_time_remaining;
      TextView tvTimeRemaining = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeRemaining == null) {
        break missingId;
      }

      return new ActivityScanningBinding((ConstraintLayout) rootView, btnBack, btnCameraView,
          btnDepthView, btnPauseScan, btnResumeScan, btnScanSettings, btnStartScan, btnStopScan,
          depthView, indicatorScanning, llProcessingStatus, llScanControls, llScanInfo, llTopBar,
          llViewToggle, pointCloudView, previewView, progressBarProcessing, progressBarScan,
          tvFrameCount, tvPointCount, tvProcessingStage, tvScanProgress, tvScanStatus, tvScanTitle,
          tvTimeRemaining);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
