package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.data.model.PointCloudData
import com.scanner3d.app.repository.ScanRepository
import com.scanner3d.app.utils.FileManager
import kotlinx.coroutines.launch
import java.io.File

class ModelViewerViewModel(application: Application) : AndroidViewModel(application) {
    
    private val database = Scanner3DDatabase.getDatabase(application)
    private val scanDao = database.scanDao()
    private val scanRepository = ScanRepository(application)
    private val fileManager = FileManager(application)
    
    private val _scanData = MutableLiveData<ScanEntity?>()
    val scanData: LiveData<ScanEntity?> = _scanData
    
    private val _pointCloudData = MutableLiveData<PointCloudData?>()
    val pointCloudData: LiveData<PointCloudData?> = _pointCloudData
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    fun loadScan(scanId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val scan = scanDao.getScanById(scanId)
                _scanData.value = scan
                
                if (scan == null) {
                    _errorMessage.value = "Scan not found"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load scan: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun loadPointCloudData(filePath: String) {
        viewModelScope.launch {
            try {
                val file = File(filePath)
                if (file.exists()) {
                    // Load point cloud data from file
                    val pointCloud = loadPointCloudFromFile(file)
                    _pointCloudData.value = pointCloud
                } else {
                    _errorMessage.value = "3D model file not found"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load 3D model: ${e.message}"
            }
        }
    }
    
    private suspend fun loadPointCloudFromFile(file: File): PointCloudData? {
        return try {
            when (file.extension.lowercase()) {
                "obj" -> loadObjFile(file)
                "ply" -> loadPlyFile(file)
                "stl" -> loadStlFile(file)
                else -> {
                    _errorMessage.value = "Unsupported file format: ${file.extension}"
                    null
                }
            }
        } catch (e: Exception) {
            _errorMessage.value = "Error parsing 3D file: ${e.message}"
            null
        }
    }
    
    private fun loadObjFile(file: File): PointCloudData {
        val vertices = mutableListOf<Float>()
        val colors = mutableListOf<Float>()
        val confidence = mutableListOf<Float>()
        
        file.bufferedReader().use { reader ->
            reader.lineSequence().forEach { line ->
                val parts = line.trim().split("\\s+".toRegex())
                when (parts.firstOrNull()) {
                    "v" -> {
                        // Vertex: v x y z [r g b]
                        if (parts.size >= 4) {
                            vertices.add(parts[1].toFloat())
                            vertices.add(parts[2].toFloat())
                            vertices.add(parts[3].toFloat())
                            vertices.add(1.0f) // w component
                            
                            // Add colors if available
                            if (parts.size >= 7) {
                                colors.add(parts[4].toFloat())
                                colors.add(parts[5].toFloat())
                                colors.add(parts[6].toFloat())
                                colors.add(1.0f) // alpha
                            } else {
                                // Default white color
                                colors.add(1.0f)
                                colors.add(1.0f)
                                colors.add(1.0f)
                                colors.add(1.0f)
                            }
                            
                            // Default confidence
                            confidence.add(1.0f)
                        }
                    }
                }
            }
        }
        
        return PointCloudData(
            points = vertices.toFloatArray(),
            colors = colors.toFloatArray(),
            confidence = confidence.toFloatArray(),
            pointCount = vertices.size / 4
        )
    }
    
    private fun loadPlyFile(file: File): PointCloudData {
        val vertices = mutableListOf<Float>()
        val colors = mutableListOf<Float>()
        val confidence = mutableListOf<Float>()
        
        var inHeader = true
        var vertexCount = 0
        var hasColors = false
        
        file.bufferedReader().use { reader ->
            reader.lineSequence().forEach { line ->
                val trimmed = line.trim()
                
                if (inHeader) {
                    when {
                        trimmed.startsWith("element vertex") -> {
                            vertexCount = trimmed.split("\\s+".toRegex())[2].toInt()
                        }
                        trimmed.contains("property uchar red") -> {
                            hasColors = true
                        }
                        trimmed == "end_header" -> {
                            inHeader = false
                        }
                    }
                } else {
                    // Parse vertex data
                    val parts = trimmed.split("\\s+".toRegex())
                    if (parts.size >= 3) {
                        vertices.add(parts[0].toFloat())
                        vertices.add(parts[1].toFloat())
                        vertices.add(parts[2].toFloat())
                        vertices.add(1.0f) // w component
                        
                        if (hasColors && parts.size >= 6) {
                            colors.add(parts[3].toFloat() / 255.0f)
                            colors.add(parts[4].toFloat() / 255.0f)
                            colors.add(parts[5].toFloat() / 255.0f)
                            colors.add(1.0f)
                        } else {
                            colors.add(0.8f)
                            colors.add(0.8f)
                            colors.add(0.8f)
                            colors.add(1.0f)
                        }
                        
                        confidence.add(1.0f)
                    }
                }
            }
        }
        
        return PointCloudData(
            points = vertices.toFloatArray(),
            colors = colors.toFloatArray(),
            confidence = confidence.toFloatArray(),
            pointCount = vertices.size / 4
        )
    }
    
    private fun loadStlFile(file: File): PointCloudData {
        // STL files contain triangles, we'll extract vertices
        val vertices = mutableListOf<Float>()
        val colors = mutableListOf<Float>()
        val confidence = mutableListOf<Float>()
        
        file.bufferedReader().use { reader ->
            reader.lineSequence().forEach { line ->
                val trimmed = line.trim()
                if (trimmed.startsWith("vertex")) {
                    val parts = trimmed.split("\\s+".toRegex())
                    if (parts.size >= 4) {
                        vertices.add(parts[1].toFloat())
                        vertices.add(parts[2].toFloat())
                        vertices.add(parts[3].toFloat())
                        vertices.add(1.0f) // w component
                        
                        // Default gray color for STL
                        colors.add(0.7f)
                        colors.add(0.7f)
                        colors.add(0.7f)
                        colors.add(1.0f)
                        
                        confidence.add(1.0f)
                    }
                }
            }
        }
        
        return PointCloudData(
            points = vertices.toFloatArray(),
            colors = colors.toFloatArray(),
            confidence = confidence.toFloatArray(),
            pointCount = vertices.size / 4
        )
    }
    
    fun exportScan(scan: ScanEntity) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = scanRepository.exportScan(scan.id, scan.name, scan.description, scan.format)
                
                result.onSuccess { exportPath ->
                    _errorMessage.value = "Scan exported to: $exportPath"
                }.onFailure { error ->
                    _errorMessage.value = "Export failed: ${error.message}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Export error: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteScan(scanId: String) {
        viewModelScope.launch {
            try {
                scanDao.deleteScanById(scanId)
                _errorMessage.value = "Scan deleted successfully"
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete scan: ${e.message}"
            }
        }
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
}
