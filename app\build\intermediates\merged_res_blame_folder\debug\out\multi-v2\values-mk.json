{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "74,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7489,8136,8241,8356", "endColumns": "112,104,114,100", "endOffsets": "7597,8236,8351,8452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1383", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1378,1499"}, "to": {"startLines": "53,54,75,77,78,95,96,155,156,157,158,160,161,163,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4935,5039,7602,7791,7894,9758,9835,14668,14760,14844,14915,15070,15154,15328,15501,15572", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,70,120", "endOffsets": "5034,5124,7693,7889,7974,9830,9920,14755,14839,14910,14980,15149,15238,15395,15567,15688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,127", "endOffsets": "155,283"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "14435,14540", "endColumns": "104,127", "endOffsets": "14535,14663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,265,386,516,640,769,887,1022,1116,1246,1373", "endColumns": "116,92,120,129,123,128,117,134,93,129,126,122", "endOffsets": "167,260,381,511,635,764,882,1017,1111,1241,1368,1491"}, "to": {"startLines": "73,76,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7372,7698,8457,8578,8708,8832,8961,9079,9214,9308,9438,9565", "endColumns": "116,92,120,129,123,128,117,134,93,129,126,122", "endOffsets": "7484,7786,8573,8703,8827,8956,9074,9209,9303,9433,9560,9683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3909,4007,4109,4206,4304,4409,4512,15400", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "4002,4104,4201,4299,4404,4507,4623,15496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,288,406,526", "endColumns": "46,50,117,119,87", "endOffsets": "236,287,405,525,613"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "321,372,427,549,673", "endColumns": "50,54,121,123,91", "endOffsets": "367,422,544,668,760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6148", "endColumns": "136", "endOffsets": "6280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2908,2995,3072,3126,3181,3247,3320,3396,3485,3563,3636,3712,3787,3857,3966,4054,4129,4221,4313,4387,4461,4553,4606,4688,4755,4838,4925,4987,5051,5114,5184,5298,5413,5515,5627,5685,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2903,2990,3067,3121,3176,3242,3315,3391,3480,3558,3631,3707,3782,3852,3961,4049,4124,4216,4308,4382,4456,4548,4601,4683,4750,4833,4920,4982,5046,5109,5179,5293,5408,5510,5622,5680,5739,5824"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3480,3561,3641,3723,3820,4628,4724,4848,7979,8045,9688,9925,9989,10092,10155,10220,10280,10348,10411,10466,10594,10651,10713,10768,10843,10983,11070,11153,11286,11368,11453,11599,11686,11763,11817,11872,11938,12011,12087,12176,12254,12327,12403,12478,12548,12657,12745,12820,12912,13004,13078,13152,13244,13297,13379,13446,13529,13616,13678,13742,13805,13875,13989,14104,14206,14318,14376,14985", "endLines": "5,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "316,3556,3636,3718,3815,3904,4719,4843,4930,8040,8131,9753,9984,10087,10150,10215,10275,10343,10406,10461,10589,10646,10708,10763,10838,10978,11065,11148,11281,11363,11448,11594,11681,11758,11812,11867,11933,12006,12082,12171,12249,12322,12398,12473,12543,12652,12740,12815,12907,12999,13073,13147,13239,13292,13374,13441,13524,13611,13673,13737,13800,13870,13984,14099,14201,14313,14371,14430,15065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "765,873,977,1085,1171,1279,1398,1482,1563,1654,1747,1843,1937,2037,2130,2225,2321,2412,2503,2590,2696,2802,2903,3010,3122,3226,3382,15243", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "868,972,1080,1166,1274,1393,1477,1558,1649,1742,1838,1932,2032,2125,2220,2316,2407,2498,2585,2691,2797,2898,3005,3117,3221,3377,3475,15323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5129,5236,5397,5530,5640,5785,5918,6038,6285,6442,6549,6715,6848,7001,7160,7229,7293", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "5231,5392,5525,5635,5780,5913,6033,6143,6437,6544,6710,6843,6996,7155,7224,7288,7367"}}]}]}