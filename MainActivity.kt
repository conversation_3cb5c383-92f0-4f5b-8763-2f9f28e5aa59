package com.scanner3d.app

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.scanner3d.app.databinding.ActivityMainBinding
import com.scanner3d.app.ui.auth.AuthenticationActivity
import com.scanner3d.app.ui.gallery.GalleryActivity
import com.scanner3d.app.ui.scanning.ScanningActivity
import com.scanner3d.app.viewmodel.MainViewModel

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
        checkAuthentication()
    }
    
    private fun setupUI() {
        binding.apply {
            btnStartScanning.setOnClickListener {
                startActivity(Intent(this@MainActivity, ScanningActivity::class.java))
            }
            
            btnViewGallery.setOnClickListener {
                startActivity(Intent(this@MainActivity, GalleryActivity::class.java))
            }
            
            btnSettings.setOnClickListener {
                // TODO: Implement settings activity
            }
        }
    }
    
    private fun observeViewModel() {
        viewModel.isAuthenticated.observe(this, Observer { isAuthenticated ->
            if (!isAuthenticated) {
                startAuthenticationActivity()
            }
        })
        
        viewModel.appVersion.observe(this, Observer { version ->
            binding.tvVersion.text = "Version $version"
        })
    }
    
    private fun checkAuthentication() {
        viewModel.checkAuthentication()
    }
    
    private fun startAuthenticationActivity() {
        val intent = Intent(this, AuthenticationActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    override fun onResume() {
        super.onResume()
        // Re-check authentication when returning to the app
        viewModel.checkAuthentication()
    }
}

