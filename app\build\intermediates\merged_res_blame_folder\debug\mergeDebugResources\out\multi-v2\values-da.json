{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa51a49a0fdb8d78736b292657adf1a3\\transformed\\core-1.41.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,289,403,526", "endColumns": "48,49,113,122,85", "endOffsets": "238,288,402,525,611"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "322,375,429,547,674", "endColumns": "52,53,117,126,89", "endOffsets": "370,424,542,669,759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b986efbab3f35a8495809ca7b1ee567d\\transformed\\core-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3825,3921,4023,4120,4218,4325,4434,11355", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3916,4018,4115,4213,4320,4429,4547,11451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e90c754ff7f976aa957bdef8385fc4b1\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "764,864,958,1074,1159,1259,1372,1450,1526,1617,1710,1803,1897,1991,2084,2179,2277,2368,2459,2538,2646,2753,2849,2962,3065,3166,3319,11275", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "859,953,1069,1154,1254,1367,1445,1521,1612,1705,1798,1892,1986,2079,2174,2272,2363,2454,2533,2641,2748,2844,2957,3060,3161,3314,3411,11350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08372b0b4714dee0e9823f116e744e60\\transformed\\navigation-ui-2.7.5\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,108", "endOffsets": "155,264"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "10981,11086", "endColumns": "104,108", "endOffsets": "11081,11190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0060a6d1e9ec36933e7259d41494599\\transformed\\material-1.11.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2836,2922,3000,3056,3107,3173,3241,3315,3404,3479,3551,3629,3699,3772,3876,3960,4037,4125,4214,4288,4361,4446,4495,4573,4639,4719,4802,4864,4928,4991,5060,5168,5271,5372,5471,5531,5586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2831,2917,2995,3051,3102,3168,3236,3310,3399,3474,3546,3624,3694,3767,3871,3955,4032,4120,4209,4283,4356,4441,4490,4568,4634,4714,4797,4859,4923,4986,5055,5163,5266,5367,5466,5526,5581,5661"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3416,3494,3570,3648,3745,4552,4652,4801,5077,5141,6553,6626,6686,6773,6837,6899,6961,7029,7094,7150,7268,7326,7387,7443,7518,7644,7730,7810,7951,8029,8109,8231,8317,8395,8451,8502,8568,8636,8710,8799,8874,8946,9024,9094,9167,9271,9355,9432,9520,9609,9683,9756,9841,9890,9968,10034,10114,10197,10259,10323,10386,10455,10563,10666,10767,10866,10926,11195", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "317,3489,3565,3643,3740,3820,4647,4796,4874,5136,5222,6621,6681,6768,6832,6894,6956,7024,7089,7145,7263,7321,7382,7438,7513,7639,7725,7805,7946,8024,8104,8226,8312,8390,8446,8497,8563,8631,8705,8794,8869,8941,9019,9089,9162,9266,9350,9427,9515,9604,9678,9751,9836,9885,9963,10029,10109,10192,10254,10318,10381,10450,10558,10661,10762,10861,10921,10976,11270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad912a0b6595c0fd27017314ccf5845d\\transformed\\biometric-1.1.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,253,376,517,649,776,912,1055,1154,1303,1453", "endColumns": "109,87,122,140,131,126,135,142,98,148,149,125", "endOffsets": "160,248,371,512,644,771,907,1050,1149,1298,1448,1574"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4879,4989,5227,5350,5491,5623,5750,5886,6029,6128,6277,6427", "endColumns": "109,87,122,140,131,126,135,142,98,148,149,125", "endOffsets": "4984,5072,5345,5486,5618,5745,5881,6024,6123,6272,6422,6548"}}]}]}