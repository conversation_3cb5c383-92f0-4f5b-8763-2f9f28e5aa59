package com.scanner3d.app.repository

import android.content.Context
import androidx.room.Room
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.AppSettings
import kotlinx.coroutines.flow.Flow

class SettingsRepository(private val context: Context) {
    
    private val database by lazy {
        Room.databaseBuilder(
            context.applicationContext,
            Scanner3DDatabase::class.java,
            "scanner3d_database"
        ).build()
    }
    
    private val settingsDao by lazy { database.settingsDao() }
    
    suspend fun getSettings(): AppSettings {
        return settingsDao.getSettings() ?: AppSettings()
    }
    
    fun getSettingsFlow(): Flow<AppSettings?> {
        return settingsDao.getSettingsFlow()
    }
    
    suspend fun saveSettings(settings: AppSettings) {
        settingsDao.insertOrUpdateSettings(settings)
    }
    
    suspend fun resetSettings() {
        val defaultSettings = AppSettings()
        settingsDao.insertOrUpdateSettings(defaultSettings)
    }
    
    suspend fun updateResolution(resolution: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(resolution = resolution))
    }
    
    suspend fun updateScanQuality(quality: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(defaultScanQuality = quality))
    }
    
    suspend fun updateHighQualityMode(enabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(highQualityMode = enabled))
    }
    
    suspend fun updateBiometricEnabled(enabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(biometricEnabled = enabled))
    }
    
    suspend fun updateAutoLockEnabled(enabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(autoLockEnabled = enabled))
    }
    
    suspend fun updateDeveloperMode(enabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(developerMode = enabled))
    }
    
    suspend fun updateDebugLogging(enabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(debugLogging = enabled))
    }
} 