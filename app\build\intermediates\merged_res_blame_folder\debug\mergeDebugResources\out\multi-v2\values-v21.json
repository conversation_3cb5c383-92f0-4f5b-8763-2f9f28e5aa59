{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "24,25,26,27,28,29,30,31,32,33,34,35,36,37,39,41,42,43,44,46,48,49,50,51,52,54,56,58,60,62,64,65,70,72,74,75,76,78,80,81,82,83,88,100,143,146,189,204,216,218,220,222,225,229,232,233,234,237,238,239,240,241,242,245,246,248,250,252,254,258,260,261,262,263,265,269,271,273,274,275,276,277,278,309,310,311,321,322,323,335", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1975,2066,2169,2272,2377,2484,2593,2702,2811,2920,3029,3136,3239,3358,3513,3668,3773,3894,3995,4142,4283,4386,4505,4612,4715,4870,5041,5190,5355,5512,5663,5782,6133,6282,6431,6543,6690,6843,6990,7065,7154,7241,7766,8858,11616,11801,14571,15704,16556,16679,16802,16915,17098,17353,17554,17643,17754,17987,18088,18183,18306,18435,18552,18729,18828,18963,19106,19241,19360,19561,19680,19773,19884,19940,20047,20242,20353,20486,20581,20672,20763,20856,20973,23406,23477,23560,24183,24240,24298,24922", "endLines": "24,25,26,27,28,29,30,31,32,33,34,35,36,38,40,41,42,43,45,47,48,49,50,51,53,55,57,59,61,63,64,69,71,73,74,75,77,79,80,81,82,83,88,142,145,188,191,206,217,219,221,224,228,231,232,233,236,237,238,239,240,241,244,245,247,249,251,253,257,259,260,261,262,264,268,270,272,273,274,275,276,277,279,309,310,320,321,322,334,346", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2061,2164,2267,2372,2479,2588,2697,2806,2915,3024,3131,3234,3353,3508,3663,3768,3889,3990,4137,4278,4381,4500,4607,4710,4865,5036,5185,5350,5507,5658,5777,6128,6277,6426,6538,6685,6838,6985,7060,7149,7236,7337,7864,11611,11796,14566,14763,15898,16674,16797,16910,17093,17348,17549,17638,17749,17982,18083,18178,18301,18430,18547,18724,18823,18958,19101,19236,19355,19556,19675,19768,19879,19935,20042,20237,20348,20481,20576,20667,20758,20851,20968,21107,23472,23555,24178,24235,24293,24917,25553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1386,1476,1572,1662,1755,1862,1967,2086,2211,2332,2545,2804,3075,3293,3525,3761,4011,4224,4433,4664,4865,4981,5151,5472,6501,6958,7462,7970,8479,8993,9498,10002,10507,11013,11515,12021,12530,13038,13537,14044,14552,14844,15138,15438,15738,16067,16408,16546,16690,16846,17239,17457,17679,17905,18121,18231,18401,18591,18832,19091", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1381,1471,1567,1657,1750,1857,1962,2081,2206,2327,2540,2799,3070,3288,3520,3756,4006,4219,4428,4659,4860,4976,5146,5467,6496,6953,7457,7965,8474,8988,9493,9997,10502,11008,11510,12016,12525,13033,13532,14039,14547,14839,15133,15433,15733,16062,16403,16541,16685,16841,17234,17452,17674,17900,18116,18226,18396,18586,18827,19086,19263"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,84,85,86,87,89,90,91,94,97,192,195,198,201,207,210,213,280,283,284,287,292,303,351,360,369,378,387,396,405,414,423,432,441,450,459,468,477,486,492,498,504,510,514,518,519,520,521,525,528,531,534,545,546,549,552,556,560", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,1603,1699,1789,1885,7342,7435,7542,7647,7869,7994,8115,8328,8587,14768,14986,15218,15454,15903,16116,16325,21112,21313,21429,21599,21920,22949,26054,26558,27066,27575,28089,28594,29098,29603,30109,30611,31117,31626,32134,32633,33140,33648,33940,34234,34534,34834,35163,35504,35642,35786,35942,36335,36553,36775,37001,37741,37851,38021,38211,38452,38711", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,84,85,86,87,89,90,93,96,99,194,197,200,203,209,212,215,282,283,286,291,302,308,359,368,377,386,395,404,413,422,431,440,449,458,467,476,485,491,497,503,509,513,517,518,519,520,524,527,530,533,536,545,548,551,555,559,562", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,1694,1784,1880,1970,7430,7537,7642,7761,7989,8110,8323,8582,8853,14981,15213,15449,15699,16111,16320,16551,21308,21424,21594,21915,22944,23401,26553,27061,27570,28084,28589,29093,29598,30104,30606,31112,31621,32129,32628,33135,33643,33935,34229,34529,34829,35158,35499,35637,35781,35937,36330,36548,36770,36996,37212,37846,38016,38206,38447,38706,38883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,347,348,349,350,537,540", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25558,25674,25800,25926,37217,37389", "endLines": "2,17,18,19,347,348,349,350,539,544", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25669,25795,25921,26049,37384,37736"}}]}]}