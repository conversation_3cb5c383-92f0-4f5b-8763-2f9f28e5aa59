{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-68:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c351a29c398c623fa23a89619fe48\\transformed\\material-1.11.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,3010,3103,3182,3237,3288,3354,3433,3514,3605,3685,3757,3835,3910,3982,4093,4190,4267,4365,4463,4541,4622,4722,4779,4863,4929,5012,5099,5161,5225,5288,5364,5466,5573,5670,5776,5835,5890", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,3005,3098,3177,3232,3283,3349,3428,3509,3600,3680,3752,3830,3905,3977,4088,4185,4262,4360,4458,4536,4617,4717,4774,4858,4924,5007,5094,5156,5220,5283,5359,5461,5568,5665,5771,5830,5885,5974"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3573,3680,3788,3870,3971,4791,4891,5013,5308,5374,6894,6974,7036,7128,7195,7269,7330,7409,7473,7527,7643,7702,7764,7818,7900,8029,8121,8205,8349,8428,8509,8656,8749,8828,8883,8934,9000,9079,9160,9251,9331,9403,9481,9556,9628,9739,9836,9913,10011,10109,10187,10268,10368,10425,10509,10575,10658,10745,10807,10871,10934,11010,11112,11219,11316,11422,11481,11775", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "328,3675,3783,3865,3966,4063,4886,5008,5093,5369,5466,6969,7031,7123,7190,7264,7325,7404,7468,7522,7638,7697,7759,7813,7895,8024,8116,8200,8344,8423,8504,8651,8744,8823,8878,8929,8995,9074,9155,9246,9326,9398,9476,9551,9623,9734,9831,9908,10006,10104,10182,10263,10363,10420,10504,10570,10653,10740,10802,10866,10929,11005,11107,11214,11311,11417,11476,11531,11859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8587d926d346ccf2176f046e22a2a17\\transformed\\core-1.41.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,242,294,424,561", "endColumns": "47,51,129,136,102", "endOffsets": "241,293,423,560,663"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "333,385,441,575,716", "endColumns": "51,55,133,140,106", "endOffsets": "380,436,570,711,818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\70befc9c16dd9e301c153021ec73a9fd\\transformed\\core-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4068,4166,4268,4367,4469,4573,4677,11951", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4161,4263,4362,4464,4568,4672,4786,12047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fae1aef513bb18ebfc860e4ba437b55\\transformed\\biometric-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,265,386,549,703,837,968,1148,1251,1385,1548", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "168,260,381,544,698,832,963,1143,1246,1380,1543,1683"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5098,5216,5471,5592,5755,5909,6043,6174,6354,6457,6591,6754", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "5211,5303,5587,5750,5904,6038,6169,6349,6452,6586,6749,6889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f5f3850a7c512d8f41852114da6284cb\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,934,1041,1151,1238,1344,1474,1559,1639,1730,1823,1921,2016,2116,2209,2302,2397,2488,2579,2665,2775,2886,2989,3100,3208,3315,3474,11864", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "929,1036,1146,1233,1339,1469,1554,1634,1725,1818,1916,2011,2111,2204,2297,2392,2483,2574,2660,2770,2881,2984,3095,3203,3310,3469,3568,11946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc343c3e9bd9f0c6efa95dea879d2fd0\\transformed\\navigation-ui-2.7.5\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11536,11649", "endColumns": "112,125", "endOffsets": "11644,11770"}}]}]}