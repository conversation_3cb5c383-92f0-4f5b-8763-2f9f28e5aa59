<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.scanning.ScanningActivity">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/ll_scan_controls"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_top_bar"
        app:scaleType="fillCenter" />

    <!-- Depth View -->
    <com.scanner3d.app.ui.custom.DepthView
        android:id="@+id/depth_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/ll_scan_controls"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_top_bar" />

    <!-- Point Cloud Visualization -->
    <com.scanner3d.app.ui.custom.PointCloudView
        android:id="@+id/point_cloud_view"
        android:layout_width="200dp"
        android:layout_height="150dp"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_background"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_top_bar" />

    <!-- Top Bar -->
    <LinearLayout
        android:id="@+id/ll_top_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/camera_overlay"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/white" />

        <!-- Title and Status -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_scan_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scanning_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:id="@+id/indicator_scanning"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/circle_indicator"
                    android:backgroundTint="@color/success_green"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_scan_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ready"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Settings Button -->
        <ImageButton
            android:id="@+id/btn_scan_settings"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/settings"
            android:src="@drawable/ic_settings"
            android:tint="@color/white" />

    </LinearLayout>

    <!-- View Toggle Buttons -->
    <LinearLayout
        android:id="@+id/ll_view_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_background"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_top_bar">

        <Button
            android:id="@+id/btn_camera_view"
            style="@style/Scanner3D.Button.Secondary"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginEnd="4dp"
            android:minWidth="80dp"
            android:text="@string/camera_view"
            android:textAllCaps="false"
            android:textSize="12sp" />

        <Button
            android:id="@+id/btn_depth_view"
            style="@style/Scanner3D.Button.Secondary"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:minWidth="80dp"
            android:text="@string/depth_view"
            android:textAllCaps="false"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Scan Progress Info -->
    <LinearLayout
        android:id="@+id/ll_scan_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_background"
        android:orientation="vertical"
        android:padding="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_view_toggle">

        <TextView
            android:id="@+id/tv_scan_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/scan_progress"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_frame_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Frames: 0"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_point_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Points: 0"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_time_remaining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Est. time: --"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Processing Status -->
    <LinearLayout
        android:id="@+id/ll_processing_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_background"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/point_cloud_view">

        <TextView
            android:id="@+id/tv_processing_stage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Processing..."
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ProgressBar
            android:id="@+id/progress_bar_processing"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:indeterminate="true"
            android:progressTint="@color/primary_blue" />

    </LinearLayout>

    <!-- Scan Controls -->
    <LinearLayout
        android:id="@+id/ll_scan_controls"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/camera_overlay"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar_scan"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="16dp"
            android:max="100"
            android:progress="0"
            android:progressTint="@color/scan_progress" />

        <!-- Control Buttons Row -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <!-- Start Scan Button -->
            <Button
                android:id="@+id/btn_start_scan"
                style="@style/Scanner3D.Button.Primary"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:drawableTop="@drawable/ic_play_arrow"
                android:drawablePadding="8dp"
                android:text="@string/start_scan"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <!-- Pause Scan Button -->
            <Button
                android:id="@+id/btn_pause_scan"
                style="@style/Scanner3D.Button.Secondary"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:drawableTop="@drawable/ic_pause"
                android:drawablePadding="8dp"
                android:text="@string/pause_scan"
                android:textAllCaps="false"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- Resume Scan Button -->
            <Button
                android:id="@+id/btn_resume_scan"
                style="@style/Scanner3D.Button.Primary"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:drawableTop="@drawable/ic_play_arrow"
                android:drawablePadding="8dp"
                android:text="@string/resume_scan"
                android:textAllCaps="false"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- Stop Scan Button -->
            <Button
                android:id="@+id/btn_stop_scan"
                style="@style/Scanner3D.Button.Danger"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/ic_stop"
                android:drawablePadding="8dp"
                android:enabled="false"
                android:text="@string/stop_scan"
                android:textAllCaps="false"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

