package com.scanner3d.app.ui.scanning

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.scanner3d.app.R
import com.scanner3d.app.databinding.ActivityScanningBinding
import com.scanner3d.app.ui.model.ModelViewerActivity
import com.scanner3d.app.viewmodel.ScanningViewModel

class ScanningActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "ScanningActivity"
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )
    }
    
    private lateinit var binding: ActivityScanningBinding
    private val viewModel: ScanningViewModel by viewModels()
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            initializeScanning()
        } else {
            Toast.makeText(this, "Camera permission is required for scanning", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        try {
            super.onCreate(savedInstanceState)
            Log.d(TAG, "onCreate started")

            binding = ActivityScanningBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.d(TAG, "Binding and layout set successfully")

            setupUI()
            Log.d(TAG, "UI setup completed")

            observeViewModel()
            Log.d(TAG, "ViewModel observation setup completed")

            if (allPermissionsGranted()) {
                initializeScanning()
            } else {
                requestPermissionLauncher.launch(REQUIRED_PERMISSIONS)
            }
            Log.d(TAG, "onCreate completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate", e)
            Toast.makeText(this, "Error initializing activity: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun setupUI() {
        binding.apply {
            // Control buttons
            btnStartScan.setOnClickListener {
                viewModel.startScanning()
            }
            
            btnStopScan.setOnClickListener {
                viewModel.stopScanning()
            }
            
            btnPauseScan.setOnClickListener {
                viewModel.pauseScanning()
            }
            
            btnResumeScan.setOnClickListener {
                viewModel.resumeScanning()
            }
            
            // View toggle buttons
            btnCameraView.setOnClickListener {
                showCameraView()
            }
            
            btnDepthView.setOnClickListener {
                showDepthView()
            }
            
            // Settings button
            try {
                Log.d(TAG, "Setting up settings button")
                if (::binding.isInitialized) {
                    btnScanSettings.setOnClickListener {
                        try {
                            Log.d(TAG, "Settings button clicked")
                            showScanSettings()
                        } catch (e: Exception) {
                            Log.e(TAG, "Error in settings button click", e)
                            Toast.makeText(this@ScanningActivity, "Settings error: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                    Log.d(TAG, "Settings button setup completed")
                } else {
                    Log.e(TAG, "Binding not initialized when setting up settings button")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up settings button", e)
            }
            
            // Back button
            btnBack.setOnClickListener {
                onBackPressed()
            }
            
            // Initialize view state
            showCameraView()
        }
    }
    
    private fun observeViewModel() {
        viewModel.scanProgress.observe(this, Observer { progress ->
            updateScanProgress(progress)
        })
        
        viewModel.pointCloudData.observe(this, Observer { pointCloud ->
            updatePointCloudVisualization(pointCloud)
        })
        
        viewModel.isScanning.observe(this, Observer { isScanning ->
            updateScanningState(isScanning)
        })
        
        viewModel.errorMessage.observe(this, Observer { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                viewModel.clearError()
            }
        })
        
        viewModel.scanComplete.observe(this, Observer { isComplete ->
            if (isComplete) {
                navigateToModelViewer()
            }
        })
    }
    
    private fun initializeScanning() {
        viewModel.initializeScanning(this, binding.previewView)
    }
    
    private fun updateScanProgress(progress: com.scanner3d.app.data.model.ScanProgress) {
        binding.apply {
            // Update progress bar
            progressBarScan.progress = progress.progressPercentage
            tvScanProgress.text = getString(R.string.scan_progress, progress.progressPercentage)
            
            // Update processing stage
            if (progress.isProcessing) {
                tvProcessingStage.text = progress.processingStage
                tvProcessingStage.visibility = View.VISIBLE
                progressBarProcessing.visibility = View.VISIBLE
            } else {
                tvProcessingStage.visibility = View.GONE
                progressBarProcessing.visibility = View.GONE
            }
            
            // Update button states
            btnStartScan.isEnabled = !progress.isInProgress
            btnStopScan.isEnabled = progress.canStop
            btnPauseScan.isEnabled = progress.canPause && !progress.isPaused
            btnResumeScan.isEnabled = progress.canResume && progress.isPaused
            
            // Show/hide pause/resume buttons
            btnPauseScan.visibility = if (progress.canPause && !progress.isPaused) View.VISIBLE else View.GONE
            btnResumeScan.visibility = if (progress.canResume && progress.isPaused) View.VISIBLE else View.GONE
            
            // Update frame count
            tvFrameCount.text = "Frames: ${progress.frameCount}"
            
            // Show estimated time remaining
            if (progress.estimatedTimeRemaining > 0) {
                val seconds = progress.estimatedTimeRemaining / 1000
                tvTimeRemaining.text = "Est. time: ${seconds}s"
                tvTimeRemaining.visibility = View.VISIBLE
            } else {
                tvTimeRemaining.visibility = View.GONE
            }
        }
    }
    
    private fun updatePointCloudVisualization(pointCloud: com.scanner3d.app.data.model.PointCloudData?) {
        pointCloud?.let {
            binding.apply {
                tvPointCount.text = "Points: ${it.pointCount}"
                
                // Update 3D visualization (this would be implemented with OpenGL)
                // For now, we'll just show the point count
                pointCloudView.updatePointCloud(it)
            }
        }
    }
    
    private fun updateScanningState(isScanning: Boolean) {
        binding.apply {
            if (isScanning) {
                tvScanStatus.text = "Scanning..."
                tvScanStatus.setTextColor(ContextCompat.getColor(this@ScanningActivity, R.color.success_green))
                indicatorScanning.visibility = View.VISIBLE
            } else {
                tvScanStatus.text = "Ready"
                tvScanStatus.setTextColor(ContextCompat.getColor(this@ScanningActivity, R.color.text_primary_light))
                indicatorScanning.visibility = View.GONE
            }
        }
    }
    
    private fun showCameraView() {
        binding.apply {
            previewView.visibility = View.VISIBLE
            depthView.visibility = View.GONE
            
            btnCameraView.isSelected = true
            btnDepthView.isSelected = false
        }
    }
    
    private fun showDepthView() {
        binding.apply {
            previewView.visibility = View.GONE
            depthView.visibility = View.VISIBLE
            
            btnCameraView.isSelected = false
            btnDepthView.isSelected = true
        }
    }
    
    private fun showScanSettings() {
        try {
            Log.d(TAG, "Showing scan settings")
            // TODO: Implement scan settings dialog
            Toast.makeText(this, "Scan settings coming soon", Toast.LENGTH_SHORT).show()
            Log.d(TAG, "Scan settings toast shown successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error showing scan settings", e)
            // Fallback - just log the error without showing another toast to avoid crash loops
        }
    }
    
    private fun navigateToModelViewer() {
        val intent = Intent(this, ModelViewerActivity::class.java)
        intent.putExtra("scan_id", viewModel.getCurrentScanId())
        startActivity(intent)
        finish()
    }
    
    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }
    
    override fun onResume() {
        super.onResume()
        viewModel.resumeCamera()
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.pauseCamera()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        viewModel.cleanup()
    }
    
    override fun onBackPressed() {
        if (viewModel.isScanning.value == true) {
            // Show confirmation dialog before leaving during scanning
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Stop Scanning?")
                .setMessage("Are you sure you want to stop the current scan? Progress will be lost.")
                .setPositiveButton("Stop") { _, _ ->
                    viewModel.stopScanning()
                    super.onBackPressed()
                }
                .setNegativeButton("Continue Scanning", null)
                .show()
        } else {
            super.onBackPressed()
        }
    }
}
