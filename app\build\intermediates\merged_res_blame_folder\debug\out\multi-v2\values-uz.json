{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-88:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f125d22f5b7a30c1ca1fc138bb19f94\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,955,1050,1150,1232,1332,1449,1534,1612,1703,1796,1891,1985,2079,2172,2267,2362,2453,2545,2629,2739,2845,2945,3053,3159,3261,3422,15365", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "950,1045,1145,1227,1327,1444,1529,1607,1698,1791,1886,1980,2074,2167,2262,2357,2448,2540,2624,2734,2840,2940,3048,3154,3256,3417,3516,15444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89d562fe715b9b51755a21e777da3575\\transformed\\ui-1.3.3\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1386", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1381,1498"}, "to": {"startLines": "53,54,75,77,78,95,96,155,156,157,158,160,161,163,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4984,5083,7641,7835,7942,9920,10003,14807,14900,14983,15051,15201,15282,15449,15624,15692", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,67,116", "endOffsets": "5078,5163,7740,7937,8033,9998,10088,14895,14978,15046,15113,15277,15360,15518,15687,15804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df20b26819e36dfa5eaf28349d99f1f8\\transformed\\biometric-1.1.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "73,76,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7409,7745,8530,8650,8781,8909,9056,9188,9333,9430,9569,9709", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "7518,7830,8645,8776,8904,9051,9183,9328,9425,9564,9704,9845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3406e717720b5f6099835249ae8be0b\\transformed\\play-services-base-18.0.1\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5168,5273,5423,5552,5661,5806,5939,6059,6315,6487,6595,6754,6886,7040,7202,7268,7329", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "5268,5418,5547,5656,5801,5934,6054,6160,6482,6590,6749,6881,7035,7197,7263,7324,7404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19c64b63b5985308cc35feeafae41b5b\\transformed\\play-services-basement-18.1.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6165", "endColumns": "149", "endOffsets": "6310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\602bee39a0b171ae84c113fedb57ac61\\transformed\\navigation-ui-2.7.5\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,121", "endOffsets": "151,273"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "14584,14685", "endColumns": "100,121", "endOffsets": "14680,14802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc3141e738914980a5d47f9dcd7d1340\\transformed\\browser-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "74,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7523,8205,8314,8424", "endColumns": "117,108,109,105", "endOffsets": "7636,8309,8419,8525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00b39c9b4a875310eccf763762cac5b0\\transformed\\material-1.11.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3521,3598,3678,3761,3855,4678,4773,4900,8038,8102,9850,10093,10160,10269,10332,10399,10458,10532,10595,10649,10764,10822,10884,10938,11013,11142,11232,11321,11462,11544,11626,11765,11851,11935,11995,12046,12112,12185,12263,12349,12430,12502,12579,12654,12725,12826,12920,12999,13095,13189,13263,13339,13425,13478,13565,13631,13716,13807,13869,13933,13996,14065,14167,14268,14364,14465,14529,15118", "endLines": "5,38,39,40,41,42,50,51,52,79,80,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "328,3593,3673,3756,3850,3937,4768,4895,4979,8097,8200,9915,10155,10264,10327,10394,10453,10527,10590,10644,10759,10817,10879,10933,11008,11137,11227,11316,11457,11539,11621,11760,11846,11930,11990,12041,12107,12180,12258,12344,12425,12497,12574,12649,12720,12821,12915,12994,13090,13184,13258,13334,13420,13473,13560,13626,13711,13802,13864,13928,13991,14060,14162,14263,14359,14460,14524,14579,15196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b19d232e81648a4661fee435f9a34af1\\transformed\\core-1.41.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,243,297,443,596", "endColumns": "52,53,145,152,90", "endOffsets": "242,296,442,595,686"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "333,390,448,598,755", "endColumns": "56,57,149,156,94", "endOffsets": "385,443,593,750,845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d2a741c98e34e3b57b614e0f8c97bc7\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3942,4044,4146,4247,4347,4455,4559,15523", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "4039,4141,4242,4342,4450,4554,4673,15619"}}]}]}