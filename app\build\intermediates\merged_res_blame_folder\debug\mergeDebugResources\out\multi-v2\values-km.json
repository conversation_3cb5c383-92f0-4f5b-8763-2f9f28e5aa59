{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0060a6d1e9ec36933e7259d41494599\\transformed\\material-1.11.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2795,2892,2970,3025,3081,3147,3227,3317,3403,3488,3567,3644,3714,3789,3901,3989,4062,4162,4261,4335,4411,4518,4572,4662,4735,4826,4922,4984,5048,5111,5182,5281,5379,5471,5567,5625,5685", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2790,2887,2965,3020,3076,3142,3222,3312,3398,3483,3562,3639,3709,3784,3896,3984,4057,4157,4256,4330,4406,4513,4567,4657,4730,4821,4917,4979,5043,5106,5177,5276,5374,5466,5562,5620,5680,5763"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3456,3534,3610,3690,3769,4569,4669,4781,5072,5137,6576,6646,6708,6795,6858,6923,6982,7047,7108,7165,7284,7342,7403,7460,7531,7661,7747,7825,7963,8038,8109,8259,8356,8434,8489,8545,8611,8691,8781,8867,8952,9031,9108,9178,9253,9365,9453,9526,9626,9725,9799,9875,9982,10036,10126,10199,10290,10386,10448,10512,10575,10646,10745,10843,10935,11031,11089,11359", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "314,3529,3605,3685,3764,3843,4664,4776,4856,5132,5226,6641,6703,6790,6853,6918,6977,7042,7103,7160,7279,7337,7398,7455,7526,7656,7742,7820,7958,8033,8104,8254,8351,8429,8484,8540,8606,8686,8776,8862,8947,9026,9103,9173,9248,9360,9448,9521,9621,9720,9794,9870,9977,10031,10121,10194,10285,10381,10443,10507,10570,10641,10740,10838,10930,11026,11084,11144,11437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa51a49a0fdb8d78736b292657adf1a3\\transformed\\core-1.41.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,284,406,530", "endColumns": "46,46,121,123,88", "endOffsets": "236,283,405,529,618"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "319,370,421,547,675", "endColumns": "50,50,125,127,92", "endOffsets": "365,416,542,670,763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e90c754ff7f976aa957bdef8385fc4b1\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,870,969,1079,1166,1269,1390,1468,1544,1635,1728,1820,1914,2014,2107,2202,2296,2387,2478,2561,2665,2769,2869,2978,3087,3196,3358,11442", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "865,964,1074,1161,1264,1385,1463,1539,1630,1723,1815,1909,2009,2102,2197,2291,2382,2473,2556,2660,2764,2864,2973,3082,3191,3353,3451,11521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b986efbab3f35a8495809ca7b1ee567d\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3848,3943,4046,4144,4244,4345,4457,11526", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3938,4041,4139,4239,4340,4452,4564,11622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad912a0b6595c0fd27017314ccf5845d\\transformed\\biometric-1.1.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,266,385,517,651,810,940,1098,1202,1342,1481", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "160,261,380,512,646,805,935,1093,1197,1337,1476,1606"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4861,4971,5231,5350,5482,5616,5775,5905,6063,6167,6307,6446", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "4966,5067,5345,5477,5611,5770,5900,6058,6162,6302,6441,6571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08372b0b4714dee0e9823f116e744e60\\transformed\\navigation-ui-2.7.5\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,104", "endOffsets": "155,260"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11149,11254", "endColumns": "104,104", "endOffsets": "11249,11354"}}]}]}