package com.scanner3d.app.repository

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.scanner3d.app.data.database.Scanner3DDatabase
import com.scanner3d.app.data.model.Mesh3D
import com.scanner3d.app.data.model.ScanEntity
import com.scanner3d.app.utils.FileManager
import com.scanner3d.app.utils.MeshExporter
import com.scanner3d.app.utils.ThumbnailGenerator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class ScanRepository(private val context: Context) {
    
    companion object {
        private const val TAG = "ScanRepository"
    }
    
    private val database = Scanner3DDatabase.getDatabase(context)
    private val scanDao = database.scanDao()
    private val fileManager = FileManager(context)
    private val meshExporter = MeshExporter(context)
    private val thumbnailGenerator = ThumbnailGenerator()
    
    fun getAllScans(): Flow<List<ScanEntity>> {
        return scanDao.getAllScans()
    }
    
    fun getScanById(scanId: String): Flow<ScanEntity?> {
        return scanDao.getScanByIdLiveData(scanId).asFlow()
    }
    
    fun searchScans(query: String): Flow<List<ScanEntity>> {
        return scanDao.searchScans("%$query%")
    }
    
    suspend fun saveScan(
        mesh: Mesh3D,
        name: String,
        description: String? = null,
        format: String = "OBJ"
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val scanId = UUID.randomUUID().toString()
            
            // Save mesh to file
            val meshFile = fileManager.createScanFile(scanId, format)
            val success = meshExporter.exportMesh(mesh, meshFile, format)
            
            if (!success) {
                return@withContext Result.failure(Exception("Failed to export mesh"))
            }
            
            // Generate thumbnail
            val thumbnailFile = fileManager.createThumbnailFile(scanId)
            val thumbnail = thumbnailGenerator.generateThumbnail(mesh)
            fileManager.saveBitmap(thumbnail, thumbnailFile)
            
            // Create scan entity
            val scanEntity = ScanEntity(
                id = scanId,
                name = name,
                description = description,
                createdAt = System.currentTimeMillis(),
                modifiedAt = System.currentTimeMillis(),
                scanDuration = mesh.metadata.scanDuration,
                filePath = meshFile.absolutePath,
                thumbnailPath = thumbnailFile.absolutePath,
                fileSize = meshFile.length(),
                format = format,
                quality = mesh.metadata.quality.name,
                vertexCount = mesh.vertexCount,
                triangleCount = mesh.triangleCount,
                hasTexture = mesh.metadata.hasTexture,
                hasColors = mesh.metadata.hasColors,
                boundingBoxMinX = mesh.boundingBox.minX,
                boundingBoxMinY = mesh.boundingBox.minY,
                boundingBoxMinZ = mesh.boundingBox.minZ,
                boundingBoxMaxX = mesh.boundingBox.maxX,
                boundingBoxMaxY = mesh.boundingBox.maxY,
                boundingBoxMaxZ = mesh.boundingBox.maxZ
            )
            
            // Save to database
            scanDao.insertScan(scanEntity)
            
            Log.d(TAG, "Scan saved successfully: $scanId")
            Result.success(scanId)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save scan", e)
            Result.failure(e)
        }
    }
    
    suspend fun deleteScan(scanId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val scan = scanDao.getScanById(scanId)
            if (scan != null) {
                // Delete files
                fileManager.deleteScanFiles(scan)
                
                // Delete from database
                scanDao.deleteScanById(scanId)
                
                Log.d(TAG, "Scan deleted successfully: $scanId")
                Result.success(Unit)
            } else {
                Result.failure(Exception("Scan not found"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete scan", e)
            Result.failure(e)
        }
    }
    
    suspend fun updateScanMetadata(
        scanId: String,
        name: String,
        description: String?
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            scanDao.updateScanMetadata(
                scanId = scanId,
                name = name,
                description = description,
                modifiedAt = System.currentTimeMillis()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update scan metadata", e)
            Result.failure(e)
        }
    }
    
    suspend fun exportScan(
        scanId: String,
        targetFormat: String,
        outputFile: File
    ): Result<File> = withContext(Dispatchers.IO) {
        try {
            val scan = scanDao.getScanById(scanId)
                ?: return@withContext Result.failure(Exception("Scan not found"))
            
            // Load mesh from file
            val mesh = meshExporter.importMesh(File(scan.filePath), scan.format)
                ?: return@withContext Result.failure(Exception("Failed to load mesh"))
            
            // Export to target format
            val success = meshExporter.exportMesh(mesh, outputFile, targetFormat)
            
            if (success) {
                Result.success(outputFile)
            } else {
                Result.failure(Exception("Export failed"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export scan", e)
            Result.failure(e)
        }
    }
    
    suspend fun getScanStats(): ScanStats = withContext(Dispatchers.IO) {
        try {
            val totalScans = scanDao.getScanCount()
            val totalSize = scanDao.getTotalFileSize()
            val averageDuration = scanDao.getAverageScanDuration()
            
            ScanStats(
                totalScans = totalScans,
                totalSize = totalSize,
                averageScanDuration = averageDuration.toLong()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get scan stats", e)
            ScanStats(0, 0, 0)
        }
    }
    
    suspend fun cleanupOldScans(retentionDays: Int): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val cutoffDate = System.currentTimeMillis() - (retentionDays * 24 * 60 * 60 * 1000L)
            
            // Get scans to delete
            val scansToDelete = scanDao.getAllScans().first().filter { it.createdAt < cutoffDate }
            
            // Delete files and database entries
            var deletedCount = 0
            scansToDelete.forEach { scan ->
                try {
                    fileManager.deleteScanFiles(scan)
                    scanDao.deleteScan(scan)
                    deletedCount++
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to delete scan ${scan.id}", e)
                }
            }
            
            Log.d(TAG, "Cleaned up $deletedCount old scans")
            Result.success(deletedCount)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old scans", e)
            Result.failure(e)
        }
    }
    
    data class ScanStats(
        val totalScans: Int,
        val totalSize: Long,
        val averageScanDuration: Long
    )
}
