{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eba1fadeac71389c08443fad8408d732\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "879,999,1102,1218,1304,1409,1528,1608,1685,1777,1871,1966,2060,2155,2249,2345,2440,2532,2624,2705,2811,2916,3014,3122,3228,3336,3509,11731", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "994,1097,1213,1299,1404,1523,1603,1680,1772,1866,1961,2055,2150,2244,2340,2435,2527,2619,2700,2806,2911,3009,3117,3223,3331,3504,3604,11808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0797190b21212baeb7d2979587e3aa46\\transformed\\biometric-1.1.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,261,391,542,681,810,937,1088,1187,1329,1481", "endColumns": "111,93,129,150,138,128,126,150,98,141,151,126", "endOffsets": "162,256,386,537,676,805,932,1083,1182,1324,1476,1603"}, "to": {"startLines": "55,56,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5077,5189,5443,5573,5724,5863,5992,6119,6270,6369,6511,6663", "endColumns": "111,93,129,150,138,128,126,150,98,141,151,126", "endOffsets": "5184,5278,5568,5719,5858,5987,6114,6265,6364,6506,6658,6785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558d40362b31612b3ec89decd760abb0\\transformed\\core-1.41.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,288,408,531", "endColumns": "49,47,119,122,85", "endOffsets": "239,287,407,530,616"}, "to": {"startLines": "8,9,10,11,12", "startColumns": "4,4,4,4,4", "startOffsets": "432,486,538,662,789", "endColumns": "53,51,123,126,89", "endOffsets": "481,533,657,784,874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36866d4b2dcf3202b3505f64db5ac044\\transformed\\navigation-ui-2.7.5\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,125", "endOffsets": "159,285"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11416,11525", "endColumns": "108,125", "endOffsets": "11520,11646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e7bc507de6eea8b94b2b424380ec10ff\\transformed\\material-1.11.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3609,3686,3763,3845,3942,4765,4862,4994,5283,5350,6790,6867,6930,7046,7109,7178,7237,7308,7367,7421,7542,7603,7666,7720,7793,7915,8003,8086,8238,8324,8411,8544,8635,8718,8775,8826,8892,8964,9041,9125,9208,9283,9360,9442,9518,9626,9715,9797,9888,9984,10058,10139,10234,10288,10370,10436,10523,10609,10671,10735,10798,10867,10977,11090,11193,11300,11361,11651", "endLines": "7,40,41,42,43,44,52,53,54,57,58,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "427,3681,3758,3840,3937,4029,4857,4989,5072,5345,5438,6862,6925,7041,7104,7173,7232,7303,7362,7416,7537,7598,7661,7715,7788,7910,7998,8081,8233,8319,8406,8539,8630,8713,8770,8821,8887,8959,9036,9120,9203,9278,9355,9437,9513,9621,9710,9792,9883,9979,10053,10134,10229,10283,10365,10431,10518,10604,10666,10730,10793,10862,10972,11085,11188,11295,11356,11411,11726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ae3dccc5aff18b6b52c6a8ac40de27a\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "45,46,47,48,49,50,51,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4034,4132,4234,4334,4435,4541,4644,11813", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "4127,4229,4329,4430,4536,4639,4760,11909"}}]}]}