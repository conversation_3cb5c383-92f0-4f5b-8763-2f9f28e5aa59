{"logs": [{"outputFile": "com.scanner3d.app-mergeDebugResources-63:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a75ad7db577b573e5bbedca2fd3129\\transformed\\core-1.41.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,243,295,413,539", "endColumns": "48,51,117,125,86", "endOffsets": "242,294,412,538,625"}, "to": {"startLines": "6,7,8,9,10", "startColumns": "4,4,4,4,4", "startOffsets": "323,376,432,554,684", "endColumns": "52,55,121,129,90", "endOffsets": "371,427,549,679,770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c6ee81e1874838655af13a25ed58d23d\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,895,1001,1108,1197,1298,1417,1502,1582,1673,1766,1861,1955,2055,2148,2243,2338,2429,2520,2605,2712,2823,2925,3033,3141,3251,3413,11558", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "890,996,1103,1192,1293,1412,1497,1577,1668,1761,1856,1950,2050,2143,2238,2333,2424,2515,2600,2707,2818,2920,3028,3136,3246,3408,3508,11639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dcd3e061114e6fadefc732524b779acb\\transformed\\biometric-1.1.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "53,54,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4977,5081,5331,5448,5587,5734,5865,5995,6139,6240,6374,6518", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "5076,5170,5443,5582,5729,5860,5990,6134,6235,6369,6513,6636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\857ae526a5ee6c76b63616ddc978cbae\\transformed\\navigation-ui-2.7.5\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "11249,11361", "endColumns": "111,119", "endOffsets": "11356,11476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c834369ca5e6a96a53c1c6f4fcc9f7bd\\transformed\\core-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "43,44,45,46,47,48,49,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3940,4037,4139,4238,4338,4445,4555,11644", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "4032,4134,4233,4333,4440,4550,4670,11740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d650b516ccc5b69f06f13cc896d11129\\transformed\\material-1.11.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3513,3594,3672,3756,3851,4675,4776,4896,5175,5239,6641,6720,6780,6870,6934,7005,7068,7143,7207,7261,7388,7446,7508,7562,7641,7782,7869,7951,8090,8173,8257,8396,8483,8563,8619,8670,8736,8810,8890,8977,9060,9133,9210,9279,9353,9455,9543,9620,9713,9809,9883,9963,10060,10112,10196,10262,10349,10437,10499,10563,10626,10694,10806,10917,11024,11134,11194,11481", "endLines": "5,38,39,40,41,42,50,51,52,55,56,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3589,3667,3751,3846,3935,4771,4891,4972,5234,5326,6715,6775,6865,6929,7000,7063,7138,7202,7256,7383,7441,7503,7557,7636,7777,7864,7946,8085,8168,8252,8391,8478,8558,8614,8665,8731,8805,8885,8972,9055,9128,9205,9274,9348,9450,9538,9615,9708,9804,9878,9958,10055,10107,10191,10257,10344,10432,10494,10558,10621,10689,10801,10912,11019,11129,11189,11244,11553"}}]}]}